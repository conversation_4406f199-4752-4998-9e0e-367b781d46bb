{"name": "baja-backend", "version": "1.0.0", "description": "BAJA Event Organizer Backend API", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:api": "node test-api.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "mysql2": "^3.6.5", "nodemailer": "^6.9.7", "sequelize": "^6.35.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "keywords": ["express", "typescript", "api", "baja", "event-organizer"], "author": "BAJA Team", "license": "MIT"}