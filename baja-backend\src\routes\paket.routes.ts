import { Router } from 'express';
import { authenticate, authorize, optionalAuth } from '../middleware/auth.middleware';
import {
  getAllPaket,
  getPaketById,
  createPaket,
  updatePaket,
  deletePaket,
  changePaketStatus,
  getActivePaket,
  uploadPackageImage,
  upload
} from '../controllers/paket.controller';

const router = Router();

// Public routes
router.get('/active', getActivePaket);
router.get('/', optionalAuth, getAllPaket);
router.get('/:id', optionalAuth, getPaketById);

// Protected routes
router.use(authenticate);

router.post('/', authorize('admin'), createPaket);
router.put('/:id', authorize('admin'), updatePaket);
router.delete('/:id', authorize('admin'), deletePaket);
router.patch('/:id/status', authorize('admin'), changePaketStatus);
router.post('/:id/upload', authorize('admin'), upload.single('image'), uploadPackageImage);

export default router;
