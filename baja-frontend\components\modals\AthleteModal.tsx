'use client';

import React, { useState, useEffect } from 'react';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import { api } from '@/lib/api';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ImageUpload from '@/components/ui/ImageUpload';
import FileUpload from '@/components/ui/FileUpload';
import { useAuth } from '@/contexts/AuthContext';

interface AthleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  athlete?: {
    id: number;
    nik: string;
    name: string;
    no_hp?: string;
    tanggal_lahir: string;
    jenis_kelamin: 'M' | 'F';
    agama?: string;
    alamat?: string;
    umur: number;
    berat_badan: string;
    tinggi_badan: string;
    foto?: string;
  } | null;
}

const AthleteModal: React.FC<AthleteModalProps> = ({ isOpen, onClose, onSuccess, athlete }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    nik: '',
    name: '',
    no_hp: '',
    tanggal_lahir: '',
    jenis_kelamin: 'M' as 'M' | 'F',
    agama: '',
    alamat: '',
    umur: '',
    berat_badan: '',
    tinggi_badan: '',
    id_kontingen: '',
  });
  const [kontingen, setKontingen] = useState<Array<{id: number, name: string}>>([]);
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<{[key: string]: File}>({});
  const [uploadedFiles, setUploadedFiles] = useState<{[key: string]: boolean}>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (athlete) {
      setFormData({
        nik: athlete.nik,
        name: athlete.name,
        no_hp: athlete.no_hp || '',
        tanggal_lahir: athlete.tanggal_lahir.split('T')[0], // Format for date input
        jenis_kelamin: athlete.jenis_kelamin,
        agama: athlete.agama || '',
        alamat: athlete.alamat || '',
        umur: athlete.umur.toString(),
        berat_badan: athlete.berat_badan,
        tinggi_badan: athlete.tinggi_badan,
        id_kontingen: '',
      });
    } else {
      setFormData({
        nik: '',
        name: '',
        no_hp: '',
        tanggal_lahir: '',
        jenis_kelamin: 'M',
        agama: '',
        alamat: '',
        umur: '',
        berat_badan: '',
        tinggi_badan: '',
        id_kontingen: '',
      });
    }
    setSelectedImages([]);
    setSelectedFiles({});
    setUploadedFiles({});
    setError('');
  }, [athlete, isOpen]);

  // Fetch kontingen list for admin users
  useEffect(() => {
    if (isOpen && user?.role === 'admin') {
      fetchKontingen();
    }
  }, [isOpen, user]);

  const fetchKontingen = async () => {
    try {
      const response = await api.get('/kontingen');
      if (response.data.success) {
        setKontingen(response.data.data.kontingen || []);
      }
    } catch (error) {
      console.error('Error fetching kontingen:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Prepare data object
      const submitData: any = {
        nik: formData.nik,
        name: formData.name,
        no_hp: formData.no_hp,
        tanggal_lahir: formData.tanggal_lahir,
        jenis_kelamin: formData.jenis_kelamin,
        agama: formData.agama,
        alamat: formData.alamat,
        umur: parseInt(formData.umur),
        berat_badan: parseFloat(formData.berat_badan),
        tinggi_badan: parseFloat(formData.tinggi_badan)
      };

      // Only include id_kontingen for admin users
      if (user?.role === 'admin' && formData.id_kontingen) {
        submitData.id_kontingen = parseInt(formData.id_kontingen);
      }

      let response;
      if (athlete) {
        response = await api.put(`/atlet/${athlete.id}`, submitData);
      } else {
        response = await api.post('/atlet', submitData);
      }

      if (response.data.success) {
        const atletId = response.data.data.id;

        // Upload photo if selected
        if (selectedImages.length > 0) {
          const photoFormData = new FormData();
          photoFormData.append('photo', selectedImages[0]);

          try {
            await api.post(`/atlet/${atletId}/upload`, photoFormData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            });
          } catch (photoError) {
            console.error('Error uploading photo:', photoError);
            // Don't fail the whole operation if photo upload fails
          }
        }

        // Upload files if selected
        for (const [fileType, file] of Object.entries(selectedFiles)) {
          try {
            const fileFormData = new FormData();
            fileFormData.append('file', file);
            fileFormData.append('fileType', fileType);

            await api.post(`/atlet/${atletId}/upload-file`, fileFormData, {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            });

            setUploadedFiles(prev => ({ ...prev, [fileType]: true }));
          } catch (fileError) {
            console.error(`Error uploading ${fileType}:`, fileError);
            // Don't fail the whole operation if file upload fails
          }
        }

        onSuccess();
        onClose();
      } else {
        setError(response.data.message || 'Failed to save athlete');
      }
    } catch (error: any) {
      console.error('Error saving athlete:', error);
      setError(error.response?.data?.message || 'Failed to save athlete');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateAge = (birthDate: string) => {
    if (!birthDate) return '';
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age.toString();
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setFormData(prev => ({
      ...prev,
      tanggal_lahir: value,
      umur: calculateAge(value)
    }));
  };

  const handleFileSelect = (file: File, fileType: string) => {
    setSelectedFiles(prev => ({
      ...prev,
      [fileType]: file
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={athlete ? 'Edit Athlete' : 'Add Athlete'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="nik" className="block text-sm font-medium text-gray-300 mb-2">
              NIK *
            </label>
            <Input
              id="nik"
              name="nik"
              type="text"
              value={formData.nik}
              onChange={handleChange}
              placeholder="Enter NIK"
              required
              disabled={loading}
              maxLength={16}
            />
          </div>

          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
              Full Name *
            </label>
            <Input
              id="name"
              name="name"
              type="text"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter full name"
              required
              disabled={loading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="tanggal_lahir" className="block text-sm font-medium text-gray-300 mb-2">
              Birth Date *
            </label>
            <Input
              id="tanggal_lahir"
              name="tanggal_lahir"
              type="date"
              value={formData.tanggal_lahir}
              onChange={handleDateChange}
              required
              disabled={loading}
            />
          </div>

          <div>
            <label htmlFor="umur" className="block text-sm font-medium text-gray-300 mb-2">
              Age
            </label>
            <Input
              id="umur"
              name="umur"
              type="number"
              value={formData.umur}
              onChange={handleChange}
              placeholder="Age (auto-calculated)"
              disabled={true}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="jenis_kelamin" className="block text-sm font-medium text-gray-300 mb-2">
              Gender *
            </label>
            <select
              id="jenis_kelamin"
              name="jenis_kelamin"
              value={formData.jenis_kelamin}
              onChange={handleChange}
              required
              disabled={loading}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
            >
              <option value="M">Male</option>
              <option value="F">Female</option>
            </select>
          </div>

          <div>
            <label htmlFor="no_hp" className="block text-sm font-medium text-gray-300 mb-2">
              Phone Number
            </label>
            <Input
              id="no_hp"
              name="no_hp"
              type="tel"
              value={formData.no_hp}
              onChange={handleChange}
              placeholder="Enter phone number"
              disabled={loading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="tinggi_badan" className="block text-sm font-medium text-gray-300 mb-2">
              Height (cm) *
            </label>
            <Input
              id="tinggi_badan"
              name="tinggi_badan"
              type="number"
              value={formData.tinggi_badan}
              onChange={handleChange}
              placeholder="Enter height in cm"
              required
              disabled={loading}
            />
          </div>

          <div>
            <label htmlFor="berat_badan" className="block text-sm font-medium text-gray-300 mb-2">
              Weight (kg) *
            </label>
            <Input
              id="berat_badan"
              name="berat_badan"
              type="number"
              value={formData.berat_badan}
              onChange={handleChange}
              placeholder="Enter weight in kg"
              required
              disabled={loading}
            />
          </div>
        </div>

        {/* Kontingen selection for admin users */}
        {user?.role === 'admin' && (
          <div>
            <label htmlFor="id_kontingen" className="block text-sm font-medium text-gray-300 mb-2">
              Kontingen *
            </label>
            <select
              id="id_kontingen"
              name="id_kontingen"
              value={formData.id_kontingen}
              onChange={handleChange}
              required
              disabled={loading}
              className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent"
            >
              <option value="">Select Kontingen</option>
              {kontingen.map((k) => (
                <option key={k.id} value={k.id}>
                  {k.name}
                </option>
              ))}
            </select>
          </div>
        )}

        <div>
          <label htmlFor="agama" className="block text-sm font-medium text-gray-300 mb-2">
            Religion
          </label>
          <Input
            id="agama"
            name="agama"
            type="text"
            value={formData.agama}
            onChange={handleChange}
            placeholder="Enter religion"
            disabled={loading}
          />
        </div>

        <div>
          <label htmlFor="alamat" className="block text-sm font-medium text-gray-300 mb-2">
            Address
          </label>
          <textarea
            id="alamat"
            name="alamat"
            value={formData.alamat}
            onChange={handleChange}
            placeholder="Enter address"
            disabled={loading}
            rows={3}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-gold-500 focus:border-transparent resize-none"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Photo
          </label>
          <ImageUpload
            onImagesSelected={setSelectedImages}
            maxImages={1}
            currentImages={athlete?.foto ? [athlete.foto] : []}
          />
        </div>

        {/* Required Documents */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-white border-b border-gray-600 pb-2">
            Required Documents
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FileUpload
              label="Report Card (Rapor)"
              fileType="rapor"
              onFileSelect={handleFileSelect}
              uploaded={uploadedFiles.rapor}
              loading={loading}
              disabled={loading}
            />

            <FileUpload
              label="ID Card / Family Card (KK/KTP)"
              fileType="kk_ktp"
              onFileSelect={handleFileSelect}
              uploaded={uploadedFiles.kk_ktp}
              loading={loading}
              disabled={loading}
            />
          </div>

          <FileUpload
            label="Health Certificate (Surat Kesehatan)"
            fileType="surat_kesehatan"
            onFileSelect={handleFileSelect}
            uploaded={uploadedFiles.surat_kesehatan}
            loading={loading}
            disabled={loading}
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="bg-gold-500 hover:bg-gold-600 text-black"
            disabled={loading}
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                {athlete ? 'Updating...' : 'Adding...'}
              </>
            ) : (
              athlete ? 'Update Athlete' : 'Add Athlete'
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AthleteModal;
