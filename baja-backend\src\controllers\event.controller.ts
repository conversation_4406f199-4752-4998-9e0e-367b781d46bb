import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import Event from '../models/Event';
import User from '../models/User';
import PendaftaranEvent from '../models/PendaftaranEvent';
import Kontingen from '../models/Kontingen';
import { Op } from 'sequelize';
import { eventUpload, documentUpload, deleteFromCloudinary, getOptimizedUrl, extractPublicId } from '../services/cloudinary.service';

export const upload = eventUpload;

export const getAllEvents = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, search = '', status = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    const user = req.user;

    const whereClause: any = {};

    // Filter berdasarkan role (hanya jika user login)
    if (user && user.role === 'admin-event') {
      whereClause.id_user = user.id;
    }

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { lokasi: { [Op.like]: `%${search}%` } }
      ];
    }

    if (status) {
      whereClause.status = status;
    }

    const { count, rows } = await Event.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'eventUser',
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Events retrieved successfully',
      data: {
        events: rows,
        pagination: {
          total: count,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get all events error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getEventById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    const whereClause: any = { id };

    // Admin-event hanya bisa melihat event mereka sendiri (hanya jika user login)
    if (user && user.role === 'admin-event') {
      whereClause.id_user = user.id;
    }

    const event = await Event.findOne({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'eventUser',
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    if (!event) {
      res.status(404).json({
        success: false,
        message: 'Event not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Event retrieved successfully',
      data: event
    });
  } catch (error) {
    console.error('Get event by id error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const createEvent = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const {
      name,
      description,
      start_date,
      end_date,
      lokasi,
      biaya_registrasi,
      metode_pembayaran
    } = req.body;

    // Hanya admin dan admin-event yang bisa membuat event
    if (user.role === 'ketua-kontingen') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin and admin-event can create events.'
      });
      return;
    }

    const event = await Event.create({
      id_user: user.id,
      name,
      description,
      start_date,
      end_date,
      lokasi,
      biaya_registrasi,
      metode_pembayaran,
      status: 'active'
    });

    res.status(201).json({
      success: true,
      message: 'Event created successfully',
      data: event
    });
  } catch (error) {
    console.error('Create event error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updateEvent = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Admin-event hanya bisa update event mereka sendiri
    if (user.role === 'admin-event') {
      whereClause.id_user = user.id;
    }

    const event = await Event.findOne({ where: whereClause });
    if (!event) {
      res.status(404).json({
        success: false,
        message: 'Event not found'
      });
      return;
    }

    await event.update(req.body);

    res.json({
      success: true,
      message: 'Event updated successfully',
      data: event
    });
  } catch (error) {
    console.error('Update event error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deleteEvent = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Admin-event hanya bisa delete event mereka sendiri
    if (user.role === 'admin-event') {
      whereClause.id_user = user.id;
    }

    const event = await Event.findOne({ where: whereClause });
    if (!event) {
      res.status(404).json({
        success: false,
        message: 'Event not found'
      });
      return;
    }

    await event.destroy();

    res.json({
      success: true,
      message: 'Event deleted successfully'
    });
  } catch (error) {
    console.error('Delete event error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const uploadEventFile = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { fileType } = req.body; // 'gambar' or 'proposal'
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Admin-event hanya bisa upload ke event mereka sendiri
    if (user.role === 'admin-event') {
      whereClause.id_user = user.id;
    }

    const event = await Event.findOne({ where: whereClause });
    if (!event) {
      res.status(404).json({
        success: false,
        message: 'Event not found'
      });
      return;
    }

    if (!req.file) {
      res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
      return;
    }

    const updateData: any = {};
    if (fileType === 'gambar') {
      updateData.event_image = req.file.path; // Cloudinary URL

      // Delete old image from Cloudinary if exists
      if (event.event_image) {
        const publicId = extractPublicId(event.event_image);
        await deleteFromCloudinary(publicId);
      }
    } else if (fileType === 'proposal') {
      updateData.event_proposal = req.file.path; // Cloudinary URL

      // Delete old document from Cloudinary if exists
      if (event.event_proposal) {
        const publicId = extractPublicId(event.event_proposal);
        await deleteFromCloudinary(publicId);
      }
    }

    await event.update(updateData);

    res.json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        filename: req.file.filename,
        url: req.file.path,
        optimizedUrl: getOptimizedUrl(req.file.filename)
      }
    });
  } catch (error) {
    console.error('Upload event file error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Admin-specific event management
export const getAllEventsAdmin = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, search = '', status = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);

    const whereClause: any = {};

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
        { lokasi: { [Op.like]: `%${search}%` } }
      ];
    }

    if (status) {
      whereClause.status = status;
    }

    const { count, rows } = await Event.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'eventUser',
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Events retrieved successfully',
      data: {
        events: rows,
        pagination: {
          total: count,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get events admin error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
