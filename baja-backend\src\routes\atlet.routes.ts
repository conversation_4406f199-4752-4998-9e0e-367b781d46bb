import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth.middleware';
import {
  getAllAtlet,
  getAtletById,
  createAtlet,
  updateAtlet,
  deleteAtlet,
  verifyAtlet,
  uploadAtletPhoto,
  upload
} from '../controllers/atlet.controller';

const router = Router();

// All routes require authentication
router.use(authenticate);

router.get('/', getAllAtlet);
router.post('/', authorize('admin', 'ketua-kontingen'), createAtlet);
router.get('/:id', getAtletById);
router.put('/:id', authorize('admin', 'ketua-kontingen'), updateAtlet);
router.delete('/:id', authorize('admin', 'ketua-kontingen'), deleteAtlet);
router.patch('/:id/verify', authorize('admin', 'admin-event'), verifyAtlet);
router.post('/:id/upload', authorize('admin', 'ketua-kontingen'), upload.single('photo'), uploadAtletPhoto);

export default router;
