export interface User {
  id: number;
  profile?: string;
  name: string;
  email: string;
  no_hp?: string;
  alamat?: string;
  agama?: string;
  password: string;
  role: 'admin' | 'admin-event' | 'ketua-kontingen' | 'user';
  status: '0' | '1';
  created_at: Date;
  updated_at: Date;
}

export interface Event {
  id: number;
  name: string;
  description?: string;
  start_date: Date;
  end_date: Date;
  lokasi: string;
  biaya_registrasi: number;
  metode_pembayaran: string;
  status: 'active' | 'completed';
  event_image?: string;
  event_proposal?: string;
  event_pemenang?: string;
  id_user: number;
  created_at: Date;
  updated_at: Date;
}

export interface Atlet {
  id: number;
  nik: string;
  name: string;
  no_hp?: string;
  tanggal_lahir: Date;
  jenis_kelamin: 'M' | 'F';
  agama?: string;
  alamat?: string;
  umur: number;
  berat_badan: string;
  tinggi_badan: string;
  status_verifikasi: 'pending' | 'verified';
  foto?: string;
  id_user: number;
  id_kontingen: number;
  created_at: Date;
  updated_at: Date;
}

export interface Kontingen {
  id: number;
  name: string;
  negara: string;
  provinsi: string;
  kabupaten_kota: string;
  id_user: number;
  created_at: Date;
  updated_at: Date;
}

export interface Official {
  id: number;
  profile?: string;
  name: string;
  no_hp: string;
  alamat: string;
  agama: string;
  jenis_kelamin: 'M' | 'F';
  id_kontingen: number;
  created_at: Date;
  updated_at: Date;
}

export interface Paket {
  id: number;
  name: string;
  images?: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Gallery {
  id: number;
  images: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

export interface PendaftaranEvent {
  id: number;
  id_event: number;
  id_kontingen: number;
  created_at: Date;
  updated_at: Date;
}

export interface PendaftaranAtlet {
  id: number;
  jenis_tanding: string;
  kategori_umur?: string;
  kelas_tanding?: string;
  status: 'pending' | 'oncheck' | 'paid';
  bukti_pembayaran?: string;
  id_pendaftaran_event: number;
  created_at: Date;
  updated_at: Date;
}

export interface PendaftaranAtletDetail {
  id: number;
  id_pendaftaran_atlet: number;
  id_atlet: number;
  created_at: Date;
  updated_at: Date;
}

export interface AtletFile {
  id: number;
  file: string;
  file_type?: 'rapor' | 'kk_ktp' | 'surat_kesehatan';
  id_atlet: number;
  created_at: Date;
  updated_at: Date;
}

export interface Negara {
  id: number;
  name: string;
}

export interface Provinsi {
  id: number;
  name: string;
  id_negara: number;
}

export interface KabupatenKota {
  id: number;
  name: string;
  id_provinsi: number;
}

export interface AuthRequest extends Request {
  user?: User;
}

export interface JWTPayload {
  id: number;
  email: string;
  role: string;
  event_id?: number | undefined;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface DashboardStats {
  count_event: number;
  count_kontingen: number;
  count_atlet: number;
  count_official: number;
}
