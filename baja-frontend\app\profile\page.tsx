'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { User } from '@/types';
import { getRoleDisplayName } from '@/lib/utils';

const ProfilePage = () => {
  const { user, updateUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<Partial<User>>({
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
      no_hp: user?.no_hp || '',
      alamat: user?.alamat || '',
      agama: user?.agama || '',
    },
  });

  const {
    register: registerPassword,
    handleSubmit: handlePasswordSubmit,
    formState: { errors: passwordErrors },
    reset: resetPassword,
    watch,
  } = useForm<{
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }>();

  const newPassword = watch('newPassword');

  const onSubmitProfile = async (data: Partial<User>) => {
    try {
      setLoading(true);
      await updateUser(data);
      reset(data);
    } catch (error) {
      // Error handled by AuthContext
    } finally {
      setLoading(false);
    }
  };

  const onSubmitPassword = async (data: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) => {
    try {
      setLoading(true);
      // TODO: Implement password change API
      console.log('Change password:', data);
      resetPassword();
    } catch (error) {
      // Error handling
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'profile', name: 'Informasi Profil' },
    { id: 'password', name: 'Ubah Password' },
  ];

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-2xl font-bold text-white">Profil <span className="text-gold-400">Saya</span></h1>
            <p className="text-gray-300">Kelola informasi profil dan keamanan akun Anda</p>
          </div>

          {/* Profile Card */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-6">
                <div className="h-20 w-20 rounded-full bg-gold-500/20 border border-gold-500/30 flex items-center justify-center">
                  <span className="text-2xl font-bold text-gold-400">
                    {user?.name?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">{user?.name}</h2>
                  <p className="text-gray-300">{user?.email}</p>
                  <p className="text-sm text-gold-400">{getRoleDisplayName(user?.role || '')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabs */}
          <div className="border-b border-gold-500/30">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-300 ${
                    activeTab === tab.id
                      ? 'border-gold-500 text-gold-400'
                      : 'border-transparent text-gray-400 hover:text-white hover:border-gold-500/50'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'profile' && (
            <Card>
              <CardHeader>
                <CardTitle>Informasi Profil</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit(onSubmitProfile)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                      label="Nama Lengkap"
                      {...register('name', {
                        required: 'Nama lengkap wajib diisi',
                        minLength: {
                          value: 2,
                          message: 'Nama minimal 2 karakter',
                        },
                      })}
                      error={errors.name?.message}
                    />

                    <Input
                      label="Email"
                      type="email"
                      {...register('email')}
                      disabled
                      helperText="Email tidak dapat diubah"
                    />

                    <Input
                      label="Nomor HP"
                      type="tel"
                      {...register('no_hp', {
                        pattern: {
                          value: /^(\+62|62|0)8[1-9][0-9]{6,9}$/,
                          message: 'Format nomor HP tidak valid',
                        },
                      })}
                      error={errors.no_hp?.message}
                    />

                    <div>
                      <label className="block text-sm font-medium text-white mb-1">
                        Agama
                      </label>
                      <select
                        {...register('agama')}
                        className="flex h-10 w-full rounded-md border border-gold-500/30 bg-gray-800 text-white px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gold-500 focus-visible:ring-offset-2"
                      >
                        <option value="">Pilih Agama</option>
                        <option value="Islam">Islam</option>
                        <option value="Kristen">Kristen</option>
                        <option value="Katolik">Katolik</option>
                        <option value="Hindu">Hindu</option>
                        <option value="Buddha">Buddha</option>
                        <option value="Konghucu">Konghucu</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-1">
                      Alamat
                    </label>
                    <textarea
                      {...register('alamat')}
                      rows={3}
                      className="flex w-full rounded-md border border-gold-500/30 bg-gray-800 text-white px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gold-500 focus-visible:ring-offset-2 placeholder:text-gray-400"
                      placeholder="Masukkan alamat lengkap"
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" loading={loading}>
                      Simpan Perubahan
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {activeTab === 'password' && (
            <Card>
              <CardHeader>
                <CardTitle>Ubah Password</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handlePasswordSubmit(onSubmitPassword)} className="space-y-6">
                  <Input
                    label="Password Saat Ini"
                    type="password"
                    {...registerPassword('currentPassword', {
                      required: 'Password saat ini wajib diisi',
                    })}
                    error={passwordErrors.currentPassword?.message}
                  />

                  <Input
                    label="Password Baru"
                    type="password"
                    {...registerPassword('newPassword', {
                      required: 'Password baru wajib diisi',
                      minLength: {
                        value: 6,
                        message: 'Password minimal 6 karakter',
                      },
                    })}
                    error={passwordErrors.newPassword?.message}
                  />

                  <Input
                    label="Konfirmasi Password Baru"
                    type="password"
                    {...registerPassword('confirmPassword', {
                      required: 'Konfirmasi password wajib diisi',
                      validate: (value) =>
                        value === newPassword || 'Password tidak cocok',
                    })}
                    error={passwordErrors.confirmPassword?.message}
                  />

                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-md p-4">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-yellow-400">
                          Perhatian
                        </h3>
                        <div className="mt-2 text-sm text-yellow-300">
                          <ul className="list-disc pl-5 space-y-1">
                            <li>Password harus minimal 6 karakter</li>
                            <li>Gunakan kombinasi huruf, angka, dan simbol</li>
                            <li>Jangan gunakan password yang mudah ditebak</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" loading={loading}>
                      Ubah Password
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default ProfilePage;
