import { Response } from 'express';
import { AuthRequest } from '../middleware/auth.middleware';
import Atlet from '../models/Atlet';
import AtletFile from '../models/AtletFile';
import Kontingen from '../models/Kontingen';
import Event from '../models/Event';
import PendaftaranEvent from '../models/PendaftaranEvent';
import { Op } from 'sequelize';
import { atletUpload, deleteFromCloudinary, getOptimizedUrl, extractPublicId } from '../services/cloudinary.service';

export const upload = atletUpload;

export const getAllAtlet = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, search = '', kontingen_id = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    const user = req.user;

    const whereClause: any = {};
    
    // Filter berdasarkan role
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found. Please register your kontingen first.'
        });
        return;
      }
    } else if (user.role === 'admin-event') {
      // Admin event hanya melihat atlet yang mendaftar ke event mereka
      const userEvents = await Event.findAll({
        where: { id_user: user.id },
        attributes: ['id']
      });

      if (userEvents.length === 0) {
        res.json({
          success: true,
          message: 'Athletes retrieved successfully',
          data: {
            atlet: [],
            pagination: {
              total: 0,
              page: Number(page),
              limit: Number(limit),
              totalPages: 0
            }
          }
        });
        return;
      }

      const eventIds = userEvents.map(event => event.id);

      // Cari kontingen yang mendaftar ke event admin-event ini
      const registrations = await PendaftaranEvent.findAll({
        where: { id_event: { [Op.in]: eventIds } },
        attributes: ['id_kontingen']
      });

      const kontingenIds = [...new Set(registrations.map(reg => reg.id_kontingen))];

      if (kontingenIds.length === 0) {
        res.json({
          success: true,
          message: 'Athletes retrieved successfully',
          data: {
            atlet: [],
            pagination: {
              total: 0,
              page: Number(page),
              limit: Number(limit),
              totalPages: 0
            }
          }
        });
        return;
      }

      whereClause.id_kontingen = { [Op.in]: kontingenIds };
    }

    if (kontingen_id) {
      whereClause.id_kontingen = kontingen_id;
    }

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { nik: { [Op.like]: `%${search}%` } }
      ];
    }

    const { count, rows } = await Atlet.findAndCountAll({
      where: whereClause,
      limit: Number(limit),
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: Kontingen,
          as: 'atletKontingen',
          attributes: ['id', 'name']
        }
      ]
    });

    res.json({
      success: true,
      message: 'Athletes retrieved successfully',
      data: {
        atlet: rows,
        pagination: {
          total: count,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(count / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get all atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getAtletById = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Ketua kontingen hanya bisa melihat atlet dari kontingen mereka
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const atlet = await Atlet.findOne({
      where: whereClause,
      include: [
        {
          model: Kontingen,
          as: 'atletKontingen',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!atlet) {
      res.status(404).json({
        success: false,
        message: 'Athlete not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Athlete retrieved successfully',
      data: atlet
    });
  } catch (error) {
    console.error('Get atlet by id error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const createAtlet = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = req.user;
    const {
      nik,
      name,
      tanggal_lahir,
      jenis_kelamin,
      alamat,
      no_hp,
      agama,
      umur,
      berat_badan,
      tinggi_badan,
      id_kontingen
    } = req.body;

    let kontingenId = id_kontingen;

    // Untuk ketua kontingen, gunakan kontingen mereka sendiri
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (!kontingen) {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found. Please register your kontingen first.'
        });
        return;
      }
      kontingenId = kontingen.id;
    }

    // Check if NIK already exists
    const existingAtlet = await Atlet.findOne({ where: { nik } });
    if (existingAtlet) {
      res.status(400).json({
        success: false,
        message: 'NIK already exists'
      });
      return;
    }

    const atlet = await Atlet.create({
      id_kontingen: kontingenId,
      id_user: user.id,
      nik,
      name,
      tanggal_lahir,
      jenis_kelamin,
      alamat,
      no_hp,
      agama,
      umur,
      berat_badan,
      tinggi_badan,
      status_verifikasi: 'pending'
    });

    res.status(201).json({
      success: true,
      message: 'Athlete created successfully',
      data: atlet
    });
  } catch (error) {
    console.error('Create atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const updateAtlet = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Ketua kontingen hanya bisa update atlet dari kontingen mereka
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const atlet = await Atlet.findOne({ where: whereClause });
    if (!atlet) {
      res.status(404).json({
        success: false,
        message: 'Athlete not found'
      });
      return;
    }

    // Check if NIK already exists (excluding current atlet)
    if (req.body.nik && req.body.nik !== atlet.nik) {
      const existingAtlet = await Atlet.findOne({ 
        where: { 
          nik: req.body.nik,
          id: { [Op.ne]: id }
        } 
      });
      if (existingAtlet) {
        res.status(400).json({
          success: false,
          message: 'NIK already exists'
        });
        return;
      }
    }

    await atlet.update(req.body);

    res.json({
      success: true,
      message: 'Athlete updated successfully',
      data: atlet
    });
  } catch (error) {
    console.error('Update atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const deleteAtlet = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;
    
    const whereClause: any = { id };
    
    // Ketua kontingen hanya bisa delete atlet dari kontingen mereka
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const atlet = await Atlet.findOne({ where: whereClause });
    if (!atlet) {
      res.status(404).json({
        success: false,
        message: 'Athlete not found'
      });
      return;
    }

    await atlet.destroy();

    res.json({
      success: true,
      message: 'Athlete deleted successfully'
    });
  } catch (error) {
    console.error('Delete atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const uploadAtletPhoto = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const user = req.user;

    if (!req.file) {
      res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
      return;
    }

    const whereClause: any = { id };

    // Ketua kontingen hanya bisa upload foto atlet dari kontingen mereka
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const atlet = await Atlet.findOne({ where: whereClause });
    if (!atlet) {
      res.status(404).json({
        success: false,
        message: 'Athlete not found'
      });
      return;
    }

    // Delete old photo from Cloudinary if exists
    if (atlet.foto) {
      const publicId = extractPublicId(atlet.foto);
      await deleteFromCloudinary(publicId);
    }

    // Update atlet with new photo URL
    await atlet.update({ foto: req.file.path });

    res.json({
      success: true,
      message: 'Photo uploaded successfully',
      data: {
        id: atlet.id,
        foto: req.file.path,
        optimizedUrl: getOptimizedUrl(req.file.filename)
      }
    });
  } catch (error) {
    console.error('Upload atlet photo error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const uploadAtletFile = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { fileType } = req.body; // 'rapor', 'kk_ktp', or 'surat_kesehatan'
    const user = req.user;

    if (!req.file) {
      res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
      return;
    }

    if (!['rapor', 'kk_ktp', 'surat_kesehatan'].includes(fileType)) {
      res.status(400).json({
        success: false,
        message: 'Invalid file type. Must be rapor, kk_ktp, or surat_kesehatan'
      });
      return;
    }

    const whereClause: any = { id };

    // Ketua kontingen hanya bisa upload file atlet dari kontingen mereka
    if (user.role === 'ketua-kontingen') {
      const kontingen = await Kontingen.findOne({ where: { id_user: user.id } });
      if (kontingen) {
        whereClause.id_kontingen = kontingen.id;
      } else {
        res.status(404).json({
          success: false,
          message: 'Kontingen not found'
        });
        return;
      }
    }

    const atlet = await Atlet.findOne({ where: whereClause });
    if (!atlet) {
      res.status(404).json({
        success: false,
        message: 'Athlete not found'
      });
      return;
    }

    // Check if file already exists for this type
    const existingFile = await AtletFile.findOne({
      where: {
        id_atlet: atlet.id,
        file_type: fileType
      }
    });

    if (existingFile) {
      // Delete old file from Cloudinary
      const publicId = extractPublicId(existingFile.file);
      await deleteFromCloudinary(publicId);

      // Update existing record
      await existingFile.update({ file: req.file.path });
    } else {
      // Create new record
      await AtletFile.create({
        id_atlet: atlet.id,
        file: req.file.path,
        file_type: fileType
      });
    }

    res.json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        id_atlet: atlet.id,
        file_type: fileType,
        file_url: req.file.path,
        optimizedUrl: getOptimizedUrl(req.file.filename)
      }
    });
  } catch (error) {
    console.error('Upload atlet file error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const verifyAtlet = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { status_verifikasi } = req.body;
    const user = req.user;

    // Hanya admin dan admin-event yang bisa verifikasi
    if (user.role !== 'admin' && user.role !== 'admin-event') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only admin or admin-event can verify athletes.'
      });
      return;
    }

    const atlet = await Atlet.findByPk(id);
    if (!atlet) {
      res.status(404).json({
        success: false,
        message: 'Athlete not found'
      });
      return;
    }

    await atlet.update({ status_verifikasi });

    res.json({
      success: true,
      message: `Athlete ${status_verifikasi} successfully`,
      data: { id: atlet.id, status_verifikasi }
    });
  } catch (error) {
    console.error('Verify atlet error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
