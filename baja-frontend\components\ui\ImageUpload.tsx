'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import Button from './Button';

interface ImageUploadProps {
  onImagesSelected: (files: File[]) => void;
  onImageUpload?: (file: File) => Promise<void>;
  currentImages?: string[];
  maxImages?: number;
  placeholder?: string;
  maxSize?: number; // in MB
  acceptedFormats?: string[];
  className?: string;
  disabled?: boolean;
  showPreview?: boolean;
  uploadButtonText?: string;
  selectButtonText?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImagesSelected,
  onImageUpload,
  currentImages = [],
  maxImages = 1,
  placeholder = "Click to select an image",
  maxSize = 5,
  acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png'],
  className = '',
  disabled = false,
  showPreview = true,
  uploadButtonText = "Upload",
  selectButtonText = "Select Image"
}) => {
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>(currentImages);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    // Reset error
    setError(null);

    // Validate files
    const validFiles: File[] = [];
    for (const file of files) {
      // Validate file type
      if (!acceptedFormats.includes(file.type)) {
        setError(`Please select valid image files (${acceptedFormats.join(', ')})`);
        return;
      }

      // Validate file size
      if (file.size > maxSize * 1024 * 1024) {
        setError(`File size must be less than ${maxSize}MB`);
        return;
      }

      validFiles.push(file);
    }

    // Check max images limit
    if (selectedImages.length + validFiles.length > maxImages) {
      setError(`You can only select up to ${maxImages} image(s)`);
      return;
    }

    const newSelectedImages = [...selectedImages, ...validFiles];
    setSelectedImages(newSelectedImages);
    onImagesSelected(newSelectedImages);

    // Create preview URLs
    if (showPreview) {
      const newUrls = validFiles.map(file => URL.createObjectURL(file));
      setPreviewUrls(prev => [...prev, ...newUrls]);
    }
  };

  const handleUpload = async () => {
    if (selectedImages.length === 0 || !onImageUpload) return;

    setIsUploading(true);
    setError(null);

    try {
      for (const image of selectedImages) {
        await onImageUpload(image);
      }
      setSelectedImages([]);
      setPreviewUrls(currentImages);
    } catch (error) {
      console.error('Upload error:', error);
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleSelectClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemoveImage = (index: number) => {
    const newSelectedImages = selectedImages.filter((_, i) => i !== index);
    const newPreviewUrls = previewUrls.filter((_, i) => i !== index);

    setSelectedImages(newSelectedImages);
    setPreviewUrls(newPreviewUrls);
    onImagesSelected(newSelectedImages);
    setError(null);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-4 ${className} relative`}>
      {/* Preview Area */}
      {showPreview && (
        <div className="space-y-2">
          {previewUrls.length > 0 ? (
            <div className="grid grid-cols-2 gap-2">
              {previewUrls.map((url, index) => (
                <div key={index} className="relative w-full h-32 border-2 border-gray-300 rounded-lg overflow-hidden">
                  <Image
                    src={url}
                    alt={`Preview ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                  {/* Loading Overlay for Upload */}
                  {isUploading && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <div className="bg-white rounded-lg p-2 flex flex-col items-center space-y-1">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                        <p className="text-xs text-gray-700">Uploading...</p>
                      </div>
                    </div>
                  )}
                  {index >= currentImages.length && (
                    <div className="absolute top-1 right-1">
                      <Button
                        type="button"
                        onClick={() => handleRemoveImage(index)}
                        className="bg-red-500 hover:bg-red-600 text-white p-1 rounded-full text-xs w-6 h-6 flex items-center justify-center"
                        disabled={disabled || isUploading}
                      >
                        ✕
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div
              className="w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={handleSelectClick}
            >
              <div className="text-center">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 48 48"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    strokeWidth={2}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <p className="mt-2 text-sm text-gray-600">{placeholder}</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
        multiple={maxImages > 1}
      />

      {/* Error Message */}
      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-2 rounded">
          {error}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          type="button"
          onClick={handleSelectClick}
          disabled={disabled || isUploading}
          className="flex-1"
        >
          {selectButtonText}
        </Button>

        {selectedImages.length > 0 && onImageUpload && (
          <Button
            type="button"
            onClick={handleUpload}
            disabled={disabled || isUploading}
            className="flex-1 bg-green-600 hover:bg-green-700"
          >
            {isUploading ? 'Uploading...' : uploadButtonText}
          </Button>
        )}
      </div>

      {/* File Info */}
      {selectedImages.length > 0 && (
        <div className="text-sm text-gray-600">
          <p>Selected: {selectedImages.length} file(s)</p>
          <p>Total Size: {(selectedImages.reduce((total, file) => total + file.size, 0) / 1024 / 1024).toFixed(2)} MB</p>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
