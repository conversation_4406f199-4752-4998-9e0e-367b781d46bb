'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
  UsersIcon,
  CalendarDaysIcon,
  TrophyIcon,
  PhotoIcon,
  CogIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  const getNavigationItems = () => {
    const baseItems = [
      { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
    ];

    if (user?.role === 'admin') {
      return [
        ...baseItems,
        { name: 'Users', href: '/dashboard/users', icon: UsersIcon },
        { name: 'Events', href: '/dashboard/events', icon: CalendarDaysIcon },
        { name: 'Packages', href: '/dashboard/packages', icon: TrophyIcon },
        { name: 'Gallery', href: '/dashboard/gallery', icon: PhotoIcon },
      ];
    }

    if (user?.role === 'admin-event') {
      return [
        ...baseItems,
        { name: 'My Events', href: '/dashboard/my-events', icon: CalendarDaysIcon },
        { name: 'Athletes', href: '/dashboard/athletes', icon: UsersIcon },
        { name: 'Teams', href: '/dashboard/teams', icon: UsersIcon },
      ];
    }

    if (user?.role === 'ketua-kontingen') {
      return [
        ...baseItems,
        { name: 'My Team', href: '/dashboard/my-team', icon: UsersIcon },
        { name: 'Athletes', href: '/dashboard/my-athletes', icon: UsersIcon },
        { name: 'Officials', href: '/dashboard/my-officials', icon: UsersIcon },
        { name: 'Event Registration', href: '/dashboard/event-registration', icon: CalendarDaysIcon },
      ];
    }

    return baseItems;
  };

  const navigation = getNavigationItems();

  return (
    <div className="min-h-screen bg-black">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-40 lg:hidden ${sidebarOpen ? '' : 'hidden'}`}>
        <div className="fixed inset-0 bg-black bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-900 border-r border-gold-500/30">
          <div className="flex h-16 items-center justify-between px-4">
            <div className="flex items-center">
              <img className="h-8 w-auto rounded border border-gold-500/30" src="/baja.jpeg" alt="BAJA" />
              <span className="ml-2 text-lg font-bold text-white">BAJA</span>
            </div>
            <button
              type="button"
              className="text-gray-400 hover:text-gold-400 transition-colors duration-300"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-gold-500/10 hover:text-gold-400 transition-colors duration-300"
                onClick={() => setSidebarOpen(false)}
              >
                <item.icon className="mr-3 h-6 w-6" />
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-gray-900 border-r border-gold-500/30">
          <div className="flex h-16 items-center px-4">
            <img className="h-8 w-auto rounded border border-gold-500/30" src="/baja.jpeg" alt="BAJA" />
            <span className="ml-2 text-xl font-semibold text-white">BAJA</span>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-300 hover:bg-gold-500/10 hover:text-gold-400 transition-colors duration-300"
              >
                <item.icon className="mr-3 h-6 w-6" />
                {item.name}
              </Link>
            ))}
          </nav>
          <div className="border-t border-gold-500/30 p-4">
            <div className="flex items-center">
              <UserCircleIcon className="h-8 w-8 text-gold-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-white">{user?.name}</p>
                <p className="text-xs text-gray-400">{user?.role}</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="mt-3 flex w-full items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gold-500/10 hover:text-gold-400 transition-colors duration-300"
            >
              <ArrowRightOnRectangleIcon className="mr-3 h-5 w-5" />
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-10 bg-gray-900 border-b border-gold-500/30 lg:hidden">
          <div className="flex h-16 items-center justify-between px-4">
            <button
              type="button"
              className="text-gray-400 hover:text-gold-400 transition-colors duration-300"
              onClick={() => setSidebarOpen(true)}
            >
              <Bars3Icon className="h-6 w-6" />
            </button>
            <div className="flex items-center">
              <img className="h-8 w-auto rounded border border-gold-500/30" src="/baja.jpeg" alt="BAJA" />
              <span className="ml-2 text-lg font-bold text-white">BAJA</span>
            </div>
            <div className="flex items-center space-x-4">
              <UserCircleIcon className="h-8 w-8 text-gold-400" />
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6 bg-black min-h-screen">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
