import api from './api';
import Cookies from 'js-cookie';
import { LoginRequest, RegisterRequest, AuthResponse, User, ApiResponse } from '@/types';

export const authService = {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await api.post<ApiResponse<AuthResponse>>('/auth/login', credentials);
    
    if (response.data.success && response.data.data) {
      const { token } = response.data.data;
      Cookies.set('token', token, { expires: 7 }); // 7 days
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Login failed');
  },

  async register(userData: RegisterRequest): Promise<User> {
    const response = await api.post<ApiResponse<User>>('/auth/register', userData);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Registration failed');
  },

  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      Cookies.remove('token');
    }
  },

  async getProfile(): Promise<User> {
    const response = await api.get<ApiResponse<User>>('/auth/profile');
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to get profile');
  },

  async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await api.put<ApiResponse<User>>('/auth/profile', userData);
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to update profile');
  },

  isAuthenticated(): boolean {
    return !!Cookies.get('token');
  },

  getToken(): string | undefined {
    return Cookies.get('token');
  },
};
