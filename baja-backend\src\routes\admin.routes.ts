import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth.middleware';
import {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  updateUserStatus
} from '../controllers/admin/user.controller';
import { getAllEventsAdmin } from '../controllers/event.controller';
import { getAllPaketsAdmin } from '../controllers/paket.controller';
import { getAllGalleryAdmin } from '../controllers/gallery.controller';

const router = Router();

// Apply auth middleware to all admin routes
router.use(authenticate);
router.use(authorize('admin'));

// User management routes
router.get('/users', getAllUsers);
router.get('/users/:id', getUserById);
router.post('/users', createUser);
router.put('/users/:id', updateUser);
router.delete('/users/:id', deleteUser);
router.patch('/users/:id/status', updateUserStatus);

// Event management routes
router.get('/events', getAllEventsAdmin);

// Package management routes
router.get('/packages', getAllPaketsAdmin);

// Gallery management routes
router.get('/gallery', getAllGalleryAdmin);

export default router;
