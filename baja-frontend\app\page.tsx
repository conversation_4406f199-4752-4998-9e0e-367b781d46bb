'use client';

import React from 'react';
import CountUp from 'react-countup'; 
import Beams from '@/components/ui/Beams'; 
import BlurText from '@/components/ui/BlurText';
import Silk from '@/components/ui/Silk';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import Button from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Link from 'next/link';
import {
  TrophyIcon,
  UsersIcon,
  CalendarDaysIcon,
  StarIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const HomePage = () => {
  const features = [
    {
      icon: TrophyIcon,
      title: 'Event Management',
      description: 'Kelola event olahraga bela diri dengan mudah dan efisien'
    },
    {
      icon: UsersIcon,
      title: 'Manajemen Atlet',
      description: 'Daftarkan dan kelola data atlet serta kontingen'
    },
    {
      icon: CalendarDaysIcon,
      title: '<PERSON><PERSON><PERSON>walan',
      description: 'Atur jadwal pertandingan dan acara dengan sistematis'
    },
    {
      icon: StarIcon,
      title: 'Sistem Penilaian',
      description: 'Catat hasil pertandingan dan tentukan pemenang'
    }
  ];

  const packages = [
    {
      name: 'Basic',
      price: 'Rp 500,000',
      features: [
        'Manajemen Event Dasar',
        'Registrasi Atlet',
        'Laporan Sederhana',
        'Support Email'
      ]
    },
    {
      name: 'Professional',
      price: 'Rp 1,000,000',
      features: [
        'Semua fitur Basic',
        'Manajemen Kontingen',
        'Sistem Penilaian',
        'Laporan Detail',
        'Support Prioritas'
      ],
      popular: true
    },
    {
      name: 'Enterprise',
      price: 'Rp 2,000,000',
      features: [
        'Semua fitur Professional',
        'Multi Event',
        'Custom Branding',
        'API Access',
        'Dedicated Support'
      ]
    }
  ];

const stats = [
  { label: 'Event Terselenggara', value: 150, suffix: '+' },
  { label: 'Atlet Terdaftar', value: 5000, suffix: '+' },
  { label: 'Kontingen Aktif', value: 200, suffix: '+' },
  { label: 'Kepuasan Pengguna', value: 98, suffix: '%' }
];

  return (
    <div className="min-h-screen bg-black">
      <Navbar />

      <section className="relative pt-16 bg-gradient-to-br from-black via-gray-900 to-yellow-900 overflow-hidden">
  {/* Background Beams */}
  <div className="absolute inset-0 z-0">
    <Beams
      beamWidth={2}
      beamHeight={15}
      beamNumber={12}
      lightColor="#ffffff"
      speed={2}
      noiseIntensity={1.75}
      scale={0.2}
      rotation={15}
    />
  </div>

  {/* Content Layer */}
  <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
    <div className="text-center">
      <BlurText
        text="BAJA Event Organizer"
        delay={150}
        animateBy="words"
        direction="top"
        onAnimationComplete={() => console.log('Animation completed!')}
        className="text-4xl justify-center md:text-6xl font-bold text-white mb-6"
      />
      <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
        Platform terpercaya untuk mengelola event olahraga bela diri.
        Sistem informasi yang cerdas dan mudah digunakan untuk semua kebutuhan event organizer Anda.
      </p>
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Link href="/auth/register">
          <Button size="lg" className="w-full sm:w-auto">
            Mulai Sekarang
          </Button>
        </Link>
        <Link href="/events">
          <Button variant="outline" size="lg" className="w-full sm:w-auto">
            Lihat Event
          </Button>
        </Link>
      </div>
    </div>
  </div>
</section>

      {/* Stats Section */}
   <section className="py-16 bg-gray-900">
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
      {stats.map((stat, index) => (
        <div key={index} className="text-center">
          <div className="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">
            <CountUp
              start={0}
              end={stat.value}
              duration={2}
              separator="."
              suffix={stat.suffix}
            />
          </div>
          <div className="text-gray-300">{stat.label}</div>
        </div>
      ))}
    </div>
  </div>
</section>

      {/* Features Section */}
      <section className="py-16 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Fitur Unggulan
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Dapatkan semua yang Anda butuhkan untuk mengelola event olahraga bela diri
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-xl hover:shadow-yellow-500/20 transition-all duration-300 bg-gray-900 border-gray-700">
                <CardHeader>
                  <feature.icon className="h-12 w-12 text-black-400 mx-auto mb-4 drop-shadow-lg" />
                  <CardTitle className="text-xl text-white">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Packages Section */}
      <section className="py-16 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Paket Berlangganan
            </h2>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Pilih paket yang sesuai dengan kebutuhan event Anda
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <Card
                key={index}
                className={`relative bg-black border-gray-700 hover:shadow-xl transition-all duration-300 ${
                  pkg.popular 
                    ? 'ring-2 ring-yellow-400 shadow-lg shadow-yellow-400/20 hover:shadow-yellow-400/30' 
                    : 'hover:shadow-gray-500/20'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-yellow-400 text-black px-4 py-1 rounded-full text-sm font-bold shadow-lg">
                      Paling Populer
                    </span>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl text-white font-bold">{pkg.name}</CardTitle>
                  <div className="text-3xl font-bold text-black-400 mt-4 drop-shadow-lg">
                    {pkg.price}
                  </div>
                  <div className="text-gray-400 font-medium">per event</div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <CheckCircleIcon className="h-5 w-5 text-yellow-400 mr-3 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className="w-full mt-6"
                    variant={pkg.popular ? 'primary' : 'outline'}
                  >
                    Pilih Paket
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-16 bg-gradient-to-r from-yellow-600 to-yellow-500 overflow-hidden">
        {/* Background Silk */}
        <div className="absolute inset-0">
          <Silk
            speed={5}
            scale={1}
            color="#202020"
            noiseIntensity={1.5}
            rotation={0}
          />
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Siap Memulai Event Anda?
          </h2>
          <p className="text-xl text-gray-100 mb-8 max-w-2xl mx-auto">
            Bergabunglah dengan ribuan event organizer yang telah mempercayai platform kami
          </p>
          <Link href="/auth/register">
            <Button 
              size="lg" 
              className="bg-black text-white hover:bg-gray-800 border-2 border-black hover:border-gray-800 hover:shadow-xl hover:shadow-black/30 transition-all duration-300 font-semibold"
            >
              Daftar Sekarang
            </Button>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default HomePage;