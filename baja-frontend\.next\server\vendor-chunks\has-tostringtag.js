"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/has-tostringtag";
exports.ids = ["vendor-chunks/has-tostringtag"];
exports.modules = {

/***/ "(ssr)/./node_modules/has-tostringtag/shams.js":
/*!***********************************************!*\
  !*** ./node_modules/has-tostringtag/shams.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar hasSymbols = __webpack_require__(/*! has-symbols/shams */ \"(ssr)/./node_modules/has-symbols/shams.js\");\n/** @type {import('.')} */ module.exports = function hasToStringTagShams() {\n    return hasSymbols() && !!Symbol.toStringTag;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzLXRvc3RyaW5ndGFnL3NoYW1zLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBRUEsSUFBSUEsYUFBYUMsbUJBQU9BLENBQUM7QUFFekIsd0JBQXdCLEdBQ3hCQyxPQUFPQyxPQUFPLEdBQUcsU0FBU0M7SUFDekIsT0FBT0osZ0JBQWdCLENBQUMsQ0FBQ0ssT0FBT0MsV0FBVztBQUM1QyIsInNvdXJjZXMiOlsid2VicGFjazovL2JhamEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzLXRvc3RyaW5ndGFnL3NoYW1zLmpzPzM2MzYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgaGFzU3ltYm9scyA9IHJlcXVpcmUoJ2hhcy1zeW1ib2xzL3NoYW1zJyk7XG5cbi8qKiBAdHlwZSB7aW1wb3J0KCcuJyl9ICovXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIGhhc1RvU3RyaW5nVGFnU2hhbXMoKSB7XG5cdHJldHVybiBoYXNTeW1ib2xzKCkgJiYgISFTeW1ib2wudG9TdHJpbmdUYWc7XG59O1xuIl0sIm5hbWVzIjpbImhhc1N5bWJvbHMiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsImhhc1RvU3RyaW5nVGFnU2hhbXMiLCJTeW1ib2wiLCJ0b1N0cmluZ1RhZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/has-tostringtag/shams.js\n");

/***/ })

};
;