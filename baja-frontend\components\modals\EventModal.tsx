'use client';

import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import ImageUpload from '@/components/ui/ImageUpload';
import LoadingOverlay from '@/components/ui/LoadingOverlay';
import { AdminEvent } from '@/types';
import { uploadService } from '@/lib/upload.service';
import toast from 'react-hot-toast';

interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (eventData: Partial<AdminEvent>, imageFile?: File) => Promise<void>;
  event?: AdminEvent | null;
  mode: 'create' | 'edit';
}

const EventModal: React.FC<EventModalProps> = ({
  isOpen,
  onClose,
  onSave,
  event,
  mode
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    start_date: '',
    end_date: '',
    lokasi: '',
    biaya_registrasi: '',
    metode_pembayaran: 'transfer',
    status: 'active',
    event_image: ''
  });
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  useEffect(() => {
    if (mode === 'edit' && event) {
      setFormData({
        name: event.name || '',
        description: event.description || '',
        start_date: event.start_date ? new Date(event.start_date).toISOString().slice(0, 16) : '',
        end_date: event.end_date ? new Date(event.end_date).toISOString().slice(0, 16) : '',
        lokasi: event.lokasi || '',
        biaya_registrasi: event.biaya_registrasi?.toString() || '',
        metode_pembayaran: event.metode_pembayaran || 'transfer',
        status: event.status || 'active',
        event_image: event.event_image || ''
      });
    } else {
      setFormData({
        name: '',
        description: '',
        start_date: '',
        end_date: '',
        lokasi: '',
        biaya_registrasi: '',
        metode_pembayaran: 'transfer',
        status: 'active',
        event_image: ''
      });
    }
    setSelectedFile(null);
  }, [mode, event, isOpen]);

  const handleImageSelect = (file: File) => {
    setSelectedFile(file);
    // Don't show success message here, just store the file for later upload
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.start_date || !formData.end_date || !formData.lokasi.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (new Date(formData.start_date) >= new Date(formData.end_date)) {
      toast.error('End date must be after start date');
      return;
    }

    setLoading(true);
    try {
      const eventData = {
        ...formData,
        biaya_registrasi: parseFloat(formData.biaya_registrasi) || 0,
        start_date: new Date(formData.start_date).toISOString(),
        end_date: new Date(formData.end_date).toISOString(),
        status: formData.status as 'active' | 'completed'
      };

      // Pass the selected file to the parent component
      await onSave(eventData, selectedFile || undefined);

      toast.success(`Event ${mode === 'create' ? 'created' : 'updated'} successfully!`);
      onClose();
    } catch (error: any) {
      toast.error(error.message || `Failed to ${mode} event`);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {mode === 'create' ? 'Create New Event' : 'Edit Event'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Event Name *
            </label>
            <Input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter event name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter event description"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date *
              </label>
              <Input
                type="datetime-local"
                name="start_date"
                value={formData.start_date}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date *
              </label>
              <Input
                type="datetime-local"
                name="end_date"
                value={formData.end_date}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Location *
            </label>
            <Input
              type="text"
              name="lokasi"
              value={formData.lokasi}
              onChange={handleChange}
              placeholder="Enter event location"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Registration Fee
              </label>
              <Input
                type="number"
                name="biaya_registrasi"
                value={formData.biaya_registrasi}
                onChange={handleChange}
                placeholder="0"
                min="0"
                step="0.01"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Method
              </label>
              <select
                name="metode_pembayaran"
                value={formData.metode_pembayaran}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="transfer">Bank Transfer</option>
                <option value="cash">Cash</option>
                <option value="online">Online Payment</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="active">Active</option>
              <option value="completed">Completed</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Event Image
            </label>
            <ImageUpload
              onImageSelect={handleImageSelect}
              currentImageUrl={formData.event_image}
              placeholder="Select an event image (will be uploaded when event is saved)"
              maxSize={5}
              acceptedFormats={['image/jpeg', 'image/jpg', 'image/png']}
              showPreview={true}
              selectButtonText="Choose Image"
              disabled={loading}
            />
            {formData.event_image && !selectedFile && (
              <p className="text-sm text-green-600 mt-1">
                ✓ Current image uploaded
              </p>
            )}
            {selectedFile && (
              <p className="text-sm text-blue-600 mt-1">
                📷 Image selected: {selectedFile.name} (will be uploaded when event is saved)
              </p>
            )}
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              loading={loading}
            >
              {mode === 'create' ? 'Create Event' : 'Update Event'}
            </Button>
          </div>
        </form>
      </div>

      <LoadingOverlay
        isLoading={loading}
        message={mode === 'create' ? 'Creating event...' : 'Updating event...'}
      />
    </div>
  );
};

export default EventModal;
