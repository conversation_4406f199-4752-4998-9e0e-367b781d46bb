'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { api } from '@/lib/api';
import { Card } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Table from '@/components/ui/Table';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import Badge from '@/components/ui/Badge';
import AthleteModal from '@/components/modals/AthleteModal';
import DeleteConfirmModal from '@/components/modals/DeleteConfirmModal';
import { 
  PlusIcon, 
  MagnifyingGlassIcon, 
  PencilIcon, 
  TrashIcon,
  UserIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface Athlete {
  id: number;
  nik: string;
  name: string;
  no_hp?: string;
  tanggal_lahir: string;
  jenis_kelamin: 'M' | 'F';
  agama?: string;
  alamat?: string;
  umur: number;
  berat_badan: string;
  tinggi_badan: string;
  status_verifikasi: 'pending' | 'verified';
  foto?: string;
  created_at: string;
  updated_at: string;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const MyAthletesPage = () => {
  const { user } = useAuth();
  const [athletes, setAthletes] = useState<Athlete[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [showModal, setShowModal] = useState(false);
  const [selectedAthlete, setSelectedAthlete] = useState<Athlete | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [athleteToDelete, setAthleteToDelete] = useState<Athlete | null>(null);

  useEffect(() => {
    fetchAthletes();
  }, [pagination.page, searchTerm]);

  const fetchAthletes = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search: searchTerm,
      };

      const response = await api.get('/atlet', { params });

      if (response.data.success) {
        setAthletes(response.data.data.atlet);
        setPagination(response.data.data.pagination);
      } else {
        setError(response.data.message);
      }
    } catch (error) {
      console.error('Error fetching athletes:', error);
      setError('Failed to fetch athletes');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (athlete: Athlete) => {
    setSelectedAthlete(athlete);
    setShowModal(true);
  };

  const handleDelete = (athlete: Athlete) => {
    setAthleteToDelete(athlete);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!athleteToDelete) return;

    try {
      const response = await api.delete(`/atlet/${athleteToDelete.id}`);

      if (response.data.success) {
        fetchAthletes();
        setShowDeleteModal(false);
        setAthleteToDelete(null);
      } else {
        setError(response.data.message);
      }
    } catch (error) {
      console.error('Error deleting athlete:', error);
      setError('Failed to delete athlete');
    }
  };

  const handleModalClose = () => {
    setShowModal(false);
    setSelectedAthlete(null);
  };

  const handleModalSuccess = () => {
    fetchAthletes();
    handleModalClose();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return (
          <Badge variant="success" className="flex items-center">
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            Verified
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="warning" className="flex items-center">
            <ClockIcon className="h-4 w-4 mr-1" />
            Pending
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getGenderText = (gender: string) => {
    return gender === 'M' ? 'Male' : 'Female';
  };

  const columns = [
    {
      key: 'nik',
      label: 'NIK',
      render: (athlete: Athlete) => (
        <div className="font-mono text-sm">{athlete.nik}</div>
      )
    },
    {
      key: 'name',
      label: 'Name',
      render: (athlete: Athlete) => (
        <div className="flex items-center">
          {athlete.foto ? (
            <img 
              src={athlete.foto} 
              alt={athlete.name}
              className="h-8 w-8 rounded-full mr-3 object-cover"
            />
          ) : (
            <UserIcon className="h-8 w-8 text-gray-400 mr-3" />
          )}
          <div>
            <div className="font-medium text-white">{athlete.name}</div>
            <div className="text-sm text-gray-400">{getGenderText(athlete.jenis_kelamin)}, {athlete.umur} years</div>
          </div>
        </div>
      )
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (athlete: Athlete) => (
        <div className="text-sm">
          <div className="text-white">{athlete.no_hp || 'N/A'}</div>
          <div className="text-gray-400">{athlete.agama || 'N/A'}</div>
        </div>
      )
    },
    {
      key: 'physical',
      label: 'Physical',
      render: (athlete: Athlete) => (
        <div className="text-sm">
          <div className="text-white">{athlete.tinggi_badan} cm</div>
          <div className="text-gray-400">{athlete.berat_badan} kg</div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (athlete: Athlete) => getStatusBadge(athlete.status_verifikasi)
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (athlete: Athlete) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="secondary"
            onClick={() => handleEdit(athlete)}
          >
            <PencilIcon className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => handleDelete(athlete)}
          >
            <TrashIcon className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white">My Athletes</h1>
            <p className="text-gray-400 mt-1">Manage your team athletes</p>
          </div>
          <Button
            onClick={() => setShowModal(true)}
            className="bg-gold-500 hover:bg-gold-600 text-black"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Athlete
          </Button>
        </div>

        {error && (
          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
            <p className="text-red-400">{error}</p>
          </div>
        )}

        {/* Search and Filters */}
        <Card className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search athletes by name or NIK..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Athletes Table */}
        <Card>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <Table
              columns={columns}
              data={athletes}
              pagination={pagination}
              onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
            />
          )}
        </Card>

        {/* Modals */}
        <AthleteModal
          isOpen={showModal}
          onClose={handleModalClose}
          onSuccess={handleModalSuccess}
          athlete={selectedAthlete}
        />

        <DeleteConfirmModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={confirmDelete}
          title="Delete Athlete"
          message={`Are you sure you want to delete ${athleteToDelete?.name}? This action cannot be undone.`}
        />
      </div>
    </DashboardLayout>
  );
};

export default MyAthletesPage;
