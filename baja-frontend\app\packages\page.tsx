'use client';

import React, { useState, useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { Paket } from '@/types';
import { formatCurrency } from '@/lib/utils';
import { uploadService } from '@/lib/upload.service';
import { CheckCircleIcon } from '@heroicons/react/24/outline';
import api from '@/lib/api';

const PackagesPage = () => {
  const [packages, setPackages] = useState<Paket[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await api.get('/paket/active');
        if (response.data.success && response.data.data) {
          setPackages(response.data.data);
        } else {
          console.error('Failed to fetch packages:', response.data.message);
          setPackages([]);
        }
      } catch (error) {
        console.error('Error fetching packages:', error);
        setPackages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, []);

  const getDefaultFeatures = (): string[] => {
    return [
      'Manajemen Event',
      'Registrasi Peserta',
      'Laporan Event',
      'Support 24/7'
    ];
  };

  const getPopularPackage = () => {
    // Mark the middle package as popular
    return packages.length > 1 ? packages[1].id : null;
  };

  return (
    <div className="min-h-screen bg-black">
      <Navbar />

      <div className="pt-16">
        {/* Header */}
        <div className="bg-gradient-to-r from-black via-gray-900 to-black border-b border-gold-500/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
            <h1 className="text-4xl font-bold text-white">Paket <span className="text-gold-400">Berlangganan</span></h1>
            <p className="mt-4 text-xl text-gray-300 max-w-3xl mx-auto">
              Pilih paket yang sesuai dengan kebutuhan event Anda.
              Semua paket dilengkapi dengan fitur-fitur terbaik untuk mengelola event olahraga bela diri.
            </p>
          </div>
        </div>

        {/* Packages */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 bg-black">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="bg-gray-900 rounded-lg border border-gray-800 animate-pulse">
                  <div className="p-6">
                    <div className="h-6 bg-gray-800 rounded mb-4"></div>
                    <div className="h-8 bg-gray-800 rounded mb-2"></div>
                    <div className="h-4 bg-gray-800 rounded mb-4"></div>
                    <div className="space-y-3">
                      {[...Array(6)].map((_, i) => (
                        <div key={i} className="h-4 bg-gray-800 rounded"></div>
                      ))}
                    </div>
                    <div className="mt-6 h-10 bg-gray-800 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : packages.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="mt-2 text-sm font-medium text-white">Tidak ada paket</h3>
              <p className="mt-1 text-sm text-gray-400">
                Belum ada paket yang tersedia saat ini.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {packages.map((pkg) => {
                const isPopular = pkg.id === getPopularPackage();
                const features = getDefaultFeatures();

                return (
                  <div
                    key={pkg.id}
                    className={`relative bg-gray-900 rounded-lg border border-gray-800 hover:border-gold-500/50 hover:shadow-xl hover:shadow-gold-500/10 transition-all duration-300 ${isPopular ? 'ring-2 ring-gold-500 shadow-lg shadow-gold-500/20 scale-105' : ''}`}
                  >
                    {isPopular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-gold-500 text-black px-4 py-1 rounded-full text-sm font-bold shadow-lg">
                          Paling Populer
                        </span>
                      </div>
                    )}

                    <div className="p-6">
                      <div className="mb-4">
                        <img
                          src={pkg.images ? uploadService.getOptimizedUrl(pkg.images) : '/placeholder-package.jpg'}
                          alt={pkg.name}
                          className="w-full h-48 object-cover rounded-lg aspect-[3/4]"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder-package.jpg';
                          }}
                        />
                      </div>
                      <h3 className="text-2xl font-bold text-white text-center mb-2">{pkg.name}</h3>
                      <div className="text-lg text-gold-400 mt-2 text-center font-semibold">
                        Paket Premium
                      </div>
                      {pkg.description && (
                        <p className="text-sm text-gray-300 mt-2 text-center">{pkg.description}</p>
                      )}
                    </div>

                    <div className="px-6 pb-6">
                      <ul className="space-y-3 mb-6">
                        {features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            <CheckCircleIcon className="h-5 w-5 text-gold-400 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-300">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      <Button
                        className="w-full"
                        variant={isPopular ? 'primary' : 'outline'}
                        size="lg"
                      >
                        Pilih Paket
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* FAQ Section */}
        <div className="bg-gradient-to-r from-gray-900 via-black to-gray-900 border-t border-gold-500/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white">Pertanyaan <span className="text-gold-400">Umum</span></h2>
              <p className="mt-4 text-gray-300">
                Jawaban untuk pertanyaan yang sering diajukan tentang paket berlangganan
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-gray-800/50 p-6 rounded-lg border border-gold-500/20 hover:border-gold-500/50 transition-colors duration-300">
                <h3 className="text-lg font-semibold text-gold-400 mb-3">
                  Apakah bisa upgrade paket di tengah event?
                </h3>
                <p className="text-gray-200 leading-relaxed">
                  Ya, Anda dapat melakukan upgrade paket kapan saja. Perbedaan harga akan dihitung secara proporsional.
                </p>
              </div>

              <div className="bg-gray-800/50 p-6 rounded-lg border border-gold-500/20 hover:border-gold-500/50 transition-colors duration-300">
                <h3 className="text-lg font-semibold text-gold-400 mb-3">
                  Bagaimana sistem pembayaran?
                </h3>
                <p className="text-gray-200 leading-relaxed">
                  Pembayaran dapat dilakukan melalui transfer bank, e-wallet, atau kartu kredit. Pembayaran dilakukan per event.
                </p>
              </div>

              <div className="bg-gray-800/50 p-6 rounded-lg border border-gold-500/20 hover:border-gold-500/50 transition-colors duration-300">
                <h3 className="text-lg font-semibold text-gold-400 mb-3">
                  Apakah ada trial gratis?
                </h3>
                <p className="text-gray-200 leading-relaxed">
                  Ya, kami menyediakan trial gratis selama 7 hari untuk semua paket. Tidak ada biaya tersembunyi.
                </p>
              </div>

              <div className="bg-gray-800/50 p-6 rounded-lg border border-gold-500/20 hover:border-gold-500/50 transition-colors duration-300">
                <h3 className="text-lg font-semibold text-gold-400 mb-3">
                  Bagaimana dengan support teknis?
                </h3>
                <p className="text-gray-200 leading-relaxed">
                  Semua paket mendapat support teknis. Paket Professional dan Enterprise mendapat prioritas lebih tinggi.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-gold-600 to-gold-500">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
            <h2 className="text-3xl font-bold text-black mb-4">
              Siap Memulai Event Anda?
            </h2>
            <p className="text-xl text-black/80 mb-8 max-w-2xl mx-auto font-medium">
              Bergabunglah dengan ribuan event organizer yang telah mempercayai platform kami
            </p>
            <Button
              size="lg"
              className="bg-black text-gold-400 hover:bg-gray-900 border-2 border-black hover:border-gray-900 hover:shadow-xl hover:shadow-black/30 transition-all duration-300 font-semibold"
            >
              Mulai Trial Gratis
            </Button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default PackagesPage;
