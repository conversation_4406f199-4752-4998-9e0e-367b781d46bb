"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three";
exports.ids = ["vendor-chunks/@react-three"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-three/drei/core/PerspectiveCamera.js":
/*!******************************************************************!*\
  !*** ./node_modules/@react-three/drei/core/PerspectiveCamera.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PerspectiveCamera: () => (/* binding */ PerspectiveCamera)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/index-5918012a.esm.js\");\n/* harmony import */ var react_merge_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-merge-refs */ \"(ssr)/./node_modules/react-merge-refs/dist/react-merge-refs.esm.js\");\n/* harmony import */ var _useFBO_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useFBO.js */ \"(ssr)/./node_modules/@react-three/drei/core/useFBO.js\");\n\n\n\n\n\nconst isFunction = (node)=>typeof node === \"function\";\nconst PerspectiveCamera = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ envMap, resolution = 256, frames = Infinity, makeDefault, children, ...props }, ref)=>{\n    const set = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.A)(({ set })=>set);\n    const camera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.A)(({ camera })=>camera);\n    const size = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.A)(({ size })=>size);\n    const cameraRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const groupRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    const fbo = (0,_useFBO_js__WEBPACK_IMPORTED_MODULE_4__.useFBO)(resolution);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(()=>{\n        if (!props.manual) {\n            cameraRef.current.aspect = size.width / size.height;\n        }\n    }, [\n        size,\n        props\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(()=>{\n        cameraRef.current.updateProjectionMatrix();\n    });\n    let count = 0;\n    let oldEnvMap = null;\n    const functional = isFunction(children);\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_3__.C)((state)=>{\n        if (functional && (frames === Infinity || count < frames)) {\n            groupRef.current.visible = false;\n            state.gl.setRenderTarget(fbo);\n            oldEnvMap = state.scene.background;\n            if (envMap) state.scene.background = envMap;\n            state.gl.render(state.scene, cameraRef.current);\n            state.scene.background = oldEnvMap;\n            state.gl.setRenderTarget(null);\n            groupRef.current.visible = true;\n            count++;\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(()=>{\n        if (makeDefault) {\n            const oldCam = camera;\n            set(()=>({\n                    camera: cameraRef.current\n                }));\n            return ()=>set(()=>({\n                        camera: oldCam\n                    }));\n        }\n    // The camera should not be part of the dependency list because this components camera is a stable reference\n    // that must exchange the default, and clean up after itself on unmount.\n    }, [\n        cameraRef,\n        makeDefault,\n        set\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"perspectiveCamera\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        ref: (0,react_merge_refs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])([\n            cameraRef,\n            ref\n        ])\n    }, props), !functional && children), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"group\", {\n        ref: groupRef\n    }, functional && children(fbo.texture)));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2RyZWkvY29yZS9QZXJzcGVjdGl2ZUNhbWVyYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBEO0FBQzNCO0FBQ3lCO0FBQ2Y7QUFDSjtBQUVyQyxNQUFNTSxhQUFhQyxDQUFBQSxPQUFRLE9BQU9BLFNBQVM7QUFDM0MsTUFBTUMsb0JBQW9CLFdBQVcsR0FBRVAsNkNBQWdCLENBQUMsQ0FBQyxFQUN2RFMsTUFBTSxFQUNOQyxhQUFhLEdBQUcsRUFDaEJDLFNBQVNDLFFBQVEsRUFDakJDLFdBQVcsRUFDWEMsUUFBUSxFQUNSLEdBQUdDLE9BQ0osRUFBRUM7SUFDRCxNQUFNQyxNQUFNaEIscURBQVFBLENBQUMsQ0FBQyxFQUNwQmdCLEdBQUcsRUFDSixHQUFLQTtJQUNOLE1BQU1DLFNBQVNqQixxREFBUUEsQ0FBQyxDQUFDLEVBQ3ZCaUIsTUFBTSxFQUNQLEdBQUtBO0lBQ04sTUFBTUMsT0FBT2xCLHFEQUFRQSxDQUFDLENBQUMsRUFDckJrQixJQUFJLEVBQ0wsR0FBS0E7SUFDTixNQUFNQyxZQUFZcEIseUNBQVksQ0FBQztJQUMvQixNQUFNc0IsV0FBV3RCLHlDQUFZLENBQUM7SUFDOUIsTUFBTXVCLE1BQU1uQixrREFBTUEsQ0FBQ007SUFDbkJWLGtEQUFxQixDQUFDO1FBQ3BCLElBQUksQ0FBQ2UsTUFBTVUsTUFBTSxFQUFFO1lBQ2pCTCxVQUFVTSxPQUFPLENBQUNDLE1BQU0sR0FBR1IsS0FBS1MsS0FBSyxHQUFHVCxLQUFLVSxNQUFNO1FBQ3JEO0lBQ0YsR0FBRztRQUFDVjtRQUFNSjtLQUFNO0lBQ2hCZixrREFBcUIsQ0FBQztRQUNwQm9CLFVBQVVNLE9BQU8sQ0FBQ0ksc0JBQXNCO0lBQzFDO0lBQ0EsSUFBSUMsUUFBUTtJQUNaLElBQUlDLFlBQVk7SUFDaEIsTUFBTUMsYUFBYTVCLFdBQVdTO0lBQzlCWixxREFBUUEsQ0FBQ2dDLENBQUFBO1FBQ1AsSUFBSUQsY0FBZXRCLENBQUFBLFdBQVdDLFlBQVltQixRQUFRcEIsTUFBSyxHQUFJO1lBQ3pEVyxTQUFTSSxPQUFPLENBQUNTLE9BQU8sR0FBRztZQUMzQkQsTUFBTUUsRUFBRSxDQUFDQyxlQUFlLENBQUNkO1lBQ3pCUyxZQUFZRSxNQUFNSSxLQUFLLENBQUNDLFVBQVU7WUFDbEMsSUFBSTlCLFFBQVF5QixNQUFNSSxLQUFLLENBQUNDLFVBQVUsR0FBRzlCO1lBQ3JDeUIsTUFBTUUsRUFBRSxDQUFDSSxNQUFNLENBQUNOLE1BQU1JLEtBQUssRUFBRWxCLFVBQVVNLE9BQU87WUFDOUNRLE1BQU1JLEtBQUssQ0FBQ0MsVUFBVSxHQUFHUDtZQUN6QkUsTUFBTUUsRUFBRSxDQUFDQyxlQUFlLENBQUM7WUFDekJmLFNBQVNJLE9BQU8sQ0FBQ1MsT0FBTyxHQUFHO1lBQzNCSjtRQUNGO0lBQ0Y7SUFDQS9CLGtEQUFxQixDQUFDO1FBQ3BCLElBQUlhLGFBQWE7WUFDZixNQUFNNEIsU0FBU3ZCO1lBQ2ZELElBQUksSUFBTztvQkFDVEMsUUFBUUUsVUFBVU0sT0FBTztnQkFDM0I7WUFDQSxPQUFPLElBQU1ULElBQUksSUFBTzt3QkFDdEJDLFFBQVF1QjtvQkFDVjtRQUNGO0lBQ0EsNEdBQTRHO0lBQzVHLHdFQUF3RTtJQUMxRSxHQUFHO1FBQUNyQjtRQUFXUDtRQUFhSTtLQUFJO0lBQ2hDLE9BQU8sV0FBVyxHQUFFakIsZ0RBQW1CLENBQUNBLDJDQUFjLEVBQUUsTUFBTSxXQUFXLEdBQUVBLGdEQUFtQixDQUFDLHFCQUFxQkQsOEVBQVFBLENBQUM7UUFDM0hpQixLQUFLYiw0REFBU0EsQ0FBQztZQUFDaUI7WUFBV0o7U0FBSTtJQUNqQyxHQUFHRCxRQUFRLENBQUNrQixjQUFjbkIsV0FBVyxXQUFXLEdBQUVkLGdEQUFtQixDQUFDLFNBQVM7UUFDN0VnQixLQUFLTTtJQUNQLEdBQUdXLGNBQWNuQixTQUFTUyxJQUFJcUIsT0FBTztBQUN2QztBQUU2QiIsInNvdXJjZXMiOlsid2VicGFjazovL2JhamEtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2RyZWkvY29yZS9QZXJzcGVjdGl2ZUNhbWVyYS5qcz80NjZkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tICdAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzJztcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVRocmVlLCB1c2VGcmFtZSB9IGZyb20gJ0ByZWFjdC10aHJlZS9maWJlcic7XG5pbXBvcnQgbWVyZ2VSZWZzIGZyb20gJ3JlYWN0LW1lcmdlLXJlZnMnO1xuaW1wb3J0IHsgdXNlRkJPIH0gZnJvbSAnLi91c2VGQk8uanMnO1xuXG5jb25zdCBpc0Z1bmN0aW9uID0gbm9kZSA9PiB0eXBlb2Ygbm9kZSA9PT0gJ2Z1bmN0aW9uJztcbmNvbnN0IFBlcnNwZWN0aXZlQ2FtZXJhID0gLyojX19QVVJFX18qL1JlYWN0LmZvcndhcmRSZWYoKHtcbiAgZW52TWFwLFxuICByZXNvbHV0aW9uID0gMjU2LFxuICBmcmFtZXMgPSBJbmZpbml0eSxcbiAgbWFrZURlZmF1bHQsXG4gIGNoaWxkcmVuLFxuICAuLi5wcm9wc1xufSwgcmVmKSA9PiB7XG4gIGNvbnN0IHNldCA9IHVzZVRocmVlKCh7XG4gICAgc2V0XG4gIH0pID0+IHNldCk7XG4gIGNvbnN0IGNhbWVyYSA9IHVzZVRocmVlKCh7XG4gICAgY2FtZXJhXG4gIH0pID0+IGNhbWVyYSk7XG4gIGNvbnN0IHNpemUgPSB1c2VUaHJlZSgoe1xuICAgIHNpemVcbiAgfSkgPT4gc2l6ZSk7XG4gIGNvbnN0IGNhbWVyYVJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgY29uc3QgZ3JvdXBSZWYgPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gIGNvbnN0IGZibyA9IHVzZUZCTyhyZXNvbHV0aW9uKTtcbiAgUmVhY3QudXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXByb3BzLm1hbnVhbCkge1xuICAgICAgY2FtZXJhUmVmLmN1cnJlbnQuYXNwZWN0ID0gc2l6ZS53aWR0aCAvIHNpemUuaGVpZ2h0O1xuICAgIH1cbiAgfSwgW3NpemUsIHByb3BzXSk7XG4gIFJlYWN0LnVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgY2FtZXJhUmVmLmN1cnJlbnQudXBkYXRlUHJvamVjdGlvbk1hdHJpeCgpO1xuICB9KTtcbiAgbGV0IGNvdW50ID0gMDtcbiAgbGV0IG9sZEVudk1hcCA9IG51bGw7XG4gIGNvbnN0IGZ1bmN0aW9uYWwgPSBpc0Z1bmN0aW9uKGNoaWxkcmVuKTtcbiAgdXNlRnJhbWUoc3RhdGUgPT4ge1xuICAgIGlmIChmdW5jdGlvbmFsICYmIChmcmFtZXMgPT09IEluZmluaXR5IHx8IGNvdW50IDwgZnJhbWVzKSkge1xuICAgICAgZ3JvdXBSZWYuY3VycmVudC52aXNpYmxlID0gZmFsc2U7XG4gICAgICBzdGF0ZS5nbC5zZXRSZW5kZXJUYXJnZXQoZmJvKTtcbiAgICAgIG9sZEVudk1hcCA9IHN0YXRlLnNjZW5lLmJhY2tncm91bmQ7XG4gICAgICBpZiAoZW52TWFwKSBzdGF0ZS5zY2VuZS5iYWNrZ3JvdW5kID0gZW52TWFwO1xuICAgICAgc3RhdGUuZ2wucmVuZGVyKHN0YXRlLnNjZW5lLCBjYW1lcmFSZWYuY3VycmVudCk7XG4gICAgICBzdGF0ZS5zY2VuZS5iYWNrZ3JvdW5kID0gb2xkRW52TWFwO1xuICAgICAgc3RhdGUuZ2wuc2V0UmVuZGVyVGFyZ2V0KG51bGwpO1xuICAgICAgZ3JvdXBSZWYuY3VycmVudC52aXNpYmxlID0gdHJ1ZTtcbiAgICAgIGNvdW50Kys7XG4gICAgfVxuICB9KTtcbiAgUmVhY3QudXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBpZiAobWFrZURlZmF1bHQpIHtcbiAgICAgIGNvbnN0IG9sZENhbSA9IGNhbWVyYTtcbiAgICAgIHNldCgoKSA9PiAoe1xuICAgICAgICBjYW1lcmE6IGNhbWVyYVJlZi5jdXJyZW50XG4gICAgICB9KSk7XG4gICAgICByZXR1cm4gKCkgPT4gc2V0KCgpID0+ICh7XG4gICAgICAgIGNhbWVyYTogb2xkQ2FtXG4gICAgICB9KSk7XG4gICAgfVxuICAgIC8vIFRoZSBjYW1lcmEgc2hvdWxkIG5vdCBiZSBwYXJ0IG9mIHRoZSBkZXBlbmRlbmN5IGxpc3QgYmVjYXVzZSB0aGlzIGNvbXBvbmVudHMgY2FtZXJhIGlzIGEgc3RhYmxlIHJlZmVyZW5jZVxuICAgIC8vIHRoYXQgbXVzdCBleGNoYW5nZSB0aGUgZGVmYXVsdCwgYW5kIGNsZWFuIHVwIGFmdGVyIGl0c2VsZiBvbiB1bm1vdW50LlxuICB9LCBbY2FtZXJhUmVmLCBtYWtlRGVmYXVsdCwgc2V0XSk7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChSZWFjdC5GcmFnbWVudCwgbnVsbCwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJwZXJzcGVjdGl2ZUNhbWVyYVwiLCBfZXh0ZW5kcyh7XG4gICAgcmVmOiBtZXJnZVJlZnMoW2NhbWVyYVJlZiwgcmVmXSlcbiAgfSwgcHJvcHMpLCAhZnVuY3Rpb25hbCAmJiBjaGlsZHJlbiksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwiZ3JvdXBcIiwge1xuICAgIHJlZjogZ3JvdXBSZWZcbiAgfSwgZnVuY3Rpb25hbCAmJiBjaGlsZHJlbihmYm8udGV4dHVyZSkpKTtcbn0pO1xuXG5leHBvcnQgeyBQZXJzcGVjdGl2ZUNhbWVyYSB9O1xuIl0sIm5hbWVzIjpbIl9leHRlbmRzIiwiUmVhY3QiLCJ1c2VUaHJlZSIsInVzZUZyYW1lIiwibWVyZ2VSZWZzIiwidXNlRkJPIiwiaXNGdW5jdGlvbiIsIm5vZGUiLCJQZXJzcGVjdGl2ZUNhbWVyYSIsImZvcndhcmRSZWYiLCJlbnZNYXAiLCJyZXNvbHV0aW9uIiwiZnJhbWVzIiwiSW5maW5pdHkiLCJtYWtlRGVmYXVsdCIsImNoaWxkcmVuIiwicHJvcHMiLCJyZWYiLCJzZXQiLCJjYW1lcmEiLCJzaXplIiwiY2FtZXJhUmVmIiwidXNlUmVmIiwiZ3JvdXBSZWYiLCJmYm8iLCJ1c2VMYXlvdXRFZmZlY3QiLCJtYW51YWwiLCJjdXJyZW50IiwiYXNwZWN0Iiwid2lkdGgiLCJoZWlnaHQiLCJ1cGRhdGVQcm9qZWN0aW9uTWF0cml4IiwiY291bnQiLCJvbGRFbnZNYXAiLCJmdW5jdGlvbmFsIiwic3RhdGUiLCJ2aXNpYmxlIiwiZ2wiLCJzZXRSZW5kZXJUYXJnZXQiLCJzY2VuZSIsImJhY2tncm91bmQiLCJyZW5kZXIiLCJvbGRDYW0iLCJjcmVhdGVFbGVtZW50IiwiRnJhZ21lbnQiLCJ0ZXh0dXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/PerspectiveCamera.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/core/useFBO.js":
/*!*******************************************************!*\
  !*** ./node_modules/@react-three/drei/core/useFBO.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFBO: () => (/* binding */ useFBO)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/index-5918012a.esm.js\");\n\n\n\n// 👇 uncomment when TS version supports function overloads\n// export function useFBO(settings?: FBOSettings)\nfunction useFBO(/** Width in pixels, or settings (will render fullscreen by default) */ width, /** Height in pixels */ height, /**Settings */ settings) {\n    const size = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.A)((state)=>state.size);\n    const viewport = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.A)((state)=>state.viewport);\n    const _width = typeof width === \"number\" ? width : size.width * viewport.dpr;\n    const _height = typeof height === \"number\" ? height : size.height * viewport.dpr;\n    const _settings = (typeof width === \"number\" ? settings : width) || {};\n    const { samples = 0, depth, ...targetSettings } = _settings;\n    const target = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        const target = new three__WEBPACK_IMPORTED_MODULE_2__.WebGLRenderTarget(_width, _height, {\n            minFilter: three__WEBPACK_IMPORTED_MODULE_2__.LinearFilter,\n            magFilter: three__WEBPACK_IMPORTED_MODULE_2__.LinearFilter,\n            type: three__WEBPACK_IMPORTED_MODULE_2__.HalfFloatType,\n            ...targetSettings\n        });\n        if (depth) {\n            target.depthTexture = new three__WEBPACK_IMPORTED_MODULE_2__.DepthTexture(_width, _height, three__WEBPACK_IMPORTED_MODULE_2__.FloatType);\n        }\n        target.samples = samples;\n        return target;\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(()=>{\n        target.setSize(_width, _height);\n        if (samples) target.samples = samples;\n    }, [\n        samples,\n        target,\n        _width,\n        _height\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>target.dispose();\n    }, []);\n    return target;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/useFBO.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/index-5918012a.esm.js":
/*!********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/index-5918012a.esm.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ useThree),\n/* harmony export */   B: () => (/* binding */ Block),\n/* harmony export */   C: () => (/* binding */ useFrame),\n/* harmony export */   D: () => (/* binding */ useGraph),\n/* harmony export */   E: () => (/* binding */ ErrorBoundary),\n/* harmony export */   F: () => (/* binding */ useLoader),\n/* harmony export */   a: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   b: () => (/* binding */ createRoot),\n/* harmony export */   c: () => (/* binding */ createEvents),\n/* harmony export */   d: () => (/* binding */ unmountComponentAtNode),\n/* harmony export */   e: () => (/* binding */ extend),\n/* harmony export */   f: () => (/* binding */ context),\n/* harmony export */   g: () => (/* binding */ createPortal),\n/* harmony export */   h: () => (/* binding */ reconciler),\n/* harmony export */   i: () => (/* binding */ isRef),\n/* harmony export */   j: () => (/* binding */ applyProps),\n/* harmony export */   k: () => (/* binding */ dispose),\n/* harmony export */   l: () => (/* binding */ invalidate),\n/* harmony export */   m: () => (/* binding */ advance),\n/* harmony export */   n: () => (/* binding */ addEffect),\n/* harmony export */   o: () => (/* binding */ addAfterEffect),\n/* harmony export */   p: () => (/* binding */ addTail),\n/* harmony export */   q: () => (/* binding */ flushGlobalEffects),\n/* harmony export */   r: () => (/* binding */ render),\n/* harmony export */   s: () => (/* binding */ getRootState),\n/* harmony export */   t: () => (/* binding */ threeTypes),\n/* harmony export */   u: () => (/* binding */ useMutableCallback),\n/* harmony export */   v: () => (/* binding */ act),\n/* harmony export */   w: () => (/* binding */ buildGraph),\n/* harmony export */   x: () => (/* binding */ roots),\n/* harmony export */   y: () => (/* binding */ useInstanceHandle),\n/* harmony export */   z: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/zustand/esm/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\");\n/* harmony import */ var suspend_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! suspend-react */ \"(ssr)/./node_modules/suspend-react/index.js\");\n\n\n\n\n\n\n\nvar threeTypes = /*#__PURE__*/ Object.freeze({\n    __proto__: null\n});\nconst catalogue = {};\nconst extend = (objects)=>void Object.assign(catalogue, objects);\nfunction createRenderer(_roots, _getEventPriority) {\n    function createInstance(type, { args = [], attach, ...props }, root) {\n        let name = `${type[0].toUpperCase()}${type.slice(1)}`;\n        let instance;\n        if (type === \"primitive\") {\n            if (props.object === undefined) throw new Error(\"R3F: Primitives without 'object' are invalid!\");\n            const object = props.object;\n            instance = prepare(object, {\n                type,\n                root,\n                attach,\n                primitive: true\n            });\n        } else {\n            const target = catalogue[name];\n            if (!target) {\n                throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n            }\n            // Throw if an object or literal was passed for args\n            if (!Array.isArray(args)) throw new Error(\"R3F: The args prop must be an array!\");\n            // Instanciate new object, link it to the root\n            // Append memoized props with args so it's not forgotten\n            instance = prepare(new target(...args), {\n                type,\n                root,\n                attach,\n                // Save args in case we need to reconstruct later for HMR\n                memoizedProps: {\n                    args\n                }\n            });\n        }\n        // Auto-attach geometries and materials\n        if (instance.__r3f.attach === undefined) {\n            if (instance instanceof three__WEBPACK_IMPORTED_MODULE_4__.BufferGeometry) instance.__r3f.attach = \"geometry\";\n            else if (instance instanceof three__WEBPACK_IMPORTED_MODULE_4__.Material) instance.__r3f.attach = \"material\";\n        }\n        // It should NOT call onUpdate on object instanciation, because it hasn't been added to the\n        // view yet. If the callback relies on references for instance, they won't be ready yet, this is\n        // why it passes \"true\" here\n        // There is no reason to apply props to injects\n        if (name !== \"inject\") applyProps$1(instance, props);\n        return instance;\n    }\n    function appendChild(parentInstance, child) {\n        let added = false;\n        if (child) {\n            var _child$__r3f, _parentInstance$__r3f;\n            // The attach attribute implies that the object attaches itself on the parent\n            if ((_child$__r3f = child.__r3f) != null && _child$__r3f.attach) {\n                attach(parentInstance, child, child.__r3f.attach);\n            } else if (child.isObject3D && parentInstance.isObject3D) {\n                // add in the usual parent-child way\n                parentInstance.add(child);\n                added = true;\n            }\n            // This is for anything that used attach, and for non-Object3Ds that don't get attached to props;\n            // that is, anything that's a child in React but not a child in the scenegraph.\n            if (!added) (_parentInstance$__r3f = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f.objects.push(child);\n            if (!child.__r3f) prepare(child, {});\n            child.__r3f.parent = parentInstance;\n            updateInstance(child);\n            invalidateInstance(child);\n        }\n    }\n    function insertBefore(parentInstance, child, beforeChild) {\n        let added = false;\n        if (child) {\n            var _child$__r3f2, _parentInstance$__r3f2;\n            if ((_child$__r3f2 = child.__r3f) != null && _child$__r3f2.attach) {\n                attach(parentInstance, child, child.__r3f.attach);\n            } else if (child.isObject3D && parentInstance.isObject3D) {\n                child.parent = parentInstance;\n                child.dispatchEvent({\n                    type: \"added\"\n                });\n                const restSiblings = parentInstance.children.filter((sibling)=>sibling !== child);\n                const index = restSiblings.indexOf(beforeChild);\n                parentInstance.children = [\n                    ...restSiblings.slice(0, index),\n                    child,\n                    ...restSiblings.slice(index)\n                ];\n                added = true;\n            }\n            if (!added) (_parentInstance$__r3f2 = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f2.objects.push(child);\n            if (!child.__r3f) prepare(child, {});\n            child.__r3f.parent = parentInstance;\n            updateInstance(child);\n            invalidateInstance(child);\n        }\n    }\n    function removeRecursive(array, parent, dispose = false) {\n        if (array) [\n            ...array\n        ].forEach((child)=>removeChild(parent, child, dispose));\n    }\n    function removeChild(parentInstance, child, dispose) {\n        if (child) {\n            var _parentInstance$__r3f3, _child$__r3f3, _child$__r3f5;\n            // Clear the parent reference\n            if (child.__r3f) child.__r3f.parent = null;\n            // Remove child from the parents objects\n            if ((_parentInstance$__r3f3 = parentInstance.__r3f) != null && _parentInstance$__r3f3.objects) parentInstance.__r3f.objects = parentInstance.__r3f.objects.filter((x)=>x !== child);\n            // Remove attachment\n            if ((_child$__r3f3 = child.__r3f) != null && _child$__r3f3.attach) {\n                detach(parentInstance, child, child.__r3f.attach);\n            } else if (child.isObject3D && parentInstance.isObject3D) {\n                var _child$__r3f4;\n                parentInstance.remove(child);\n                // Remove interactivity\n                if ((_child$__r3f4 = child.__r3f) != null && _child$__r3f4.root) {\n                    removeInteractivity(child.__r3f.root, child);\n                }\n            }\n            // Allow objects to bail out of recursive dispose altogether by passing dispose={null}\n            // Never dispose of primitives because their state may be kept outside of React!\n            // In order for an object to be able to dispose it has to have\n            //   - a dispose method,\n            //   - it cannot be a <primitive object={...} />\n            //   - it cannot be a THREE.Scene, because three has broken it's own api\n            //\n            // Since disposal is recursive, we can check the optional dispose arg, which will be undefined\n            // when the reconciler calls it, but then carry our own check recursively\n            const isPrimitive = (_child$__r3f5 = child.__r3f) == null ? void 0 : _child$__r3f5.primitive;\n            const shouldDispose = dispose === undefined ? child.dispose !== null && !isPrimitive : dispose;\n            // Remove nested child objects. Primitives should not have objects and children that are\n            // attached to them declaratively ...\n            if (!isPrimitive) {\n                var _child$__r3f6;\n                removeRecursive((_child$__r3f6 = child.__r3f) == null ? void 0 : _child$__r3f6.objects, child, shouldDispose);\n                removeRecursive(child.children, child, shouldDispose);\n            }\n            // Remove references\n            delete child.__r3f;\n            // Dispose item whenever the reconciler feels like it\n            if (shouldDispose && child.dispose && child.type !== \"Scene\") {\n                (0,scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_scheduleCallback)(scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_IdlePriority, ()=>{\n                    try {\n                        child.dispose();\n                    } catch (e) {\n                    /* ... */ }\n                });\n            }\n            invalidateInstance(parentInstance);\n        }\n    }\n    function switchInstance(instance, type, newProps, fiber) {\n        var _instance$__r3f;\n        const parent = (_instance$__r3f = instance.__r3f) == null ? void 0 : _instance$__r3f.parent;\n        if (!parent) return;\n        const newInstance = createInstance(type, newProps, instance.__r3f.root);\n        // https://github.com/pmndrs/react-three-fiber/issues/1348\n        // When args change the instance has to be re-constructed, which then\n        // forces r3f to re-parent the children and non-scene objects\n        if (instance.children) {\n            for (const child of instance.children){\n                if (child.__r3f) appendChild(newInstance, child);\n            }\n            instance.children = instance.children.filter((child)=>!child.__r3f);\n        }\n        instance.__r3f.objects.forEach((child)=>appendChild(newInstance, child));\n        instance.__r3f.objects = [];\n        if (!instance.__r3f.autoRemovedBeforeAppend) {\n            removeChild(parent, instance);\n        }\n        if (newInstance.parent) {\n            newInstance.__r3f.autoRemovedBeforeAppend = true;\n        }\n        appendChild(parent, newInstance);\n        // Re-bind event handlers\n        if (newInstance.raycast && newInstance.__r3f.eventCount) {\n            const rootState = newInstance.__r3f.root.getState();\n            rootState.internal.interaction.push(newInstance);\n        }\n        [\n            fiber,\n            fiber.alternate\n        ].forEach((fiber)=>{\n            if (fiber !== null) {\n                fiber.stateNode = newInstance;\n                if (fiber.ref) {\n                    if (typeof fiber.ref === \"function\") fiber.ref(newInstance);\n                    else fiber.ref.current = newInstance;\n                }\n            }\n        });\n    }\n    // Don't handle text instances, warn on undefined behavior\n    const handleTextInstance = ()=>console.warn(\"Text is not allowed in the R3F tree! This could be stray whitespace or characters.\");\n    const reconciler = react_reconciler__WEBPACK_IMPORTED_MODULE_2___default()({\n        createInstance,\n        removeChild,\n        appendChild,\n        appendInitialChild: appendChild,\n        insertBefore,\n        supportsMutation: true,\n        isPrimaryRenderer: false,\n        supportsPersistence: false,\n        supportsHydration: false,\n        noTimeout: -1,\n        appendChildToContainer: (container, child)=>{\n            if (!child) return;\n            // Don't append to unmounted container\n            const scene = container.getState().scene;\n            if (!scene.__r3f) return;\n            // Link current root to the default scene\n            scene.__r3f.root = container;\n            appendChild(scene, child);\n        },\n        removeChildFromContainer: (container, child)=>{\n            if (!child) return;\n            removeChild(container.getState().scene, child);\n        },\n        insertInContainerBefore: (container, child, beforeChild)=>{\n            if (!child || !beforeChild) return;\n            // Don't append to unmounted container\n            const scene = container.getState().scene;\n            if (!scene.__r3f) return;\n            insertBefore(scene, child, beforeChild);\n        },\n        getRootHostContext: ()=>null,\n        getChildHostContext: (parentHostContext)=>parentHostContext,\n        finalizeInitialChildren (instance) {\n            var _instance$__r3f2;\n            const localState = (_instance$__r3f2 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f2 : {};\n            // https://github.com/facebook/react/issues/20271\n            // Returning true will trigger commitMount\n            return Boolean(localState.handlers);\n        },\n        prepareUpdate (instance, _type, oldProps, newProps) {\n            var _instance$__r3f3;\n            const localState = (_instance$__r3f3 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f3 : {};\n            // Create diff-sets\n            if (localState.primitive && newProps.object && newProps.object !== instance) {\n                return [\n                    true\n                ];\n            } else {\n                // This is a data object, let's extract critical information about it\n                const { args: argsNew = [], children: cN, ...restNew } = newProps;\n                const { args: argsOld = [], children: cO, ...restOld } = oldProps;\n                // Throw if an object or literal was passed for args\n                if (!Array.isArray(argsNew)) throw new Error(\"R3F: the args prop must be an array!\");\n                // If it has new props or arguments, then it needs to be re-instantiated\n                if (argsNew.some((value, index)=>value !== argsOld[index])) return [\n                    true\n                ];\n                // Create a diff-set, flag if there are any changes\n                const diff = diffProps(instance, restNew, restOld, true);\n                if (diff.changes.length) return [\n                    false,\n                    diff\n                ];\n                // Otherwise do not touch the instance\n                return null;\n            }\n        },\n        commitUpdate (instance, [reconstruct, diff], type, _oldProps, newProps, fiber) {\n            // Reconstruct when args or <primitive object={...} have changes\n            if (reconstruct) switchInstance(instance, type, newProps, fiber);\n            else applyProps$1(instance, diff);\n        },\n        commitMount (instance, _type, _props, _int) {\n            var _instance$__r3f4;\n            // https://github.com/facebook/react/issues/20271\n            // This will make sure events are only added once to the central container\n            const localState = (_instance$__r3f4 = instance.__r3f) != null ? _instance$__r3f4 : {};\n            if (instance.raycast && localState.handlers && localState.eventCount) {\n                instance.__r3f.root.getState().internal.interaction.push(instance);\n            }\n        },\n        getPublicInstance: (instance)=>instance,\n        prepareForCommit: ()=>null,\n        preparePortalMount: (container)=>prepare(container.getState().scene),\n        resetAfterCommit: ()=>{},\n        shouldSetTextContent: ()=>false,\n        clearContainer: ()=>false,\n        hideInstance (instance) {\n            var _instance$__r3f5;\n            // Detach while the instance is hidden\n            const { attach: type, parent } = (_instance$__r3f5 = instance.__r3f) != null ? _instance$__r3f5 : {};\n            if (type && parent) detach(parent, instance, type);\n            if (instance.isObject3D) instance.visible = false;\n            invalidateInstance(instance);\n        },\n        unhideInstance (instance, props) {\n            var _instance$__r3f6;\n            // Re-attach when the instance is unhidden\n            const { attach: type, parent } = (_instance$__r3f6 = instance.__r3f) != null ? _instance$__r3f6 : {};\n            if (type && parent) attach(parent, instance, type);\n            if (instance.isObject3D && props.visible == null || props.visible) instance.visible = true;\n            invalidateInstance(instance);\n        },\n        createTextInstance: handleTextInstance,\n        hideTextInstance: handleTextInstance,\n        unhideTextInstance: handleTextInstance,\n        // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r916356874\n        // @ts-ignore\n        getCurrentEventPriority: ()=>_getEventPriority ? _getEventPriority() : react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority,\n        beforeActiveInstanceBlur: ()=>{},\n        afterActiveInstanceBlur: ()=>{},\n        detachDeletedInstance: ()=>{},\n        now: typeof performance !== \"undefined\" && is.fun(performance.now) ? performance.now : is.fun(Date.now) ? Date.now : ()=>0,\n        // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r920883503\n        scheduleTimeout: is.fun(setTimeout) ? setTimeout : undefined,\n        cancelTimeout: is.fun(clearTimeout) ? clearTimeout : undefined\n    });\n    return {\n        reconciler,\n        applyProps: applyProps$1\n    };\n}\nvar _window$document, _window$navigator;\n/**\r\n * Returns `true` with correct TS type inference if an object has a configurable color space (since r152).\r\n */ const hasColorSpace = (object)=>\"colorSpace\" in object || \"outputColorSpace\" in object;\n/**\r\n * The current THREE.ColorManagement instance, if present.\r\n */ const getColorManagement = ()=>{\n    var _ColorManagement;\n    return (_ColorManagement = catalogue.ColorManagement) != null ? _ColorManagement : null;\n};\nconst isOrthographicCamera = (def)=>def && def.isOrthographicCamera;\nconst isRef = (obj)=>obj && obj.hasOwnProperty(\"current\");\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */ const useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMutableCallback(fn) {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n    useIsomorphicLayoutEffect(()=>void (ref.current = fn), [\n        fn\n    ]);\n    return ref;\n}\nfunction Block({ set }) {\n    useIsomorphicLayoutEffect(()=>{\n        set(new Promise(()=>null));\n        return ()=>set(false);\n    }, [\n        set\n    ]);\n    return null;\n}\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor(...args){\n        super(...args);\n        this.state = {\n            error: false\n        };\n    }\n    componentDidCatch(err) {\n        this.props.set(err);\n    }\n    render() {\n        return this.state.error ? null : this.props.children;\n    }\n}\nErrorBoundary.getDerivedStateFromError = ()=>({\n        error: true\n    });\nconst DEFAULT = \"__default\";\nconst DEFAULTS = new Map();\nconst isDiffSet = (def)=>def && !!def.memoized && !!def.changes;\nfunction calculateDpr(dpr) {\n    var _window$devicePixelRa;\n    // Err on the side of progress by assuming 2x dpr if we can't detect it\n    // This will happen in workers where window is defined but dpr isn't.\n    const target =  false ? 0 : 1;\n    return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n/**\r\n * Returns instance root state\r\n */ const getRootState = (obj)=>{\n    var _r3f;\n    return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n};\n// A collection of compare functions\nconst is = {\n    obj: (a)=>a === Object(a) && !is.arr(a) && typeof a !== \"function\",\n    fun: (a)=>typeof a === \"function\",\n    str: (a)=>typeof a === \"string\",\n    num: (a)=>typeof a === \"number\",\n    boo: (a)=>typeof a === \"boolean\",\n    und: (a)=>a === void 0,\n    arr: (a)=>Array.isArray(a),\n    equ (a, b, { arrays = \"shallow\", objects = \"reference\", strict = true } = {}) {\n        // Wrong type or one of the two undefined, doesn't match\n        if (typeof a !== typeof b || !!a !== !!b) return false;\n        // Atomic, just compare a against b\n        if (is.str(a) || is.num(a)) return a === b;\n        const isObj = is.obj(a);\n        if (isObj && objects === \"reference\") return a === b;\n        const isArr = is.arr(a);\n        if (isArr && arrays === \"reference\") return a === b;\n        // Array or Object, shallow compare first to see if it's a match\n        if ((isArr || isObj) && a === b) return true;\n        // Last resort, go through keys\n        let i;\n        // Check if a has all the keys of b\n        for(i in a)if (!(i in b)) return false;\n        // Check if values between keys match\n        if (isObj && arrays === \"shallow\" && objects === \"shallow\") {\n            for(i in strict ? b : a)if (!is.equ(a[i], b[i], {\n                strict,\n                objects: \"reference\"\n            })) return false;\n        } else {\n            for(i in strict ? b : a)if (a[i] !== b[i]) return false;\n        }\n        // If i is undefined\n        if (is.und(i)) {\n            // If both arrays are empty we consider them equal\n            if (isArr && a.length === 0 && b.length === 0) return true;\n            // If both objects are empty we consider them equal\n            if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n            // Otherwise match them by value\n            if (a !== b) return false;\n        }\n        return true;\n    }\n};\n/**\r\n * Collects nodes and materials from a THREE.Object3D.\r\n */ function buildGraph(object) {\n    const data = {\n        nodes: {},\n        materials: {}\n    };\n    if (object) {\n        object.traverse((obj)=>{\n            if (obj.name) data.nodes[obj.name] = obj;\n            if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n        });\n    }\n    return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n    if (obj.dispose && obj.type !== \"Scene\") obj.dispose();\n    for(const p in obj){\n        p.dispose == null ? void 0 : p.dispose();\n        delete obj[p];\n    }\n}\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(object, state) {\n    const instance = object;\n    instance.__r3f = {\n        type: \"\",\n        root: null,\n        previousAttach: null,\n        memoizedProps: {},\n        eventCount: 0,\n        handlers: {},\n        objects: [],\n        parent: null,\n        ...state\n    };\n    return object;\n}\nfunction resolve(instance, key) {\n    let target = instance;\n    if (key.includes(\"-\")) {\n        const entries = key.split(\"-\");\n        const last = entries.pop();\n        target = entries.reduce((acc, key)=>acc[key], instance);\n        return {\n            target,\n            key: last\n        };\n    } else return {\n        target,\n        key\n    };\n}\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child, type) {\n    if (is.str(type)) {\n        // If attaching into an array (foo-0), create one\n        if (INDEX_REGEX.test(type)) {\n            const root = type.replace(INDEX_REGEX, \"\");\n            const { target, key } = resolve(parent, root);\n            if (!Array.isArray(target[key])) target[key] = [];\n        }\n        const { target, key } = resolve(parent, type);\n        child.__r3f.previousAttach = target[key];\n        target[key] = child;\n    } else child.__r3f.previousAttach = type(parent, child);\n}\nfunction detach(parent, child, type) {\n    var _child$__r3f, _child$__r3f2;\n    if (is.str(type)) {\n        const { target, key } = resolve(parent, type);\n        const previous = child.__r3f.previousAttach;\n        // When the previous value was undefined, it means the value was never set to begin with\n        if (previous === undefined) delete target[key];\n        else target[key] = previous;\n    } else (_child$__r3f = child.__r3f) == null ? void 0 : _child$__r3f.previousAttach == null ? void 0 : _child$__r3f.previousAttach(parent, child);\n    (_child$__r3f2 = child.__r3f) == null ? true : delete _child$__r3f2.previousAttach;\n}\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, { children: cN, key: kN, ref: rN, ...props }, { children: cP, key: kP, ref: rP, ...previous } = {}, remove = false) {\n    var _instance$__r3f;\n    const localState = (_instance$__r3f = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f : {};\n    const entries = Object.entries(props);\n    const changes = [];\n    // Catch removed props, prepend them so they can be reset or removed\n    if (remove) {\n        const previousKeys = Object.keys(previous);\n        for(let i = 0; i < previousKeys.length; i++){\n            if (!props.hasOwnProperty(previousKeys[i])) entries.unshift([\n                previousKeys[i],\n                DEFAULT + \"remove\"\n            ]);\n        }\n    }\n    entries.forEach(([key, value])=>{\n        var _instance$__r3f2;\n        // Bail out on primitive object\n        if ((_instance$__r3f2 = instance.__r3f) != null && _instance$__r3f2.primitive && key === \"object\") return;\n        // When props match bail out\n        if (is.equ(value, previous[key])) return;\n        // Collect handlers and bail out\n        if (/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/.test(key)) return changes.push([\n            key,\n            value,\n            true,\n            []\n        ]);\n        // Split dashed props\n        let entries = [];\n        if (key.includes(\"-\")) entries = key.split(\"-\");\n        changes.push([\n            key,\n            value,\n            false,\n            entries\n        ]);\n        // Reset pierced props\n        for(const prop in props){\n            const value = props[prop];\n            if (prop.startsWith(`${key}-`)) changes.push([\n                prop,\n                value,\n                false,\n                prop.split(\"-\")\n            ]);\n        }\n    });\n    const memoized = {\n        ...props\n    };\n    if (localState.memoizedProps && localState.memoizedProps.args) memoized.args = localState.memoizedProps.args;\n    if (localState.memoizedProps && localState.memoizedProps.attach) memoized.attach = localState.memoizedProps.attach;\n    return {\n        memoized,\n        changes\n    };\n}\nconst __DEV__ = typeof process !== \"undefined\" && \"development\" !== \"production\";\n// This function applies a set of changes to the instance\nfunction applyProps$1(instance, data) {\n    var _instance$__r3f3, _root$getState, _instance$__r3f4;\n    // Filter equals, events and reserved props\n    const localState = (_instance$__r3f3 = instance.__r3f) != null ? _instance$__r3f3 : {};\n    const root = localState.root;\n    const rootState = (_root$getState = root == null ? void 0 : root.getState == null ? void 0 : root.getState()) != null ? _root$getState : {};\n    const { memoized, changes } = isDiffSet(data) ? data : diffProps(instance, data);\n    const prevHandlers = localState.eventCount;\n    // Prepare memoized props\n    if (instance.__r3f) instance.__r3f.memoizedProps = memoized;\n    for(let i = 0; i < changes.length; i++){\n        let [key, value, isEvent, keys] = changes[i];\n        // Alias (output)encoding => (output)colorSpace (since r152)\n        // https://github.com/pmndrs/react-three-fiber/pull/2829\n        if (hasColorSpace(instance)) {\n            const sRGBEncoding = 3001;\n            const SRGBColorSpace = \"srgb\";\n            const LinearSRGBColorSpace = \"srgb-linear\";\n            if (key === \"encoding\") {\n                key = \"colorSpace\";\n                value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n            } else if (key === \"outputEncoding\") {\n                key = \"outputColorSpace\";\n                value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n            }\n        }\n        let currentInstance = instance;\n        let targetProp = currentInstance[key];\n        // Revolve dashed props\n        if (keys.length) {\n            targetProp = keys.reduce((acc, key)=>acc[key], instance);\n            // If the target is atomic, it forces us to switch the root\n            if (!(targetProp && targetProp.set)) {\n                const [name, ...reverseEntries] = keys.reverse();\n                currentInstance = reverseEntries.reverse().reduce((acc, key)=>acc[key], instance);\n                key = name;\n            }\n        }\n        // https://github.com/mrdoob/three.js/issues/21209\n        // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n        // has no means to do this. Hence we curate a small collection of value-classes\n        // with their respective constructor/set arguments\n        // For removed props, try to set default values, if possible\n        if (value === DEFAULT + \"remove\") {\n            if (currentInstance.constructor) {\n                // create a blank slate of the instance and copy the particular parameter.\n                let ctor = DEFAULTS.get(currentInstance.constructor);\n                if (!ctor) {\n                    // @ts-ignore\n                    ctor = new currentInstance.constructor();\n                    DEFAULTS.set(currentInstance.constructor, ctor);\n                }\n                value = ctor[key];\n            } else {\n                // instance does not have constructor, just set it to 0\n                value = 0;\n            }\n        }\n        // Deal with pointer events ...\n        if (isEvent) {\n            if (value) localState.handlers[key] = value;\n            else delete localState.handlers[key];\n            localState.eventCount = Object.keys(localState.handlers).length;\n        } else if (targetProp && targetProp.set && (targetProp.copy || targetProp instanceof three__WEBPACK_IMPORTED_MODULE_4__.Layers)) {\n            // If value is an array\n            if (Array.isArray(value)) {\n                if (targetProp.fromArray) targetProp.fromArray(value);\n                else targetProp.set(...value);\n            } else if (targetProp.copy && value && value.constructor && // Some environments may break strict identity checks by duplicating versions of three.js.\n            // Loosen to unminified names, ignoring descendents.\n            // https://github.com/pmndrs/react-three-fiber/issues/2856\n            // TODO: fix upstream and remove in v9\n            (__DEV__ ? targetProp.constructor.name === value.constructor.name : targetProp.constructor === value.constructor)) {\n                targetProp.copy(value);\n            } else if (value !== undefined) {\n                const isColor = targetProp instanceof three__WEBPACK_IMPORTED_MODULE_4__.Color;\n                // Allow setting array scalars\n                if (!isColor && targetProp.setScalar) targetProp.setScalar(value);\n                else if (targetProp instanceof three__WEBPACK_IMPORTED_MODULE_4__.Layers && value instanceof three__WEBPACK_IMPORTED_MODULE_4__.Layers) targetProp.mask = value.mask;\n                else targetProp.set(value);\n                // For versions of three which don't support THREE.ColorManagement,\n                // Auto-convert sRGB colors\n                // https://github.com/pmndrs/react-three-fiber/issues/344\n                if (!getColorManagement() && !rootState.linear && isColor) targetProp.convertSRGBToLinear();\n            }\n        // Else, just overwrite the value\n        } else {\n            currentInstance[key] = value;\n            // Auto-convert sRGB textures, for now ...\n            // https://github.com/pmndrs/react-three-fiber/issues/344\n            if (currentInstance[key] instanceof three__WEBPACK_IMPORTED_MODULE_4__.Texture && // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n            currentInstance[key].format === three__WEBPACK_IMPORTED_MODULE_4__.RGBAFormat && currentInstance[key].type === three__WEBPACK_IMPORTED_MODULE_4__.UnsignedByteType) {\n                const texture = currentInstance[key];\n                if (hasColorSpace(texture) && hasColorSpace(rootState.gl)) texture.colorSpace = rootState.gl.outputColorSpace;\n                else texture.encoding = rootState.gl.outputEncoding;\n            }\n        }\n        invalidateInstance(instance);\n    }\n    if (localState.parent && rootState.internal && instance.raycast && prevHandlers !== localState.eventCount) {\n        // Pre-emptively remove the instance from the interaction manager\n        const index = rootState.internal.interaction.indexOf(instance);\n        if (index > -1) rootState.internal.interaction.splice(index, 1);\n        // Add the instance to the interaction manager only when it has handlers\n        if (localState.eventCount) rootState.internal.interaction.push(instance);\n    }\n    // Call the update lifecycle when it is being updated, but only when it is part of the scene.\n    // Skip updates to the `onUpdate` prop itself\n    const isCircular = changes.length === 1 && changes[0][0] === \"onUpdate\";\n    if (!isCircular && changes.length && (_instance$__r3f4 = instance.__r3f) != null && _instance$__r3f4.parent) updateInstance(instance);\n    return instance;\n}\nfunction invalidateInstance(instance) {\n    var _instance$__r3f5, _instance$__r3f5$root;\n    const state = (_instance$__r3f5 = instance.__r3f) == null ? void 0 : (_instance$__r3f5$root = _instance$__r3f5.root) == null ? void 0 : _instance$__r3f5$root.getState == null ? void 0 : _instance$__r3f5$root.getState();\n    if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateInstance(instance) {\n    instance.onUpdate == null ? void 0 : instance.onUpdate(instance);\n}\nfunction updateCamera(camera, size) {\n    // https://github.com/pmndrs/react-three-fiber/issues/92\n    // Do not mess with the camera if it belongs to the user\n    if (!camera.manual) {\n        if (isOrthographicCamera(camera)) {\n            camera.left = size.width / -2;\n            camera.right = size.width / 2;\n            camera.top = size.height / 2;\n            camera.bottom = size.height / -2;\n        } else {\n            camera.aspect = size.width / size.height;\n        }\n        camera.updateProjectionMatrix();\n        // https://github.com/pmndrs/react-three-fiber/issues/178\n        // Update matrix world since the renderer is a frame late\n        camera.updateMatrixWorld();\n    }\n}\nfunction makeId(event) {\n    return (event.eventObject || event.object).uuid + \"/\" + event.index + event.instanceId;\n}\n// https://github.com/facebook/react/tree/main/packages/react-reconciler#getcurrenteventpriority\n// Gives React a clue as to how import the current interaction is\nfunction getEventPriority() {\n    var _globalScope$event;\n    // Get a handle to the current global scope in window and worker contexts if able\n    // https://github.com/pmndrs/react-three-fiber/pull/2493\n    const globalScope = typeof self !== \"undefined\" && self ||  false && 0;\n    if (!globalScope) return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n    const name = (_globalScope$event = globalScope.event) == null ? void 0 : _globalScope$event.type;\n    switch(name){\n        case \"click\":\n        case \"contextmenu\":\n        case \"dblclick\":\n        case \"pointercancel\":\n        case \"pointerdown\":\n        case \"pointerup\":\n            return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DiscreteEventPriority;\n        case \"pointermove\":\n        case \"pointerout\":\n        case \"pointerover\":\n        case \"pointerenter\":\n        case \"pointerleave\":\n        case \"wheel\":\n            return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ContinuousEventPriority;\n        default:\n            return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n    }\n}\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */ function releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n    const captureData = captures.get(obj);\n    if (captureData) {\n        captures.delete(obj);\n        // If this was the last capturing object for this pointer\n        if (captures.size === 0) {\n            capturedMap.delete(pointerId);\n            captureData.target.releasePointerCapture(pointerId);\n        }\n    }\n}\nfunction removeInteractivity(store, object) {\n    const { internal } = store.getState();\n    // Removes every trace of an object from the data store\n    internal.interaction = internal.interaction.filter((o)=>o !== object);\n    internal.initialHits = internal.initialHits.filter((o)=>o !== object);\n    internal.hovered.forEach((value, key)=>{\n        if (value.eventObject === object || value.object === object) {\n            // Clear out intersects, they are outdated by now\n            internal.hovered.delete(key);\n        }\n    });\n    internal.capturedMap.forEach((captures, pointerId)=>{\n        releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n    });\n}\nfunction createEvents(store) {\n    /** Calculates delta */ function calculateDistance(event) {\n        const { internal } = store.getState();\n        const dx = event.offsetX - internal.initialClick[0];\n        const dy = event.offsetY - internal.initialClick[1];\n        return Math.round(Math.sqrt(dx * dx + dy * dy));\n    }\n    /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */ function filterPointerEvents(objects) {\n        return objects.filter((obj)=>[\n                \"Move\",\n                \"Over\",\n                \"Enter\",\n                \"Out\",\n                \"Leave\"\n            ].some((name)=>{\n                var _r3f;\n                return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers[\"onPointer\" + name];\n            }));\n    }\n    function intersect(event, filter) {\n        const state = store.getState();\n        const duplicates = new Set();\n        const intersections = [];\n        // Allow callers to eliminate event objects\n        const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n        // Reset all raycaster cameras to undefined\n        for(let i = 0; i < eventsObjects.length; i++){\n            const state = getRootState(eventsObjects[i]);\n            if (state) {\n                state.raycaster.camera = undefined;\n            }\n        }\n        if (!state.previousRoot) {\n            // Make sure root-level pointer and ray are set up\n            state.events.compute == null ? void 0 : state.events.compute(event, state);\n        }\n        function handleRaycast(obj) {\n            const state = getRootState(obj);\n            // Skip event handling when noEvents is set, or when the raycasters camera is null\n            if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n            // When the camera is undefined we have to call the event layers update function\n            if (state.raycaster.camera === undefined) {\n                var _state$previousRoot;\n                state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n                // If the camera is still undefined we have to skip this layer entirely\n                if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n            }\n            // Intersect object by object\n            return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n        }\n        // Collect events\n        let hits = eventsObjects// Intersect objects\n        .flatMap(handleRaycast)// Sort by event priority and distance\n        .sort((a, b)=>{\n            const aState = getRootState(a.object);\n            const bState = getRootState(b.object);\n            if (!aState || !bState) return a.distance - b.distance;\n            return bState.events.priority - aState.events.priority || a.distance - b.distance;\n        })// Filter out duplicates\n        .filter((item)=>{\n            const id = makeId(item);\n            if (duplicates.has(id)) return false;\n            duplicates.add(id);\n            return true;\n        });\n        // https://github.com/mrdoob/three.js/issues/16031\n        // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n        if (state.events.filter) hits = state.events.filter(hits, state);\n        // Bubble up the events, find the event source (eventObject)\n        for (const hit of hits){\n            let eventObject = hit.object;\n            // Bubble event up\n            while(eventObject){\n                var _r3f2;\n                if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n                    ...hit,\n                    eventObject\n                });\n                eventObject = eventObject.parent;\n            }\n        }\n        // If the interaction is captured, make all capturing targets part of the intersect.\n        if (\"pointerId\" in event && state.internal.capturedMap.has(event.pointerId)) {\n            for (let captureData of state.internal.capturedMap.get(event.pointerId).values()){\n                if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n            }\n        }\n        return intersections;\n    }\n    /**  Handles intersections by forwarding them to handlers */ function handleIntersects(intersections, event, delta, callback) {\n        const rootState = store.getState();\n        // If anything has been found, forward it to the event listeners\n        if (intersections.length) {\n            const localState = {\n                stopped: false\n            };\n            for (const hit of intersections){\n                const state = getRootState(hit.object) || rootState;\n                const { raycaster, pointer, camera, internal } = state;\n                const unprojectedPoint = new three__WEBPACK_IMPORTED_MODULE_4__.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n                const hasPointerCapture = (id)=>{\n                    var _internal$capturedMap, _internal$capturedMap2;\n                    return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n                };\n                const setPointerCapture = (id)=>{\n                    const captureData = {\n                        intersection: hit,\n                        target: event.target\n                    };\n                    if (internal.capturedMap.has(id)) {\n                        // if the pointerId was previously captured, we add the hit to the\n                        // event capturedMap.\n                        internal.capturedMap.get(id).set(hit.eventObject, captureData);\n                    } else {\n                        // if the pointerId was not previously captured, we create a map\n                        // containing the hitObject, and the hit. hitObject is used for\n                        // faster access.\n                        internal.capturedMap.set(id, new Map([\n                            [\n                                hit.eventObject,\n                                captureData\n                            ]\n                        ]));\n                    }\n                    event.target.setPointerCapture(id);\n                };\n                const releasePointerCapture = (id)=>{\n                    const captures = internal.capturedMap.get(id);\n                    if (captures) {\n                        releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n                    }\n                };\n                // Add native event props\n                let extractEventProps = {};\n                // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n                for(let prop in event){\n                    let property = event[prop];\n                    // Only copy over atomics, leave functions alone as these should be\n                    // called as event.nativeEvent.fn()\n                    if (typeof property !== \"function\") extractEventProps[prop] = property;\n                }\n                let raycastEvent = {\n                    ...hit,\n                    ...extractEventProps,\n                    pointer,\n                    intersections,\n                    stopped: localState.stopped,\n                    delta,\n                    unprojectedPoint,\n                    ray: raycaster.ray,\n                    camera: camera,\n                    // Hijack stopPropagation, which just sets a flag\n                    stopPropagation () {\n                        // https://github.com/pmndrs/react-three-fiber/issues/596\n                        // Events are not allowed to stop propagation if the pointer has been captured\n                        const capturesForPointer = \"pointerId\" in event && internal.capturedMap.get(event.pointerId);\n                        // We only authorize stopPropagation...\n                        if (// ...if this pointer hasn't been captured\n                        !capturesForPointer || // ... or if the hit object is capturing the pointer\n                        capturesForPointer.has(hit.eventObject)) {\n                            raycastEvent.stopped = localState.stopped = true;\n                            // Propagation is stopped, remove all other hover records\n                            // An event handler is only allowed to flush other handlers if it is hovered itself\n                            if (internal.hovered.size && Array.from(internal.hovered.values()).find((i)=>i.eventObject === hit.eventObject)) {\n                                // Objects cannot flush out higher up objects that have already caught the event\n                                const higher = intersections.slice(0, intersections.indexOf(hit));\n                                cancelPointer([\n                                    ...higher,\n                                    hit\n                                ]);\n                            }\n                        }\n                    },\n                    // there should be a distinction between target and currentTarget\n                    target: {\n                        hasPointerCapture,\n                        setPointerCapture,\n                        releasePointerCapture\n                    },\n                    currentTarget: {\n                        hasPointerCapture,\n                        setPointerCapture,\n                        releasePointerCapture\n                    },\n                    nativeEvent: event\n                };\n                // Call subscribers\n                callback(raycastEvent);\n                // Event bubbling may be interrupted by stopPropagation\n                if (localState.stopped === true) break;\n            }\n        }\n        return intersections;\n    }\n    function cancelPointer(intersections) {\n        const { internal } = store.getState();\n        for (const hoveredObj of internal.hovered.values()){\n            // When no objects were hit or the the hovered object wasn't found underneath the cursor\n            // we call onPointerOut and delete the object from the hovered-elements map\n            if (!intersections.length || !intersections.find((hit)=>hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n                const eventObject = hoveredObj.eventObject;\n                const instance = eventObject.__r3f;\n                const handlers = instance == null ? void 0 : instance.handlers;\n                internal.hovered.delete(makeId(hoveredObj));\n                if (instance != null && instance.eventCount) {\n                    // Clear out intersects, they are outdated by now\n                    const data = {\n                        ...hoveredObj,\n                        intersections\n                    };\n                    handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n                    handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n                }\n            }\n        }\n    }\n    function pointerMissed(event, objects) {\n        for(let i = 0; i < objects.length; i++){\n            const instance = objects[i].__r3f;\n            instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n        }\n    }\n    function handlePointer(name) {\n        // Deal with cancelation\n        switch(name){\n            case \"onPointerLeave\":\n            case \"onPointerCancel\":\n                return ()=>cancelPointer([]);\n            case \"onLostPointerCapture\":\n                return (event)=>{\n                    const { internal } = store.getState();\n                    if (\"pointerId\" in event && internal.capturedMap.has(event.pointerId)) {\n                        // If the object event interface had onLostPointerCapture, we'd call it here on every\n                        // object that's getting removed. We call it on the next frame because onLostPointerCapture\n                        // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n                        // happen in the object it originated from, leaving components in a in-between state.\n                        requestAnimationFrame(()=>{\n                            // Only release if pointer-up didn't do it already\n                            if (internal.capturedMap.has(event.pointerId)) {\n                                internal.capturedMap.delete(event.pointerId);\n                                cancelPointer([]);\n                            }\n                        });\n                    }\n                };\n        }\n        // Any other pointer goes here ...\n        return function handleEvent(event) {\n            const { onPointerMissed, internal } = store.getState();\n            // prepareRay(event)\n            internal.lastEvent.current = event;\n            // Get fresh intersects\n            const isPointerMove = name === \"onPointerMove\";\n            const isClickEvent = name === \"onClick\" || name === \"onContextMenu\" || name === \"onDoubleClick\";\n            const filter = isPointerMove ? filterPointerEvents : undefined;\n            const hits = intersect(event, filter);\n            const delta = isClickEvent ? calculateDistance(event) : 0;\n            // Save initial coordinates on pointer-down\n            if (name === \"onPointerDown\") {\n                internal.initialClick = [\n                    event.offsetX,\n                    event.offsetY\n                ];\n                internal.initialHits = hits.map((hit)=>hit.eventObject);\n            }\n            // If a click yields no results, pass it back to the user as a miss\n            // Missed events have to come first in order to establish user-land side-effect clean up\n            if (isClickEvent && !hits.length) {\n                if (delta <= 2) {\n                    pointerMissed(event, internal.interaction);\n                    if (onPointerMissed) onPointerMissed(event);\n                }\n            }\n            // Take care of unhover\n            if (isPointerMove) cancelPointer(hits);\n            function onIntersect(data) {\n                const eventObject = data.eventObject;\n                const instance = eventObject.__r3f;\n                const handlers = instance == null ? void 0 : instance.handlers;\n                // Check presence of handlers\n                if (!(instance != null && instance.eventCount)) return;\n                /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/ if (isPointerMove) {\n                    // Move event ...\n                    if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n                        // When enter or out is present take care of hover-state\n                        const id = makeId(data);\n                        const hoveredItem = internal.hovered.get(id);\n                        if (!hoveredItem) {\n                            // If the object wasn't previously hovered, book it and call its handler\n                            internal.hovered.set(id, data);\n                            handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n                            handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n                        } else if (hoveredItem.stopped) {\n                            // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n                            data.stopPropagation();\n                        }\n                    }\n                    // Call mouse move\n                    handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n                } else {\n                    // All other events ...\n                    const handler = handlers[name];\n                    if (handler) {\n                        // Forward all events back to their respective handlers with the exception of click events,\n                        // which must use the initial target\n                        if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n                            // Missed events have to come first\n                            pointerMissed(event, internal.interaction.filter((object)=>!internal.initialHits.includes(object)));\n                            // Now call the handler\n                            handler(data);\n                        }\n                    } else {\n                        // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n                        if (isClickEvent && internal.initialHits.includes(eventObject)) {\n                            pointerMissed(event, internal.interaction.filter((object)=>!internal.initialHits.includes(object)));\n                        }\n                    }\n                }\n            }\n            handleIntersects(hits, event, delta, onIntersect);\n        };\n    }\n    return {\n        handlePointer\n    };\n}\n// Keys that shouldn't be copied between R3F stores\nconst privateKeys = [\n    \"set\",\n    \"get\",\n    \"setSize\",\n    \"setFrameloop\",\n    \"setDpr\",\n    \"events\",\n    \"invalidate\",\n    \"advance\",\n    \"size\",\n    \"viewport\"\n];\nconst isRenderer = (def)=>!!(def != null && def.render);\nconst context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst createStore = (invalidate, advance)=>{\n    const rootState = (0,zustand__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((set, get)=>{\n        const position = new three__WEBPACK_IMPORTED_MODULE_4__.Vector3();\n        const defaultTarget = new three__WEBPACK_IMPORTED_MODULE_4__.Vector3();\n        const tempTarget = new three__WEBPACK_IMPORTED_MODULE_4__.Vector3();\n        function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n            const { width, height, top, left } = size;\n            const aspect = width / height;\n            if (target instanceof three__WEBPACK_IMPORTED_MODULE_4__.Vector3) tempTarget.copy(target);\n            else tempTarget.set(...target);\n            const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n            if (isOrthographicCamera(camera)) {\n                return {\n                    width: width / camera.zoom,\n                    height: height / camera.zoom,\n                    top,\n                    left,\n                    factor: 1,\n                    distance,\n                    aspect\n                };\n            } else {\n                const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n                const h = 2 * Math.tan(fov / 2) * distance; // visible height\n                const w = h * (width / height);\n                return {\n                    width: w,\n                    height: h,\n                    top,\n                    left,\n                    factor: width / w,\n                    distance,\n                    aspect\n                };\n            }\n        }\n        let performanceTimeout = undefined;\n        const setPerformanceCurrent = (current)=>set((state)=>({\n                    performance: {\n                        ...state.performance,\n                        current\n                    }\n                }));\n        const pointer = new three__WEBPACK_IMPORTED_MODULE_4__.Vector2();\n        const rootState = {\n            set,\n            get,\n            // Mock objects that have to be configured\n            gl: null,\n            camera: null,\n            raycaster: null,\n            events: {\n                priority: 1,\n                enabled: true,\n                connected: false\n            },\n            xr: null,\n            scene: null,\n            invalidate: (frames = 1)=>invalidate(get(), frames),\n            advance: (timestamp, runGlobalEffects)=>advance(timestamp, runGlobalEffects, get()),\n            legacy: false,\n            linear: false,\n            flat: false,\n            controls: null,\n            clock: new three__WEBPACK_IMPORTED_MODULE_4__.Clock(),\n            pointer,\n            mouse: pointer,\n            frameloop: \"always\",\n            onPointerMissed: undefined,\n            performance: {\n                current: 1,\n                min: 0.5,\n                max: 1,\n                debounce: 200,\n                regress: ()=>{\n                    const state = get();\n                    // Clear timeout\n                    if (performanceTimeout) clearTimeout(performanceTimeout);\n                    // Set lower bound performance\n                    if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n                    // Go back to upper bound performance after a while unless something regresses meanwhile\n                    performanceTimeout = setTimeout(()=>setPerformanceCurrent(get().performance.max), state.performance.debounce);\n                }\n            },\n            size: {\n                width: 0,\n                height: 0,\n                top: 0,\n                left: 0,\n                updateStyle: false\n            },\n            viewport: {\n                initialDpr: 0,\n                dpr: 0,\n                width: 0,\n                height: 0,\n                top: 0,\n                left: 0,\n                aspect: 0,\n                distance: 0,\n                factor: 0,\n                getCurrentViewport\n            },\n            setEvents: (events)=>set((state)=>({\n                        ...state,\n                        events: {\n                            ...state.events,\n                            ...events\n                        }\n                    })),\n            setSize: (width, height, updateStyle, top, left)=>{\n                const camera = get().camera;\n                const size = {\n                    width,\n                    height,\n                    top: top || 0,\n                    left: left || 0,\n                    updateStyle\n                };\n                set((state)=>({\n                        size,\n                        viewport: {\n                            ...state.viewport,\n                            ...getCurrentViewport(camera, defaultTarget, size)\n                        }\n                    }));\n            },\n            setDpr: (dpr)=>set((state)=>{\n                    const resolved = calculateDpr(dpr);\n                    return {\n                        viewport: {\n                            ...state.viewport,\n                            dpr: resolved,\n                            initialDpr: state.viewport.initialDpr || resolved\n                        }\n                    };\n                }),\n            setFrameloop: (frameloop = \"always\")=>{\n                const clock = get().clock;\n                // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n                clock.stop();\n                clock.elapsedTime = 0;\n                if (frameloop !== \"never\") {\n                    clock.start();\n                    clock.elapsedTime = 0;\n                }\n                set(()=>({\n                        frameloop\n                    }));\n            },\n            previousRoot: undefined,\n            internal: {\n                active: false,\n                priority: 0,\n                frames: 0,\n                lastEvent: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createRef(),\n                interaction: [],\n                hovered: new Map(),\n                subscribers: [],\n                initialClick: [\n                    0,\n                    0\n                ],\n                initialHits: [],\n                capturedMap: new Map(),\n                subscribe: (ref, priority, store)=>{\n                    const internal = get().internal;\n                    // If this subscription was given a priority, it takes rendering into its own hands\n                    // For that reason we switch off automatic rendering and increase the manual flag\n                    // As long as this flag is positive there can be no internal rendering at all\n                    // because there could be multiple render subscriptions\n                    internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n                    internal.subscribers.push({\n                        ref,\n                        priority,\n                        store\n                    });\n                    // Register subscriber and sort layers from lowest to highest, meaning,\n                    // highest priority renders last (on top of the other frames)\n                    internal.subscribers = internal.subscribers.sort((a, b)=>a.priority - b.priority);\n                    return ()=>{\n                        const internal = get().internal;\n                        if (internal != null && internal.subscribers) {\n                            // Decrease manual flag if this subscription had a priority\n                            internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n                            // Remove subscriber from list\n                            internal.subscribers = internal.subscribers.filter((s)=>s.ref !== ref);\n                        }\n                    };\n                }\n            }\n        };\n        return rootState;\n    });\n    const state = rootState.getState();\n    let oldSize = state.size;\n    let oldDpr = state.viewport.dpr;\n    let oldCamera = state.camera;\n    rootState.subscribe(()=>{\n        const { camera, size, viewport, gl, set } = rootState.getState();\n        // Resize camera and renderer on changes to size and pixelratio\n        if (size !== oldSize || viewport.dpr !== oldDpr) {\n            var _size$updateStyle;\n            oldSize = size;\n            oldDpr = viewport.dpr;\n            // Update camera & renderer\n            updateCamera(camera, size);\n            gl.setPixelRatio(viewport.dpr);\n            const updateStyle = (_size$updateStyle = size.updateStyle) != null ? _size$updateStyle : typeof HTMLCanvasElement !== \"undefined\" && gl.domElement instanceof HTMLCanvasElement;\n            gl.setSize(size.width, size.height, updateStyle);\n        }\n        // Update viewport once the camera changes\n        if (camera !== oldCamera) {\n            oldCamera = camera;\n            // Update viewport\n            set((state)=>({\n                    viewport: {\n                        ...state.viewport,\n                        ...state.viewport.getCurrentViewport(camera)\n                    }\n                }));\n        }\n    });\n    // Invalidate on any change\n    rootState.subscribe((state)=>invalidate(state));\n    // Return root state\n    return rootState;\n};\nfunction createSubs(callback, subs) {\n    const sub = {\n        callback\n    };\n    subs.add(sub);\n    return ()=>void subs.delete(sub);\n}\nlet i;\nlet globalEffects = new Set();\nlet globalAfterEffects = new Set();\nlet globalTailEffects = new Set();\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */ const addEffect = (callback)=>createSubs(callback, globalEffects);\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */ const addAfterEffect = (callback)=>createSubs(callback, globalAfterEffects);\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */ const addTail = (callback)=>createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n    if (!effects.size) return;\n    for (const { callback } of effects.values()){\n        callback(timestamp);\n    }\n}\nfunction flushGlobalEffects(type, timestamp) {\n    switch(type){\n        case \"before\":\n            return run(globalEffects, timestamp);\n        case \"after\":\n            return run(globalAfterEffects, timestamp);\n        case \"tail\":\n            return run(globalTailEffects, timestamp);\n    }\n}\nlet subscribers;\nlet subscription;\nfunction render$1(timestamp, state, frame) {\n    // Run local effects\n    let delta = state.clock.getDelta();\n    // In frameloop='never' mode, clock times are updated using the provided timestamp\n    if (state.frameloop === \"never\" && typeof timestamp === \"number\") {\n        delta = timestamp - state.clock.elapsedTime;\n        state.clock.oldTime = state.clock.elapsedTime;\n        state.clock.elapsedTime = timestamp;\n    }\n    // Call subscribers (useFrame)\n    subscribers = state.internal.subscribers;\n    for(i = 0; i < subscribers.length; i++){\n        subscription = subscribers[i];\n        subscription.ref.current(subscription.store.getState(), delta, frame);\n    }\n    // Render content\n    if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n    // Decrease frame count\n    state.internal.frames = Math.max(0, state.internal.frames - 1);\n    return state.frameloop === \"always\" ? 1 : state.internal.frames;\n}\nfunction createLoop(roots) {\n    let running = false;\n    let repeat;\n    let frame;\n    let state;\n    function loop(timestamp) {\n        frame = requestAnimationFrame(loop);\n        running = true;\n        repeat = 0;\n        // Run effects\n        flushGlobalEffects(\"before\", timestamp);\n        // Render all roots\n        for (const root of roots.values()){\n            var _state$gl$xr;\n            state = root.store.getState();\n            // If the frameloop is invalidated, do not run another frame\n            if (state.internal.active && (state.frameloop === \"always\" || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n                repeat += render$1(timestamp, state);\n            }\n        }\n        // Run after-effects\n        flushGlobalEffects(\"after\", timestamp);\n        // Stop the loop if nothing invalidates it\n        if (repeat === 0) {\n            // Tail call effects, they are called when rendering stops\n            flushGlobalEffects(\"tail\", timestamp);\n            // Flag end of operation\n            running = false;\n            return cancelAnimationFrame(frame);\n        }\n    }\n    function invalidate(state, frames = 1) {\n        var _state$gl$xr2;\n        if (!state) return roots.forEach((root)=>invalidate(root.store.getState()), frames);\n        if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === \"never\") return;\n        // Increase frames, do not go higher than 60\n        state.internal.frames = Math.min(60, state.internal.frames + frames);\n        // If the render-loop isn't active, start it\n        if (!running) {\n            running = true;\n            requestAnimationFrame(loop);\n        }\n    }\n    function advance(timestamp, runGlobalEffects = true, state, frame) {\n        if (runGlobalEffects) flushGlobalEffects(\"before\", timestamp);\n        if (!state) for (const root of roots.values())render$1(timestamp, root.store.getState());\n        else render$1(timestamp, state, frame);\n        if (runGlobalEffects) flushGlobalEffects(\"after\", timestamp);\n    }\n    return {\n        loop,\n        /**\r\n     * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\r\n     * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\r\n     */ invalidate,\n        /**\r\n     * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\r\n     * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\r\n     */ advance\n    };\n}\n/**\r\n * Exposes an object's {@link LocalState}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */ function useInstanceHandle(ref) {\n    const instance = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    useIsomorphicLayoutEffect(()=>void (instance.current = ref.current.__r3f), [\n        ref\n    ]);\n    return instance;\n}\nfunction useStore() {\n    const store = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n    if (!store) throw new Error(\"R3F: Hooks can only be used within the Canvas component!\");\n    return store;\n}\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */ function useThree(selector = (state)=>state, equalityFn) {\n    return useStore()(selector, equalityFn);\n}\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */ function useFrame(callback, renderPriority = 0) {\n    const store = useStore();\n    const subscribe = store.getState().internal.subscribe;\n    // Memoize ref\n    const ref = useMutableCallback(callback);\n    // Subscribe on mount, unsubscribe on unmount\n    useIsomorphicLayoutEffect(()=>subscribe(ref, renderPriority, store), [\n        renderPriority,\n        subscribe,\n        store\n    ]);\n    return null;\n}\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */ function useGraph(object) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>buildGraph(object), [\n        object\n    ]);\n}\nconst memoizedLoaders = new WeakMap();\nfunction loadingFn(extensions, onProgress) {\n    return function(Proto, ...input) {\n        // Construct new loader and run extensions\n        let loader = memoizedLoaders.get(Proto);\n        if (!loader) {\n            loader = new Proto();\n            memoizedLoaders.set(Proto, loader);\n        }\n        if (extensions) extensions(loader);\n        // Go through the urls and load them\n        return Promise.all(input.map((input)=>new Promise((res, reject)=>loader.load(input, (data)=>{\n                    if (data.scene) Object.assign(data, buildGraph(data.scene));\n                    res(data);\n                }, onProgress, (error)=>reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`)))))).finally(()=>loader.dispose == null ? void 0 : loader.dispose());\n    };\n}\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */ function useLoader(Proto, input, extensions, onProgress) {\n    // Use suspense to load async assets\n    const keys = Array.isArray(input) ? input : [\n        input\n    ];\n    const results = (0,suspend_react__WEBPACK_IMPORTED_MODULE_6__.suspend)(loadingFn(extensions, onProgress), [\n        Proto,\n        ...keys\n    ], {\n        equal: is.equ\n    });\n    // Return the object/s\n    return Array.isArray(input) ? results : results[0];\n}\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */ useLoader.preload = function(Proto, input, extensions) {\n    const keys = Array.isArray(input) ? input : [\n        input\n    ];\n    return (0,suspend_react__WEBPACK_IMPORTED_MODULE_6__.preload)(loadingFn(extensions), [\n        Proto,\n        ...keys\n    ]);\n};\n/**\r\n * Removes a loaded asset from cache.\r\n */ useLoader.clear = function(Proto, input) {\n    const keys = Array.isArray(input) ? input : [\n        input\n    ];\n    return (0,suspend_react__WEBPACK_IMPORTED_MODULE_6__.clear)([\n        Proto,\n        ...keys\n    ]);\n};\nconst roots = new Map();\nconst { invalidate, advance } = createLoop(roots);\nconst { reconciler, applyProps } = createRenderer(roots, getEventPriority);\nconst shallowLoose = {\n    objects: \"shallow\",\n    strict: false\n};\nconst createRendererInstance = (gl, canvas)=>{\n    const customRenderer = typeof gl === \"function\" ? gl(canvas) : gl;\n    if (isRenderer(customRenderer)) return customRenderer;\n    else return new three__WEBPACK_IMPORTED_MODULE_4__.WebGLRenderer({\n        powerPreference: \"high-performance\",\n        canvas: canvas,\n        antialias: true,\n        alpha: true,\n        ...gl\n    });\n};\nfunction computeInitialSize(canvas, defaultSize) {\n    if (defaultSize) return defaultSize;\n    if (typeof HTMLCanvasElement !== \"undefined\" && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n        const { width, height, top, left } = canvas.parentElement.getBoundingClientRect();\n        return {\n            width,\n            height,\n            top,\n            left\n        };\n    } else if (typeof OffscreenCanvas !== \"undefined\" && canvas instanceof OffscreenCanvas) {\n        return {\n            width: canvas.width,\n            height: canvas.height,\n            top: 0,\n            left: 0\n        };\n    }\n    return {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n    };\n}\nfunction createRoot(canvas) {\n    // Check against mistaken use of createRoot\n    const prevRoot = roots.get(canvas);\n    const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n    const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n    if (prevRoot) console.warn(\"R3F.createRoot should only be called once!\");\n    // Report when an error was detected in a previous render\n    // https://github.com/pmndrs/react-three-fiber/pull/2261\n    const logRecoverableError = typeof reportError === \"function\" ? // In modern browsers, reportError will dispatch an error event,\n    // emulating an uncaught JavaScript error.\n    reportError : // In older browsers and test environments, fallback to console.error.\n    console.error;\n    // Create store\n    const store = prevStore || createStore(invalidate, advance);\n    // Create renderer\n    const fiber = prevFiber || reconciler.createContainer(store, react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ConcurrentRoot, null, false, null, \"\", logRecoverableError, null);\n    // Map it\n    if (!prevRoot) roots.set(canvas, {\n        fiber,\n        store\n    });\n    // Locals\n    let onCreated;\n    let configured = false;\n    let lastCamera;\n    return {\n        configure (props = {}) {\n            let { gl: glConfig, size: propsSize, scene: sceneOptions, events, onCreated: onCreatedCallback, shadows = false, linear = false, flat = false, legacy = false, orthographic = false, frameloop = \"always\", dpr = [\n                1,\n                2\n            ], performance: performance1, raycaster: raycastOptions, camera: cameraOptions, onPointerMissed } = props;\n            let state = store.getState();\n            // Set up renderer (one time only!)\n            let gl = state.gl;\n            if (!state.gl) state.set({\n                gl: gl = createRendererInstance(glConfig, canvas)\n            });\n            // Set up raycaster (one time only!)\n            let raycaster = state.raycaster;\n            if (!raycaster) state.set({\n                raycaster: raycaster = new three__WEBPACK_IMPORTED_MODULE_4__.Raycaster()\n            });\n            // Set raycaster options\n            const { params, ...options } = raycastOptions || {};\n            if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n                ...options\n            });\n            if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n                params: {\n                    ...raycaster.params,\n                    ...params\n                }\n            });\n            // Create default camera, don't overwrite any user-set state\n            if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n                lastCamera = cameraOptions;\n                const isCamera = cameraOptions instanceof three__WEBPACK_IMPORTED_MODULE_4__.Camera;\n                const camera = isCamera ? cameraOptions : orthographic ? new three__WEBPACK_IMPORTED_MODULE_4__.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new three__WEBPACK_IMPORTED_MODULE_4__.PerspectiveCamera(75, 0, 0.1, 1000);\n                if (!isCamera) {\n                    camera.position.z = 5;\n                    if (cameraOptions) applyProps(camera, cameraOptions);\n                    // Always look at center by default\n                    if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n                }\n                state.set({\n                    camera\n                });\n            }\n            // Set up scene (one time only!)\n            if (!state.scene) {\n                let scene;\n                if (sceneOptions instanceof three__WEBPACK_IMPORTED_MODULE_4__.Scene) {\n                    scene = sceneOptions;\n                } else {\n                    scene = new three__WEBPACK_IMPORTED_MODULE_4__.Scene();\n                    if (sceneOptions) applyProps(scene, sceneOptions);\n                }\n                state.set({\n                    scene: prepare(scene)\n                });\n            }\n            // Set up XR (one time only!)\n            if (!state.xr) {\n                var _gl$xr;\n                // Handle frame behavior in WebXR\n                const handleXRFrame = (timestamp, frame)=>{\n                    const state = store.getState();\n                    if (state.frameloop === \"never\") return;\n                    advance(timestamp, true, state, frame);\n                };\n                // Toggle render switching on session\n                const handleSessionChange = ()=>{\n                    const state = store.getState();\n                    state.gl.xr.enabled = state.gl.xr.isPresenting;\n                    state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n                    if (!state.gl.xr.isPresenting) invalidate(state);\n                };\n                // WebXR session manager\n                const xr = {\n                    connect () {\n                        const gl = store.getState().gl;\n                        gl.xr.addEventListener(\"sessionstart\", handleSessionChange);\n                        gl.xr.addEventListener(\"sessionend\", handleSessionChange);\n                    },\n                    disconnect () {\n                        const gl = store.getState().gl;\n                        gl.xr.removeEventListener(\"sessionstart\", handleSessionChange);\n                        gl.xr.removeEventListener(\"sessionend\", handleSessionChange);\n                    }\n                };\n                // Subscribe to WebXR session events\n                if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === \"function\") xr.connect();\n                state.set({\n                    xr\n                });\n            }\n            // Set shadowmap\n            if (gl.shadowMap) {\n                const oldEnabled = gl.shadowMap.enabled;\n                const oldType = gl.shadowMap.type;\n                gl.shadowMap.enabled = !!shadows;\n                if (is.boo(shadows)) {\n                    gl.shadowMap.type = three__WEBPACK_IMPORTED_MODULE_4__.PCFSoftShadowMap;\n                } else if (is.str(shadows)) {\n                    var _types$shadows;\n                    const types = {\n                        basic: three__WEBPACK_IMPORTED_MODULE_4__.BasicShadowMap,\n                        percentage: three__WEBPACK_IMPORTED_MODULE_4__.PCFShadowMap,\n                        soft: three__WEBPACK_IMPORTED_MODULE_4__.PCFSoftShadowMap,\n                        variance: three__WEBPACK_IMPORTED_MODULE_4__.VSMShadowMap\n                    };\n                    gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : three__WEBPACK_IMPORTED_MODULE_4__.PCFSoftShadowMap;\n                } else if (is.obj(shadows)) {\n                    Object.assign(gl.shadowMap, shadows);\n                }\n                if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n            }\n            // Safely set color management if available.\n            // Avoid accessing THREE.ColorManagement to play nice with older versions\n            const ColorManagement = getColorManagement();\n            if (ColorManagement) {\n                if (\"enabled\" in ColorManagement) ColorManagement.enabled = !legacy;\n                else if (\"legacyMode\" in ColorManagement) ColorManagement.legacyMode = legacy;\n            }\n            // Set color space and tonemapping preferences\n            const LinearEncoding = 3000;\n            const sRGBEncoding = 3001;\n            applyProps(gl, {\n                outputEncoding: linear ? LinearEncoding : sRGBEncoding,\n                toneMapping: flat ? three__WEBPACK_IMPORTED_MODULE_4__.NoToneMapping : three__WEBPACK_IMPORTED_MODULE_4__.ACESFilmicToneMapping\n            });\n            // Update color management state\n            if (state.legacy !== legacy) state.set(()=>({\n                    legacy\n                }));\n            if (state.linear !== linear) state.set(()=>({\n                    linear\n                }));\n            if (state.flat !== flat) state.set(()=>({\n                    flat\n                }));\n            // Set gl props\n            if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n            // Store events internally\n            if (events && !state.events.handlers) state.set({\n                events: events(store)\n            });\n            // Check size, allow it to take on container bounds initially\n            const size = computeInitialSize(canvas, propsSize);\n            if (!is.equ(size, state.size, shallowLoose)) {\n                state.setSize(size.width, size.height, size.updateStyle, size.top, size.left);\n            }\n            // Check pixelratio\n            if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n            // Check frameloop\n            if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n            // Check pointer missed\n            if (!state.onPointerMissed) state.set({\n                onPointerMissed\n            });\n            // Check performance\n            if (performance1 && !is.equ(performance1, state.performance, shallowLoose)) state.set((state)=>({\n                    performance: {\n                        ...state.performance,\n                        ...performance1\n                    }\n                }));\n            // Set locals\n            onCreated = onCreatedCallback;\n            configured = true;\n            return this;\n        },\n        render (children) {\n            // The root has to be configured before it can be rendered\n            if (!configured) this.configure();\n            reconciler.updateContainer(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Provider, {\n                store: store,\n                children: children,\n                onCreated: onCreated,\n                rootElement: canvas\n            }), fiber, null, ()=>undefined);\n            return store;\n        },\n        unmount () {\n            unmountComponentAtNode(canvas);\n        }\n    };\n}\nfunction render(children, canvas, config) {\n    console.warn(\"R3F.render is no longer supported in React 18. Use createRoot instead!\");\n    const root = createRoot(canvas);\n    root.configure(config);\n    return root.render(children);\n}\nfunction Provider({ store, children, onCreated, rootElement }) {\n    useIsomorphicLayoutEffect(()=>{\n        const state = store.getState();\n        // Flag the canvas active, rendering will now begin\n        state.set((state)=>({\n                internal: {\n                    ...state.internal,\n                    active: true\n                }\n            }));\n        // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n        if (onCreated) onCreated(state);\n        // Connect events to the targets parent, this is done to ensure events are registered on\n        // a shared target, and not on the canvas itself\n        if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(context.Provider, {\n        value: store\n    }, children);\n}\nfunction unmountComponentAtNode(canvas, callback) {\n    const root = roots.get(canvas);\n    const fiber = root == null ? void 0 : root.fiber;\n    if (fiber) {\n        const state = root == null ? void 0 : root.store.getState();\n        if (state) state.internal.active = false;\n        reconciler.updateContainer(null, fiber, null, ()=>{\n            if (state) {\n                setTimeout(()=>{\n                    try {\n                        var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n                        state.events.disconnect == null ? void 0 : state.events.disconnect();\n                        (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n                        (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n                        if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n                        dispose(state);\n                        roots.delete(canvas);\n                        if (callback) callback(canvas);\n                    } catch (e) {\n                    /* ... */ }\n                }, 500);\n            }\n        });\n    }\n}\nfunction createPortal(children, container, state) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Portal, {\n        key: container.uuid,\n        children: children,\n        container: container,\n        state: state\n    });\n}\nfunction Portal({ state = {}, children, container }) {\n    /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */ const { events, size, ...rest } = state;\n    const previousRoot = useStore();\n    const [raycaster] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new three__WEBPACK_IMPORTED_MODULE_4__.Raycaster());\n    const [pointer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new three__WEBPACK_IMPORTED_MODULE_4__.Vector2());\n    const inject = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((rootState, injectState)=>{\n        const intersect = {\n            ...rootState\n        }; // all prev state props\n        // Only the fields of \"rootState\" that do not differ from injectState\n        // Some props should be off-limits\n        // Otherwise filter out the props that are different and let the inject layer take precedence\n        Object.keys(rootState).forEach((key)=>{\n            if (// Some props should be off-limits\n            privateKeys.includes(key) || // Otherwise filter out the props that are different and let the inject layer take precedence\n            // Unless the inject layer props is undefined, then we keep the root layer\n            rootState[key] !== injectState[key] && injectState[key]) {\n                delete intersect[key];\n            }\n        });\n        let viewport = undefined;\n        if (injectState && size) {\n            const camera = injectState.camera;\n            // Calculate the override viewport, if present\n            viewport = rootState.viewport.getCurrentViewport(camera, new three__WEBPACK_IMPORTED_MODULE_4__.Vector3(), size);\n            // Update the portal camera, if it differs from the previous layer\n            if (camera !== rootState.camera) updateCamera(camera, size);\n        }\n        return {\n            // The intersect consists of the previous root state\n            ...intersect,\n            // Portals have their own scene, which forms the root, a raycaster and a pointer\n            scene: container,\n            raycaster,\n            pointer,\n            mouse: pointer,\n            // Their previous root is the layer before it\n            previousRoot,\n            // Events, size and viewport can be overridden by the inject layer\n            events: {\n                ...rootState.events,\n                ...injectState == null ? void 0 : injectState.events,\n                ...events\n            },\n            size: {\n                ...rootState.size,\n                ...size\n            },\n            viewport: {\n                ...rootState.viewport,\n                ...viewport\n            },\n            ...rest\n        };\n    }, [\n        state\n    ]);\n    const [usePortalStore] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>{\n        // Create a mirrored store, based on the previous root with a few overrides ...\n        const previousState = previousRoot.getState();\n        const store = (0,zustand__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((set, get)=>({\n                ...previousState,\n                scene: container,\n                raycaster,\n                pointer,\n                mouse: pointer,\n                previousRoot,\n                events: {\n                    ...previousState.events,\n                    ...events\n                },\n                size: {\n                    ...previousState.size,\n                    ...size\n                },\n                ...rest,\n                // Set and get refer to this root-state\n                set,\n                get,\n                // Layers are allowed to override events\n                setEvents: (events)=>set((state)=>({\n                            ...state,\n                            events: {\n                                ...state.events,\n                                ...events\n                            }\n                        }))\n            }));\n        return store;\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n        const unsub = previousRoot.subscribe((prev)=>usePortalStore.setState((state)=>inject(prev, state)));\n        return ()=>{\n            unsub();\n            usePortalStore.destroy();\n        };\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        usePortalStore.setState((injectState)=>inject(previousRoot.getState(), injectState));\n    }, [\n        inject\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, reconciler.createPortal(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(context.Provider, {\n        value: usePortalStore\n    }, children), usePortalStore, null));\n}\nreconciler.injectIntoDevTools({\n    bundleType:  false ? 0 : 1,\n    rendererPackageName: \"@react-three/fiber\",\n    version: react__WEBPACK_IMPORTED_MODULE_0__.version\n});\nconst act = react__WEBPACK_IMPORTED_MODULE_0__.unstable_act;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2ZpYmVyL2Rpc3QvaW5kZXgtNTkxODAxMmEuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQStCO0FBQ0E7QUFDbUc7QUFDckc7QUFDYTtBQUNtQztBQUNyQjtBQUV4RCxJQUFJYSxhQUFhLFdBQVcsR0FBRUMsT0FBT0MsTUFBTSxDQUFDO0lBQzFDQyxXQUFXO0FBQ2I7QUFFQSxNQUFNQyxZQUFZLENBQUM7QUFDbkIsTUFBTUMsU0FBU0MsQ0FBQUEsVUFBVyxLQUFLTCxPQUFPTSxNQUFNLENBQUNILFdBQVdFO0FBQ3hELFNBQVNFLGVBQWVDLE1BQU0sRUFBRUMsaUJBQWlCO0lBQy9DLFNBQVNDLGVBQWVDLElBQUksRUFBRSxFQUM1QkMsT0FBTyxFQUFFLEVBQ1RDLE1BQU0sRUFDTixHQUFHQyxPQUNKLEVBQUVDLElBQUk7UUFDTCxJQUFJQyxPQUFPLENBQUMsRUFBRUwsSUFBSSxDQUFDLEVBQUUsQ0FBQ00sV0FBVyxHQUFHLEVBQUVOLEtBQUtPLEtBQUssQ0FBQyxHQUFHLENBQUM7UUFDckQsSUFBSUM7UUFDSixJQUFJUixTQUFTLGFBQWE7WUFDeEIsSUFBSUcsTUFBTU0sTUFBTSxLQUFLQyxXQUFXLE1BQU0sSUFBSUMsTUFBTTtZQUNoRCxNQUFNRixTQUFTTixNQUFNTSxNQUFNO1lBQzNCRCxXQUFXSSxRQUFRSCxRQUFRO2dCQUN6QlQ7Z0JBQ0FJO2dCQUNBRjtnQkFDQVcsV0FBVztZQUNiO1FBQ0YsT0FBTztZQUNMLE1BQU1DLFNBQVN0QixTQUFTLENBQUNhLEtBQUs7WUFDOUIsSUFBSSxDQUFDUyxRQUFRO2dCQUNYLE1BQU0sSUFBSUgsTUFBTSxDQUFDLEtBQUssRUFBRU4sS0FBSyw0SkFBNEosQ0FBQztZQUM1TDtZQUVBLG9EQUFvRDtZQUNwRCxJQUFJLENBQUNVLE1BQU1DLE9BQU8sQ0FBQ2YsT0FBTyxNQUFNLElBQUlVLE1BQU07WUFFMUMsOENBQThDO1lBQzlDLHdEQUF3RDtZQUN4REgsV0FBV0ksUUFBUSxJQUFJRSxVQUFVYixPQUFPO2dCQUN0Q0Q7Z0JBQ0FJO2dCQUNBRjtnQkFDQSx5REFBeUQ7Z0JBQ3pEZSxlQUFlO29CQUNiaEI7Z0JBQ0Y7WUFDRjtRQUNGO1FBRUEsdUNBQXVDO1FBQ3ZDLElBQUlPLFNBQVNVLEtBQUssQ0FBQ2hCLE1BQU0sS0FBS1EsV0FBVztZQUN2QyxJQUFJRixvQkFBb0JqQyxpREFBb0IsRUFBRWlDLFNBQVNVLEtBQUssQ0FBQ2hCLE1BQU0sR0FBRztpQkFBZ0IsSUFBSU0sb0JBQW9CakMsMkNBQWMsRUFBRWlDLFNBQVNVLEtBQUssQ0FBQ2hCLE1BQU0sR0FBRztRQUN4SjtRQUVBLDJGQUEyRjtRQUMzRixnR0FBZ0c7UUFDaEcsNEJBQTRCO1FBQzVCLCtDQUErQztRQUMvQyxJQUFJRyxTQUFTLFVBQVVnQixhQUFhYixVQUFVTDtRQUM5QyxPQUFPSztJQUNUO0lBQ0EsU0FBU2MsWUFBWUMsY0FBYyxFQUFFQyxLQUFLO1FBQ3hDLElBQUlDLFFBQVE7UUFDWixJQUFJRCxPQUFPO1lBQ1QsSUFBSUUsY0FBY0M7WUFDbEIsNkVBQTZFO1lBQzdFLElBQUksQ0FBQ0QsZUFBZUYsTUFBTU4sS0FBSyxLQUFLLFFBQVFRLGFBQWF4QixNQUFNLEVBQUU7Z0JBQy9EQSxPQUFPcUIsZ0JBQWdCQyxPQUFPQSxNQUFNTixLQUFLLENBQUNoQixNQUFNO1lBQ2xELE9BQU8sSUFBSXNCLE1BQU1JLFVBQVUsSUFBSUwsZUFBZUssVUFBVSxFQUFFO2dCQUN4RCxvQ0FBb0M7Z0JBQ3BDTCxlQUFlTSxHQUFHLENBQUNMO2dCQUNuQkMsUUFBUTtZQUNWO1lBQ0EsaUdBQWlHO1lBQ2pHLCtFQUErRTtZQUMvRSxJQUFJLENBQUNBLE9BQU8sQ0FBQ0Usd0JBQXdCSixlQUFlTCxLQUFLLEtBQUssT0FBTyxLQUFLLElBQUlTLHNCQUFzQmpDLE9BQU8sQ0FBQ29DLElBQUksQ0FBQ047WUFDakgsSUFBSSxDQUFDQSxNQUFNTixLQUFLLEVBQUVOLFFBQVFZLE9BQU8sQ0FBQztZQUNsQ0EsTUFBTU4sS0FBSyxDQUFDYSxNQUFNLEdBQUdSO1lBQ3JCUyxlQUFlUjtZQUNmUyxtQkFBbUJUO1FBQ3JCO0lBQ0Y7SUFDQSxTQUFTVSxhQUFhWCxjQUFjLEVBQUVDLEtBQUssRUFBRVcsV0FBVztRQUN0RCxJQUFJVixRQUFRO1FBQ1osSUFBSUQsT0FBTztZQUNULElBQUlZLGVBQWVDO1lBQ25CLElBQUksQ0FBQ0QsZ0JBQWdCWixNQUFNTixLQUFLLEtBQUssUUFBUWtCLGNBQWNsQyxNQUFNLEVBQUU7Z0JBQ2pFQSxPQUFPcUIsZ0JBQWdCQyxPQUFPQSxNQUFNTixLQUFLLENBQUNoQixNQUFNO1lBQ2xELE9BQU8sSUFBSXNCLE1BQU1JLFVBQVUsSUFBSUwsZUFBZUssVUFBVSxFQUFFO2dCQUN4REosTUFBTU8sTUFBTSxHQUFHUjtnQkFDZkMsTUFBTWMsYUFBYSxDQUFDO29CQUNsQnRDLE1BQU07Z0JBQ1I7Z0JBQ0EsTUFBTXVDLGVBQWVoQixlQUFlaUIsUUFBUSxDQUFDQyxNQUFNLENBQUNDLENBQUFBLFVBQVdBLFlBQVlsQjtnQkFDM0UsTUFBTW1CLFFBQVFKLGFBQWFLLE9BQU8sQ0FBQ1Q7Z0JBQ25DWixlQUFlaUIsUUFBUSxHQUFHO3VCQUFJRCxhQUFhaEMsS0FBSyxDQUFDLEdBQUdvQztvQkFBUW5CO3VCQUFVZSxhQUFhaEMsS0FBSyxDQUFDb0M7aUJBQU87Z0JBQ2hHbEIsUUFBUTtZQUNWO1lBQ0EsSUFBSSxDQUFDQSxPQUFPLENBQUNZLHlCQUF5QmQsZUFBZUwsS0FBSyxLQUFLLE9BQU8sS0FBSyxJQUFJbUIsdUJBQXVCM0MsT0FBTyxDQUFDb0MsSUFBSSxDQUFDTjtZQUNuSCxJQUFJLENBQUNBLE1BQU1OLEtBQUssRUFBRU4sUUFBUVksT0FBTyxDQUFDO1lBQ2xDQSxNQUFNTixLQUFLLENBQUNhLE1BQU0sR0FBR1I7WUFDckJTLGVBQWVSO1lBQ2ZTLG1CQUFtQlQ7UUFDckI7SUFDRjtJQUNBLFNBQVNxQixnQkFBZ0JDLEtBQUssRUFBRWYsTUFBTSxFQUFFZ0IsVUFBVSxLQUFLO1FBQ3JELElBQUlELE9BQU87ZUFBSUE7U0FBTSxDQUFDRSxPQUFPLENBQUN4QixDQUFBQSxRQUFTeUIsWUFBWWxCLFFBQVFQLE9BQU91QjtJQUNwRTtJQUNBLFNBQVNFLFlBQVkxQixjQUFjLEVBQUVDLEtBQUssRUFBRXVCLE9BQU87UUFDakQsSUFBSXZCLE9BQU87WUFDVCxJQUFJMEIsd0JBQXdCQyxlQUFlQztZQUMzQyw2QkFBNkI7WUFDN0IsSUFBSTVCLE1BQU1OLEtBQUssRUFBRU0sTUFBTU4sS0FBSyxDQUFDYSxNQUFNLEdBQUc7WUFDdEMsd0NBQXdDO1lBQ3hDLElBQUksQ0FBQ21CLHlCQUF5QjNCLGVBQWVMLEtBQUssS0FBSyxRQUFRZ0MsdUJBQXVCeEQsT0FBTyxFQUFFNkIsZUFBZUwsS0FBSyxDQUFDeEIsT0FBTyxHQUFHNkIsZUFBZUwsS0FBSyxDQUFDeEIsT0FBTyxDQUFDK0MsTUFBTSxDQUFDWSxDQUFBQSxJQUFLQSxNQUFNN0I7WUFDN0ssb0JBQW9CO1lBQ3BCLElBQUksQ0FBQzJCLGdCQUFnQjNCLE1BQU1OLEtBQUssS0FBSyxRQUFRaUMsY0FBY2pELE1BQU0sRUFBRTtnQkFDakVvRCxPQUFPL0IsZ0JBQWdCQyxPQUFPQSxNQUFNTixLQUFLLENBQUNoQixNQUFNO1lBQ2xELE9BQU8sSUFBSXNCLE1BQU1JLFVBQVUsSUFBSUwsZUFBZUssVUFBVSxFQUFFO2dCQUN4RCxJQUFJMkI7Z0JBQ0poQyxlQUFlaUMsTUFBTSxDQUFDaEM7Z0JBQ3RCLHVCQUF1QjtnQkFDdkIsSUFBSSxDQUFDK0IsZ0JBQWdCL0IsTUFBTU4sS0FBSyxLQUFLLFFBQVFxQyxjQUFjbkQsSUFBSSxFQUFFO29CQUMvRHFELG9CQUFvQmpDLE1BQU1OLEtBQUssQ0FBQ2QsSUFBSSxFQUFFb0I7Z0JBQ3hDO1lBQ0Y7WUFFQSxzRkFBc0Y7WUFDdEYsZ0ZBQWdGO1lBQ2hGLDhEQUE4RDtZQUM5RCx3QkFBd0I7WUFDeEIsZ0RBQWdEO1lBQ2hELHdFQUF3RTtZQUN4RSxFQUFFO1lBQ0YsOEZBQThGO1lBQzlGLHlFQUF5RTtZQUN6RSxNQUFNa0MsY0FBYyxDQUFDTixnQkFBZ0I1QixNQUFNTixLQUFLLEtBQUssT0FBTyxLQUFLLElBQUlrQyxjQUFjdkMsU0FBUztZQUM1RixNQUFNOEMsZ0JBQWdCWixZQUFZckMsWUFBWWMsTUFBTXVCLE9BQU8sS0FBSyxRQUFRLENBQUNXLGNBQWNYO1lBRXZGLHdGQUF3RjtZQUN4RixxQ0FBcUM7WUFDckMsSUFBSSxDQUFDVyxhQUFhO2dCQUNoQixJQUFJRTtnQkFDSmYsZ0JBQWdCLENBQUNlLGdCQUFnQnBDLE1BQU1OLEtBQUssS0FBSyxPQUFPLEtBQUssSUFBSTBDLGNBQWNsRSxPQUFPLEVBQUU4QixPQUFPbUM7Z0JBQy9GZCxnQkFBZ0JyQixNQUFNZ0IsUUFBUSxFQUFFaEIsT0FBT21DO1lBQ3pDO1lBRUEsb0JBQW9CO1lBQ3BCLE9BQU9uQyxNQUFNTixLQUFLO1lBRWxCLHFEQUFxRDtZQUNyRCxJQUFJeUMsaUJBQWlCbkMsTUFBTXVCLE9BQU8sSUFBSXZCLE1BQU14QixJQUFJLEtBQUssU0FBUztnQkFDNURqQixvRUFBeUJBLENBQUNDLDREQUFxQkEsRUFBRTtvQkFDL0MsSUFBSTt3QkFDRndDLE1BQU11QixPQUFPO29CQUNmLEVBQUUsT0FBT2MsR0FBRztvQkFDVixPQUFPLEdBQ1Q7Z0JBQ0Y7WUFDRjtZQUNBNUIsbUJBQW1CVjtRQUNyQjtJQUNGO0lBQ0EsU0FBU3VDLGVBQWV0RCxRQUFRLEVBQUVSLElBQUksRUFBRStELFFBQVEsRUFBRUMsS0FBSztRQUNyRCxJQUFJQztRQUNKLE1BQU1sQyxTQUFTLENBQUNrQyxrQkFBa0J6RCxTQUFTVSxLQUFLLEtBQUssT0FBTyxLQUFLLElBQUkrQyxnQkFBZ0JsQyxNQUFNO1FBQzNGLElBQUksQ0FBQ0EsUUFBUTtRQUNiLE1BQU1tQyxjQUFjbkUsZUFBZUMsTUFBTStELFVBQVV2RCxTQUFTVSxLQUFLLENBQUNkLElBQUk7UUFFdEUsMERBQTBEO1FBQzFELHFFQUFxRTtRQUNyRSw2REFBNkQ7UUFDN0QsSUFBSUksU0FBU2dDLFFBQVEsRUFBRTtZQUNyQixLQUFLLE1BQU1oQixTQUFTaEIsU0FBU2dDLFFBQVEsQ0FBRTtnQkFDckMsSUFBSWhCLE1BQU1OLEtBQUssRUFBRUksWUFBWTRDLGFBQWExQztZQUM1QztZQUNBaEIsU0FBU2dDLFFBQVEsR0FBR2hDLFNBQVNnQyxRQUFRLENBQUNDLE1BQU0sQ0FBQ2pCLENBQUFBLFFBQVMsQ0FBQ0EsTUFBTU4sS0FBSztRQUNwRTtRQUNBVixTQUFTVSxLQUFLLENBQUN4QixPQUFPLENBQUNzRCxPQUFPLENBQUN4QixDQUFBQSxRQUFTRixZQUFZNEMsYUFBYTFDO1FBQ2pFaEIsU0FBU1UsS0FBSyxDQUFDeEIsT0FBTyxHQUFHLEVBQUU7UUFDM0IsSUFBSSxDQUFDYyxTQUFTVSxLQUFLLENBQUNpRCx1QkFBdUIsRUFBRTtZQUMzQ2xCLFlBQVlsQixRQUFRdkI7UUFDdEI7UUFDQSxJQUFJMEQsWUFBWW5DLE1BQU0sRUFBRTtZQUN0Qm1DLFlBQVloRCxLQUFLLENBQUNpRCx1QkFBdUIsR0FBRztRQUM5QztRQUNBN0MsWUFBWVMsUUFBUW1DO1FBRXBCLHlCQUF5QjtRQUN6QixJQUFJQSxZQUFZRSxPQUFPLElBQUlGLFlBQVloRCxLQUFLLENBQUNtRCxVQUFVLEVBQUU7WUFDdkQsTUFBTUMsWUFBWUosWUFBWWhELEtBQUssQ0FBQ2QsSUFBSSxDQUFDbUUsUUFBUTtZQUNqREQsVUFBVUUsUUFBUSxDQUFDQyxXQUFXLENBQUMzQyxJQUFJLENBQUNvQztRQUN0QztRQUNBO1lBQUNGO1lBQU9BLE1BQU1VLFNBQVM7U0FBQyxDQUFDMUIsT0FBTyxDQUFDZ0IsQ0FBQUE7WUFDL0IsSUFBSUEsVUFBVSxNQUFNO2dCQUNsQkEsTUFBTVcsU0FBUyxHQUFHVDtnQkFDbEIsSUFBSUYsTUFBTVksR0FBRyxFQUFFO29CQUNiLElBQUksT0FBT1osTUFBTVksR0FBRyxLQUFLLFlBQVlaLE1BQU1ZLEdBQUcsQ0FBQ1Y7eUJBQWtCRixNQUFNWSxHQUFHLENBQUNDLE9BQU8sR0FBR1g7Z0JBQ3ZGO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsMERBQTBEO0lBQzFELE1BQU1ZLHFCQUFxQixJQUFNQyxRQUFRQyxJQUFJLENBQUM7SUFDOUMsTUFBTUMsYUFBYW5HLHVEQUFVQSxDQUFDO1FBQzVCaUI7UUFDQWtEO1FBQ0EzQjtRQUNBNEQsb0JBQW9CNUQ7UUFDcEJZO1FBQ0FpRCxrQkFBa0I7UUFDbEJDLG1CQUFtQjtRQUNuQkMscUJBQXFCO1FBQ3JCQyxtQkFBbUI7UUFDbkJDLFdBQVcsQ0FBQztRQUNaQyx3QkFBd0IsQ0FBQ0MsV0FBV2pFO1lBQ2xDLElBQUksQ0FBQ0EsT0FBTztZQUVaLHNDQUFzQztZQUN0QyxNQUFNa0UsUUFBUUQsVUFBVWxCLFFBQVEsR0FBR21CLEtBQUs7WUFDeEMsSUFBSSxDQUFDQSxNQUFNeEUsS0FBSyxFQUFFO1lBRWxCLHlDQUF5QztZQUN6Q3dFLE1BQU14RSxLQUFLLENBQUNkLElBQUksR0FBR3FGO1lBQ25CbkUsWUFBWW9FLE9BQU9sRTtRQUNyQjtRQUNBbUUsMEJBQTBCLENBQUNGLFdBQVdqRTtZQUNwQyxJQUFJLENBQUNBLE9BQU87WUFDWnlCLFlBQVl3QyxVQUFVbEIsUUFBUSxHQUFHbUIsS0FBSyxFQUFFbEU7UUFDMUM7UUFDQW9FLHlCQUF5QixDQUFDSCxXQUFXakUsT0FBT1c7WUFDMUMsSUFBSSxDQUFDWCxTQUFTLENBQUNXLGFBQWE7WUFFNUIsc0NBQXNDO1lBQ3RDLE1BQU11RCxRQUFRRCxVQUFVbEIsUUFBUSxHQUFHbUIsS0FBSztZQUN4QyxJQUFJLENBQUNBLE1BQU14RSxLQUFLLEVBQUU7WUFDbEJnQixhQUFhd0QsT0FBT2xFLE9BQU9XO1FBQzdCO1FBQ0EwRCxvQkFBb0IsSUFBTTtRQUMxQkMscUJBQXFCQyxDQUFBQSxvQkFBcUJBO1FBQzFDQyx5QkFBd0J4RixRQUFRO1lBQzlCLElBQUl5RjtZQUNKLE1BQU1DLGFBQWEsQ0FBQ0QsbUJBQW1CekYsWUFBWSxPQUFPLEtBQUssSUFBSUEsU0FBU1UsS0FBSyxLQUFLLE9BQU8rRSxtQkFBbUIsQ0FBQztZQUNqSCxpREFBaUQ7WUFDakQsMENBQTBDO1lBQzFDLE9BQU9FLFFBQVFELFdBQVdFLFFBQVE7UUFDcEM7UUFDQUMsZUFBYzdGLFFBQVEsRUFBRThGLEtBQUssRUFBRUMsUUFBUSxFQUFFeEMsUUFBUTtZQUMvQyxJQUFJeUM7WUFDSixNQUFNTixhQUFhLENBQUNNLG1CQUFtQmhHLFlBQVksT0FBTyxLQUFLLElBQUlBLFNBQVNVLEtBQUssS0FBSyxPQUFPc0YsbUJBQW1CLENBQUM7WUFFakgsbUJBQW1CO1lBQ25CLElBQUlOLFdBQVdyRixTQUFTLElBQUlrRCxTQUFTdEQsTUFBTSxJQUFJc0QsU0FBU3RELE1BQU0sS0FBS0QsVUFBVTtnQkFDM0UsT0FBTztvQkFBQztpQkFBSztZQUNmLE9BQU87Z0JBQ0wscUVBQXFFO2dCQUNyRSxNQUFNLEVBQ0pQLE1BQU13RyxVQUFVLEVBQUUsRUFDbEJqRSxVQUFVa0UsRUFBRSxFQUNaLEdBQUdDLFNBQ0osR0FBRzVDO2dCQUNKLE1BQU0sRUFDSjlELE1BQU0yRyxVQUFVLEVBQUUsRUFDbEJwRSxVQUFVcUUsRUFBRSxFQUNaLEdBQUdDLFNBQ0osR0FBR1A7Z0JBRUosb0RBQW9EO2dCQUNwRCxJQUFJLENBQUN4RixNQUFNQyxPQUFPLENBQUN5RixVQUFVLE1BQU0sSUFBSTlGLE1BQU07Z0JBRTdDLHdFQUF3RTtnQkFDeEUsSUFBSThGLFFBQVFNLElBQUksQ0FBQyxDQUFDQyxPQUFPckUsUUFBVXFFLFVBQVVKLE9BQU8sQ0FBQ2pFLE1BQU0sR0FBRyxPQUFPO29CQUFDO2lCQUFLO2dCQUMzRSxtREFBbUQ7Z0JBQ25ELE1BQU1zRSxPQUFPQyxVQUFVMUcsVUFBVW1HLFNBQVNHLFNBQVM7Z0JBQ25ELElBQUlHLEtBQUtFLE9BQU8sQ0FBQ0MsTUFBTSxFQUFFLE9BQU87b0JBQUM7b0JBQU9IO2lCQUFLO2dCQUU3QyxzQ0FBc0M7Z0JBQ3RDLE9BQU87WUFDVDtRQUNGO1FBQ0FJLGNBQWE3RyxRQUFRLEVBQUUsQ0FBQzhHLGFBQWFMLEtBQUssRUFBRWpILElBQUksRUFBRXVILFNBQVMsRUFBRXhELFFBQVEsRUFBRUMsS0FBSztZQUMxRSxnRUFBZ0U7WUFDaEUsSUFBSXNELGFBQWF4RCxlQUFldEQsVUFBVVIsTUFBTStELFVBQVVDO2lCQUVyRDNDLGFBQWFiLFVBQVV5RztRQUM5QjtRQUNBTyxhQUFZaEgsUUFBUSxFQUFFOEYsS0FBSyxFQUFFbUIsTUFBTSxFQUFFQyxJQUFJO1lBQ3ZDLElBQUlDO1lBQ0osaURBQWlEO1lBQ2pELDBFQUEwRTtZQUMxRSxNQUFNekIsYUFBYSxDQUFDeUIsbUJBQW1CbkgsU0FBU1UsS0FBSyxLQUFLLE9BQU95RyxtQkFBbUIsQ0FBQztZQUNyRixJQUFJbkgsU0FBUzRELE9BQU8sSUFBSThCLFdBQVdFLFFBQVEsSUFBSUYsV0FBVzdCLFVBQVUsRUFBRTtnQkFDcEU3RCxTQUFTVSxLQUFLLENBQUNkLElBQUksQ0FBQ21FLFFBQVEsR0FBR0MsUUFBUSxDQUFDQyxXQUFXLENBQUMzQyxJQUFJLENBQUN0QjtZQUMzRDtRQUNGO1FBQ0FvSCxtQkFBbUJwSCxDQUFBQSxXQUFZQTtRQUMvQnFILGtCQUFrQixJQUFNO1FBQ3hCQyxvQkFBb0JyQyxDQUFBQSxZQUFhN0UsUUFBUTZFLFVBQVVsQixRQUFRLEdBQUdtQixLQUFLO1FBQ25FcUMsa0JBQWtCLEtBQU87UUFDekJDLHNCQUFzQixJQUFNO1FBQzVCQyxnQkFBZ0IsSUFBTTtRQUN0QkMsY0FBYTFILFFBQVE7WUFDbkIsSUFBSTJIO1lBQ0osc0NBQXNDO1lBQ3RDLE1BQU0sRUFDSmpJLFFBQVFGLElBQUksRUFDWitCLE1BQU0sRUFDUCxHQUFHLENBQUNvRyxtQkFBbUIzSCxTQUFTVSxLQUFLLEtBQUssT0FBT2lILG1CQUFtQixDQUFDO1lBQ3RFLElBQUluSSxRQUFRK0IsUUFBUXVCLE9BQU92QixRQUFRdkIsVUFBVVI7WUFDN0MsSUFBSVEsU0FBU29CLFVBQVUsRUFBRXBCLFNBQVM0SCxPQUFPLEdBQUc7WUFDNUNuRyxtQkFBbUJ6QjtRQUNyQjtRQUNBNkgsZ0JBQWU3SCxRQUFRLEVBQUVMLEtBQUs7WUFDNUIsSUFBSW1JO1lBQ0osMENBQTBDO1lBQzFDLE1BQU0sRUFDSnBJLFFBQVFGLElBQUksRUFDWitCLE1BQU0sRUFDUCxHQUFHLENBQUN1RyxtQkFBbUI5SCxTQUFTVSxLQUFLLEtBQUssT0FBT29ILG1CQUFtQixDQUFDO1lBQ3RFLElBQUl0SSxRQUFRK0IsUUFBUTdCLE9BQU82QixRQUFRdkIsVUFBVVI7WUFDN0MsSUFBSVEsU0FBU29CLFVBQVUsSUFBSXpCLE1BQU1pSSxPQUFPLElBQUksUUFBUWpJLE1BQU1pSSxPQUFPLEVBQUU1SCxTQUFTNEgsT0FBTyxHQUFHO1lBQ3RGbkcsbUJBQW1CekI7UUFDckI7UUFDQStILG9CQUFvQnpEO1FBQ3BCMEQsa0JBQWtCMUQ7UUFDbEIyRCxvQkFBb0IzRDtRQUNwQiw4RUFBOEU7UUFDOUUsYUFBYTtRQUNiNEQseUJBQXlCLElBQU01SSxvQkFBb0JBLHNCQUFzQnJCLDRFQUFvQkE7UUFDN0ZrSywwQkFBMEIsS0FBTztRQUNqQ0MseUJBQXlCLEtBQU87UUFDaENDLHVCQUF1QixLQUFPO1FBQzlCQyxLQUFLLE9BQU9DLGdCQUFnQixlQUFlQyxHQUFHQyxHQUFHLENBQUNGLFlBQVlELEdBQUcsSUFBSUMsWUFBWUQsR0FBRyxHQUFHRSxHQUFHQyxHQUFHLENBQUNDLEtBQUtKLEdBQUcsSUFBSUksS0FBS0osR0FBRyxHQUFHLElBQU07UUFDM0gsOEVBQThFO1FBQzlFSyxpQkFBaUJILEdBQUdDLEdBQUcsQ0FBQ0csY0FBY0EsYUFBYTFJO1FBQ25EMkksZUFBZUwsR0FBR0MsR0FBRyxDQUFDSyxnQkFBZ0JBLGVBQWU1STtJQUN2RDtJQUNBLE9BQU87UUFDTHVFO1FBQ0FzRSxZQUFZbEk7SUFDZDtBQUNGO0FBRUEsSUFBSW1JLGtCQUFrQkM7QUFDdEI7O0NBRUMsR0FDRCxNQUFNQyxnQkFBZ0JqSixDQUFBQSxTQUFVLGdCQUFnQkEsVUFBVSxzQkFBc0JBO0FBQ2hGOztDQUVDLEdBQ0QsTUFBTWtKLHFCQUFxQjtJQUN6QixJQUFJQztJQUNKLE9BQU8sQ0FBQ0EsbUJBQW1CcEssVUFBVXFLLGVBQWUsS0FBSyxPQUFPRCxtQkFBbUI7QUFDckY7QUFDQSxNQUFNRSx1QkFBdUJDLENBQUFBLE1BQU9BLE9BQU9BLElBQUlELG9CQUFvQjtBQUNuRSxNQUFNRSxRQUFRQyxDQUFBQSxNQUFPQSxPQUFPQSxJQUFJQyxjQUFjLENBQUM7QUFFL0M7Ozs7Ozs7O0NBUUMsR0FDRCxNQUFNQyw0QkFBNEIsTUFBME4sR0FBSzNMLENBQXFCLEdBQUdBLDRDQUFlO0FBQ3hTLFNBQVNtTSxtQkFBbUJDLEVBQUU7SUFDNUIsTUFBTWhHLE1BQU1wRyx5Q0FBWSxDQUFDb007SUFDekJULDBCQUEwQixJQUFNLEtBQU12RixDQUFBQSxJQUFJQyxPQUFPLEdBQUcrRixFQUFDLEdBQUk7UUFBQ0E7S0FBRztJQUM3RCxPQUFPaEc7QUFDVDtBQUNBLFNBQVNrRyxNQUFNLEVBQ2JDLEdBQUcsRUFDSjtJQUNDWiwwQkFBMEI7UUFDeEJZLElBQUksSUFBSUMsUUFBUSxJQUFNO1FBQ3RCLE9BQU8sSUFBTUQsSUFBSTtJQUNuQixHQUFHO1FBQUNBO0tBQUk7SUFDUixPQUFPO0FBQ1Q7QUFDQSxNQUFNRSxzQkFBc0J6TSw0Q0FBZTtJQUN6QzJNLFlBQVksR0FBR2xMLElBQUksQ0FBRTtRQUNuQixLQUFLLElBQUlBO1FBQ1QsSUFBSSxDQUFDbUwsS0FBSyxHQUFHO1lBQ1hDLE9BQU87UUFDVDtJQUNGO0lBQ0FDLGtCQUFrQkMsR0FBRyxFQUFFO1FBQ3JCLElBQUksQ0FBQ3BMLEtBQUssQ0FBQzRLLEdBQUcsQ0FBQ1E7SUFDakI7SUFDQUMsU0FBUztRQUNQLE9BQU8sSUFBSSxDQUFDSixLQUFLLENBQUNDLEtBQUssR0FBRyxPQUFPLElBQUksQ0FBQ2xMLEtBQUssQ0FBQ3FDLFFBQVE7SUFDdEQ7QUFDRjtBQUNBeUksY0FBY1Esd0JBQXdCLEdBQUcsSUFBTztRQUM5Q0osT0FBTztJQUNUO0FBQ0EsTUFBTUssVUFBVTtBQUNoQixNQUFNQyxXQUFXLElBQUlDO0FBQ3JCLE1BQU1DLFlBQVk5QixDQUFBQSxNQUFPQSxPQUFPLENBQUMsQ0FBQ0EsSUFBSStCLFFBQVEsSUFBSSxDQUFDLENBQUMvQixJQUFJNUMsT0FBTztBQUMvRCxTQUFTNEUsYUFBYUMsR0FBRztJQUN2QixJQUFJQztJQUNKLHVFQUF1RTtJQUN2RSxxRUFBcUU7SUFDckUsTUFBTW5MLFNBQVMsTUFBa0IsR0FBYyxDQUFxRixHQUFHO0lBQ3ZJLE9BQU9DLE1BQU1DLE9BQU8sQ0FBQ2dMLE9BQU9HLEtBQUtDLEdBQUcsQ0FBQ0QsS0FBS0UsR0FBRyxDQUFDTCxHQUFHLENBQUMsRUFBRSxFQUFFbEwsU0FBU2tMLEdBQUcsQ0FBQyxFQUFFLElBQUlBO0FBQzNFO0FBRUE7O0NBRUMsR0FDRCxNQUFNTSxlQUFlckMsQ0FBQUE7SUFDbkIsSUFBSXNDO0lBQ0osT0FBTyxDQUFDQSxPQUFPdEMsSUFBSS9JLEtBQUssS0FBSyxPQUFPLEtBQUssSUFBSXFMLEtBQUtuTSxJQUFJLENBQUNtRSxRQUFRO0FBQ2pFO0FBQ0Esb0NBQW9DO0FBQ3BDLE1BQU15RSxLQUFLO0lBQ1RpQixLQUFLdUMsQ0FBQUEsSUFBS0EsTUFBTW5OLE9BQU9tTixNQUFNLENBQUN4RCxHQUFHeUQsR0FBRyxDQUFDRCxNQUFNLE9BQU9BLE1BQU07SUFDeER2RCxLQUFLdUQsQ0FBQUEsSUFBSyxPQUFPQSxNQUFNO0lBQ3ZCRSxLQUFLRixDQUFBQSxJQUFLLE9BQU9BLE1BQU07SUFDdkJHLEtBQUtILENBQUFBLElBQUssT0FBT0EsTUFBTTtJQUN2QkksS0FBS0osQ0FBQUEsSUFBSyxPQUFPQSxNQUFNO0lBQ3ZCSyxLQUFLTCxDQUFBQSxJQUFLQSxNQUFNLEtBQUs7SUFDckJDLEtBQUtELENBQUFBLElBQUt6TCxNQUFNQyxPQUFPLENBQUN3TDtJQUN4Qk0sS0FBSU4sQ0FBQyxFQUFFTyxDQUFDLEVBQUUsRUFDUkMsU0FBUyxTQUFTLEVBQ2xCdE4sVUFBVSxXQUFXLEVBQ3JCdU4sU0FBUyxJQUFJLEVBQ2QsR0FBRyxDQUFDLENBQUM7UUFDSix3REFBd0Q7UUFDeEQsSUFBSSxPQUFPVCxNQUFNLE9BQU9PLEtBQUssQ0FBQyxDQUFDUCxNQUFNLENBQUMsQ0FBQ08sR0FBRyxPQUFPO1FBQ2pELG1DQUFtQztRQUNuQyxJQUFJL0QsR0FBRzBELEdBQUcsQ0FBQ0YsTUFBTXhELEdBQUcyRCxHQUFHLENBQUNILElBQUksT0FBT0EsTUFBTU87UUFDekMsTUFBTUcsUUFBUWxFLEdBQUdpQixHQUFHLENBQUN1QztRQUNyQixJQUFJVSxTQUFTeE4sWUFBWSxhQUFhLE9BQU84TSxNQUFNTztRQUNuRCxNQUFNSSxRQUFRbkUsR0FBR3lELEdBQUcsQ0FBQ0Q7UUFDckIsSUFBSVcsU0FBU0gsV0FBVyxhQUFhLE9BQU9SLE1BQU1PO1FBQ2xELGdFQUFnRTtRQUNoRSxJQUFJLENBQUNJLFNBQVNELEtBQUksS0FBTVYsTUFBTU8sR0FBRyxPQUFPO1FBQ3hDLCtCQUErQjtRQUMvQixJQUFJSztRQUNKLG1DQUFtQztRQUNuQyxJQUFLQSxLQUFLWixFQUFHLElBQUksQ0FBRVksQ0FBQUEsS0FBS0wsQ0FBQUEsR0FBSSxPQUFPO1FBQ25DLHFDQUFxQztRQUNyQyxJQUFJRyxTQUFTRixXQUFXLGFBQWF0TixZQUFZLFdBQVc7WUFDMUQsSUFBSzBOLEtBQUtILFNBQVNGLElBQUlQLEVBQUcsSUFBSSxDQUFDeEQsR0FBRzhELEdBQUcsQ0FBQ04sQ0FBQyxDQUFDWSxFQUFFLEVBQUVMLENBQUMsQ0FBQ0ssRUFBRSxFQUFFO2dCQUNoREg7Z0JBQ0F2TixTQUFTO1lBQ1gsSUFBSSxPQUFPO1FBQ2IsT0FBTztZQUNMLElBQUswTixLQUFLSCxTQUFTRixJQUFJUCxFQUFHLElBQUlBLENBQUMsQ0FBQ1ksRUFBRSxLQUFLTCxDQUFDLENBQUNLLEVBQUUsRUFBRSxPQUFPO1FBQ3REO1FBQ0Esb0JBQW9CO1FBQ3BCLElBQUlwRSxHQUFHNkQsR0FBRyxDQUFDTyxJQUFJO1lBQ2Isa0RBQWtEO1lBQ2xELElBQUlELFNBQVNYLEVBQUVwRixNQUFNLEtBQUssS0FBSzJGLEVBQUUzRixNQUFNLEtBQUssR0FBRyxPQUFPO1lBQ3RELG1EQUFtRDtZQUNuRCxJQUFJOEYsU0FBUzdOLE9BQU9nTyxJQUFJLENBQUNiLEdBQUdwRixNQUFNLEtBQUssS0FBSy9ILE9BQU9nTyxJQUFJLENBQUNOLEdBQUczRixNQUFNLEtBQUssR0FBRyxPQUFPO1lBQ2hGLGdDQUFnQztZQUNoQyxJQUFJb0YsTUFBTU8sR0FBRyxPQUFPO1FBQ3RCO1FBQ0EsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELFNBQVNPLFdBQVc3TSxNQUFNO0lBQ3hCLE1BQU04TSxPQUFPO1FBQ1hDLE9BQU8sQ0FBQztRQUNSQyxXQUFXLENBQUM7SUFDZDtJQUNBLElBQUloTixRQUFRO1FBQ1ZBLE9BQU9pTixRQUFRLENBQUN6RCxDQUFBQTtZQUNkLElBQUlBLElBQUk1SixJQUFJLEVBQUVrTixLQUFLQyxLQUFLLENBQUN2RCxJQUFJNUosSUFBSSxDQUFDLEdBQUc0SjtZQUNyQyxJQUFJQSxJQUFJMEQsUUFBUSxJQUFJLENBQUNKLEtBQUtFLFNBQVMsQ0FBQ3hELElBQUkwRCxRQUFRLENBQUN0TixJQUFJLENBQUMsRUFBRWtOLEtBQUtFLFNBQVMsQ0FBQ3hELElBQUkwRCxRQUFRLENBQUN0TixJQUFJLENBQUMsR0FBRzRKLElBQUkwRCxRQUFRO1FBQzFHO0lBQ0Y7SUFDQSxPQUFPSjtBQUNUO0FBRUEsNENBQTRDO0FBQzVDLFNBQVN4SyxRQUFRa0gsR0FBRztJQUNsQixJQUFJQSxJQUFJbEgsT0FBTyxJQUFJa0gsSUFBSWpLLElBQUksS0FBSyxTQUFTaUssSUFBSWxILE9BQU87SUFDcEQsSUFBSyxNQUFNNkssS0FBSzNELElBQUs7UUFDbkIyRCxFQUFFN0ssT0FBTyxJQUFJLE9BQU8sS0FBSyxJQUFJNkssRUFBRTdLLE9BQU87UUFDdEMsT0FBT2tILEdBQUcsQ0FBQzJELEVBQUU7SUFDZjtBQUNGO0FBRUEsaUVBQWlFO0FBQ2pFLFNBQVNoTixRQUFRSCxNQUFNLEVBQUUySyxLQUFLO0lBQzVCLE1BQU01SyxXQUFXQztJQUNqQkQsU0FBU1UsS0FBSyxHQUFHO1FBQ2ZsQixNQUFNO1FBQ05JLE1BQU07UUFDTnlOLGdCQUFnQjtRQUNoQjVNLGVBQWUsQ0FBQztRQUNoQm9ELFlBQVk7UUFDWitCLFVBQVUsQ0FBQztRQUNYMUcsU0FBUyxFQUFFO1FBQ1hxQyxRQUFRO1FBQ1IsR0FBR3FKLEtBQUs7SUFDVjtJQUNBLE9BQU8zSztBQUNUO0FBQ0EsU0FBU3FOLFFBQVF0TixRQUFRLEVBQUV1TixHQUFHO0lBQzVCLElBQUlqTixTQUFTTjtJQUNiLElBQUl1TixJQUFJQyxRQUFRLENBQUMsTUFBTTtRQUNyQixNQUFNQyxVQUFVRixJQUFJRyxLQUFLLENBQUM7UUFDMUIsTUFBTUMsT0FBT0YsUUFBUUcsR0FBRztRQUN4QnROLFNBQVNtTixRQUFRSSxNQUFNLENBQUMsQ0FBQ0MsS0FBS1AsTUFBUU8sR0FBRyxDQUFDUCxJQUFJLEVBQUV2TjtRQUNoRCxPQUFPO1lBQ0xNO1lBQ0FpTixLQUFLSTtRQUNQO0lBQ0YsT0FBTyxPQUFPO1FBQ1pyTjtRQUNBaU47SUFDRjtBQUNGO0FBRUEscURBQXFEO0FBQ3JELE1BQU1RLGNBQWM7QUFDcEIsU0FBU3JPLE9BQU82QixNQUFNLEVBQUVQLEtBQUssRUFBRXhCLElBQUk7SUFDakMsSUFBSWdKLEdBQUcwRCxHQUFHLENBQUMxTSxPQUFPO1FBQ2hCLGlEQUFpRDtRQUNqRCxJQUFJdU8sWUFBWUMsSUFBSSxDQUFDeE8sT0FBTztZQUMxQixNQUFNSSxPQUFPSixLQUFLeU8sT0FBTyxDQUFDRixhQUFhO1lBQ3ZDLE1BQU0sRUFDSnpOLE1BQU0sRUFDTmlOLEdBQUcsRUFDSixHQUFHRCxRQUFRL0wsUUFBUTNCO1lBQ3BCLElBQUksQ0FBQ1csTUFBTUMsT0FBTyxDQUFDRixNQUFNLENBQUNpTixJQUFJLEdBQUdqTixNQUFNLENBQUNpTixJQUFJLEdBQUcsRUFBRTtRQUNuRDtRQUNBLE1BQU0sRUFDSmpOLE1BQU0sRUFDTmlOLEdBQUcsRUFDSixHQUFHRCxRQUFRL0wsUUFBUS9CO1FBQ3BCd0IsTUFBTU4sS0FBSyxDQUFDMk0sY0FBYyxHQUFHL00sTUFBTSxDQUFDaU4sSUFBSTtRQUN4Q2pOLE1BQU0sQ0FBQ2lOLElBQUksR0FBR3ZNO0lBQ2hCLE9BQU9BLE1BQU1OLEtBQUssQ0FBQzJNLGNBQWMsR0FBRzdOLEtBQUsrQixRQUFRUDtBQUNuRDtBQUNBLFNBQVM4QixPQUFPdkIsTUFBTSxFQUFFUCxLQUFLLEVBQUV4QixJQUFJO0lBQ2pDLElBQUkwQixjQUFjVTtJQUNsQixJQUFJNEcsR0FBRzBELEdBQUcsQ0FBQzFNLE9BQU87UUFDaEIsTUFBTSxFQUNKYyxNQUFNLEVBQ05pTixHQUFHLEVBQ0osR0FBR0QsUUFBUS9MLFFBQVEvQjtRQUNwQixNQUFNME8sV0FBV2xOLE1BQU1OLEtBQUssQ0FBQzJNLGNBQWM7UUFDM0Msd0ZBQXdGO1FBQ3hGLElBQUlhLGFBQWFoTyxXQUFXLE9BQU9JLE1BQU0sQ0FBQ2lOLElBQUk7YUFFekNqTixNQUFNLENBQUNpTixJQUFJLEdBQUdXO0lBQ3JCLE9BQU8sQ0FBQ2hOLGVBQWVGLE1BQU1OLEtBQUssS0FBSyxPQUFPLEtBQUssSUFBSVEsYUFBYW1NLGNBQWMsSUFBSSxPQUFPLEtBQUssSUFBSW5NLGFBQWFtTSxjQUFjLENBQUM5TCxRQUFRUDtJQUN6SVksQ0FBQUEsZ0JBQWdCWixNQUFNTixLQUFLLEtBQUssT0FBTyxPQUFPLE9BQU9rQixjQUFjeUwsY0FBYztBQUNwRjtBQUVBLHdFQUF3RTtBQUN4RSxTQUFTM0csVUFBVTFHLFFBQVEsRUFBRSxFQUMzQmdDLFVBQVVrRSxFQUFFLEVBQ1pxSCxLQUFLWSxFQUFFLEVBQ1AvSixLQUFLZ0ssRUFBRSxFQUNQLEdBQUd6TyxPQUNKLEVBQUUsRUFDRHFDLFVBQVVxTSxFQUFFLEVBQ1pkLEtBQUtlLEVBQUUsRUFDUGxLLEtBQUttSyxFQUFFLEVBQ1AsR0FBR0wsVUFDSixHQUFHLENBQUMsQ0FBQyxFQUFFbEwsU0FBUyxLQUFLO0lBQ3BCLElBQUlTO0lBQ0osTUFBTWlDLGFBQWEsQ0FBQ2pDLGtCQUFrQnpELFlBQVksT0FBTyxLQUFLLElBQUlBLFNBQVNVLEtBQUssS0FBSyxPQUFPK0Msa0JBQWtCLENBQUM7SUFDL0csTUFBTWdLLFVBQVU1TyxPQUFPNE8sT0FBTyxDQUFDOU47SUFDL0IsTUFBTWdILFVBQVUsRUFBRTtJQUVsQixvRUFBb0U7SUFDcEUsSUFBSTNELFFBQVE7UUFDVixNQUFNd0wsZUFBZTNQLE9BQU9nTyxJQUFJLENBQUNxQjtRQUNqQyxJQUFLLElBQUl0QixJQUFJLEdBQUdBLElBQUk0QixhQUFhNUgsTUFBTSxFQUFFZ0csSUFBSztZQUM1QyxJQUFJLENBQUNqTixNQUFNK0osY0FBYyxDQUFDOEUsWUFBWSxDQUFDNUIsRUFBRSxHQUFHYSxRQUFRZ0IsT0FBTyxDQUFDO2dCQUFDRCxZQUFZLENBQUM1QixFQUFFO2dCQUFFMUIsVUFBVTthQUFTO1FBQ25HO0lBQ0Y7SUFDQXVDLFFBQVFqTCxPQUFPLENBQUMsQ0FBQyxDQUFDK0ssS0FBSy9HLE1BQU07UUFDM0IsSUFBSWY7UUFDSiwrQkFBK0I7UUFDL0IsSUFBSSxDQUFDQSxtQkFBbUJ6RixTQUFTVSxLQUFLLEtBQUssUUFBUStFLGlCQUFpQnBGLFNBQVMsSUFBSWtOLFFBQVEsVUFBVTtRQUNuRyw0QkFBNEI7UUFDNUIsSUFBSS9FLEdBQUc4RCxHQUFHLENBQUM5RixPQUFPMEgsUUFBUSxDQUFDWCxJQUFJLEdBQUc7UUFDbEMsZ0NBQWdDO1FBQ2hDLElBQUksbURBQW1EUyxJQUFJLENBQUNULE1BQU0sT0FBTzVHLFFBQVFyRixJQUFJLENBQUM7WUFBQ2lNO1lBQUsvRztZQUFPO1lBQU0sRUFBRTtTQUFDO1FBQzVHLHFCQUFxQjtRQUNyQixJQUFJaUgsVUFBVSxFQUFFO1FBQ2hCLElBQUlGLElBQUlDLFFBQVEsQ0FBQyxNQUFNQyxVQUFVRixJQUFJRyxLQUFLLENBQUM7UUFDM0MvRyxRQUFRckYsSUFBSSxDQUFDO1lBQUNpTTtZQUFLL0c7WUFBTztZQUFPaUg7U0FBUTtRQUV6QyxzQkFBc0I7UUFDdEIsSUFBSyxNQUFNaUIsUUFBUS9PLE1BQU87WUFDeEIsTUFBTTZHLFFBQVE3RyxLQUFLLENBQUMrTyxLQUFLO1lBQ3pCLElBQUlBLEtBQUtDLFVBQVUsQ0FBQyxDQUFDLEVBQUVwQixJQUFJLENBQUMsQ0FBQyxHQUFHNUcsUUFBUXJGLElBQUksQ0FBQztnQkFBQ29OO2dCQUFNbEk7Z0JBQU87Z0JBQU9rSSxLQUFLaEIsS0FBSyxDQUFDO2FBQUs7UUFDcEY7SUFDRjtJQUNBLE1BQU1wQyxXQUFXO1FBQ2YsR0FBRzNMLEtBQUs7SUFDVjtJQUNBLElBQUkrRixXQUFXakYsYUFBYSxJQUFJaUYsV0FBV2pGLGFBQWEsQ0FBQ2hCLElBQUksRUFBRTZMLFNBQVM3TCxJQUFJLEdBQUdpRyxXQUFXakYsYUFBYSxDQUFDaEIsSUFBSTtJQUM1RyxJQUFJaUcsV0FBV2pGLGFBQWEsSUFBSWlGLFdBQVdqRixhQUFhLENBQUNmLE1BQU0sRUFBRTRMLFNBQVM1TCxNQUFNLEdBQUdnRyxXQUFXakYsYUFBYSxDQUFDZixNQUFNO0lBQ2xILE9BQU87UUFDTDRMO1FBQ0EzRTtJQUNGO0FBQ0Y7QUFDQSxNQUFNaUksVUFBVSxPQUFPQyxZQUFZLGVBQWVBLGtCQUF5QjtBQUUzRSx5REFBeUQ7QUFDekQsU0FBU2hPLGFBQWFiLFFBQVEsRUFBRStNLElBQUk7SUFDbEMsSUFBSS9HLGtCQUFrQjhJLGdCQUFnQjNIO0lBQ3RDLDJDQUEyQztJQUMzQyxNQUFNekIsYUFBYSxDQUFDTSxtQkFBbUJoRyxTQUFTVSxLQUFLLEtBQUssT0FBT3NGLG1CQUFtQixDQUFDO0lBQ3JGLE1BQU1wRyxPQUFPOEYsV0FBVzlGLElBQUk7SUFDNUIsTUFBTWtFLFlBQVksQ0FBQ2dMLGlCQUFpQmxQLFFBQVEsT0FBTyxLQUFLLElBQUlBLEtBQUttRSxRQUFRLElBQUksT0FBTyxLQUFLLElBQUluRSxLQUFLbUUsUUFBUSxFQUFDLEtBQU0sT0FBTytLLGlCQUFpQixDQUFDO0lBQzFJLE1BQU0sRUFDSnhELFFBQVEsRUFDUjNFLE9BQU8sRUFDUixHQUFHMEUsVUFBVTBCLFFBQVFBLE9BQU9yRyxVQUFVMUcsVUFBVStNO0lBQ2pELE1BQU1nQyxlQUFlckosV0FBVzdCLFVBQVU7SUFFMUMseUJBQXlCO0lBQ3pCLElBQUk3RCxTQUFTVSxLQUFLLEVBQUVWLFNBQVNVLEtBQUssQ0FBQ0QsYUFBYSxHQUFHNks7SUFDbkQsSUFBSyxJQUFJc0IsSUFBSSxHQUFHQSxJQUFJakcsUUFBUUMsTUFBTSxFQUFFZ0csSUFBSztRQUN2QyxJQUFJLENBQUNXLEtBQUsvRyxPQUFPd0ksU0FBU25DLEtBQUssR0FBR2xHLE9BQU8sQ0FBQ2lHLEVBQUU7UUFFNUMsNERBQTREO1FBQzVELHdEQUF3RDtRQUN4RCxJQUFJMUQsY0FBY2xKLFdBQVc7WUFDM0IsTUFBTWlQLGVBQWU7WUFDckIsTUFBTUMsaUJBQWlCO1lBQ3ZCLE1BQU1DLHVCQUF1QjtZQUM3QixJQUFJNUIsUUFBUSxZQUFZO2dCQUN0QkEsTUFBTTtnQkFDTi9HLFFBQVFBLFVBQVV5SSxlQUFlQyxpQkFBaUJDO1lBQ3BELE9BQU8sSUFBSTVCLFFBQVEsa0JBQWtCO2dCQUNuQ0EsTUFBTTtnQkFDTi9HLFFBQVFBLFVBQVV5SSxlQUFlQyxpQkFBaUJDO1lBQ3BEO1FBQ0Y7UUFDQSxJQUFJQyxrQkFBa0JwUDtRQUN0QixJQUFJcVAsYUFBYUQsZUFBZSxDQUFDN0IsSUFBSTtRQUVyQyx1QkFBdUI7UUFDdkIsSUFBSVYsS0FBS2pHLE1BQU0sRUFBRTtZQUNmeUksYUFBYXhDLEtBQUtnQixNQUFNLENBQUMsQ0FBQ0MsS0FBS1AsTUFBUU8sR0FBRyxDQUFDUCxJQUFJLEVBQUV2TjtZQUNqRCwyREFBMkQ7WUFDM0QsSUFBSSxDQUFFcVAsQ0FBQUEsY0FBY0EsV0FBVzlFLEdBQUcsR0FBRztnQkFDbkMsTUFBTSxDQUFDMUssTUFBTSxHQUFHeVAsZUFBZSxHQUFHekMsS0FBSzBDLE9BQU87Z0JBQzlDSCxrQkFBa0JFLGVBQWVDLE9BQU8sR0FBRzFCLE1BQU0sQ0FBQyxDQUFDQyxLQUFLUCxNQUFRTyxHQUFHLENBQUNQLElBQUksRUFBRXZOO2dCQUMxRXVOLE1BQU0xTjtZQUNSO1FBQ0Y7UUFFQSxrREFBa0Q7UUFDbEQsMEVBQTBFO1FBQzFFLCtFQUErRTtRQUMvRSxrREFBa0Q7UUFDbEQsNERBQTREO1FBQzVELElBQUkyRyxVQUFVMEUsVUFBVSxVQUFVO1lBQ2hDLElBQUlrRSxnQkFBZ0J6RSxXQUFXLEVBQUU7Z0JBQy9CLDBFQUEwRTtnQkFDMUUsSUFBSTZFLE9BQU9yRSxTQUFTc0UsR0FBRyxDQUFDTCxnQkFBZ0J6RSxXQUFXO2dCQUNuRCxJQUFJLENBQUM2RSxNQUFNO29CQUNULGFBQWE7b0JBQ2JBLE9BQU8sSUFBSUosZ0JBQWdCekUsV0FBVztvQkFDdENRLFNBQVNaLEdBQUcsQ0FBQzZFLGdCQUFnQnpFLFdBQVcsRUFBRTZFO2dCQUM1QztnQkFDQWhKLFFBQVFnSixJQUFJLENBQUNqQyxJQUFJO1lBQ25CLE9BQU87Z0JBQ0wsdURBQXVEO2dCQUN2RC9HLFFBQVE7WUFDVjtRQUNGO1FBRUEsK0JBQStCO1FBQy9CLElBQUl3SSxTQUFTO1lBQ1gsSUFBSXhJLE9BQU9kLFdBQVdFLFFBQVEsQ0FBQzJILElBQUksR0FBRy9HO2lCQUFXLE9BQU9kLFdBQVdFLFFBQVEsQ0FBQzJILElBQUk7WUFDaEY3SCxXQUFXN0IsVUFBVSxHQUFHaEYsT0FBT2dPLElBQUksQ0FBQ25ILFdBQVdFLFFBQVEsRUFBRWdCLE1BQU07UUFDakUsT0FFSyxJQUFJeUksY0FBY0EsV0FBVzlFLEdBQUcsSUFBSzhFLENBQUFBLFdBQVdLLElBQUksSUFBSUwsc0JBQXNCdFIseUNBQVksR0FBRztZQUNoRyx1QkFBdUI7WUFDdkIsSUFBSXdDLE1BQU1DLE9BQU8sQ0FBQ2dHLFFBQVE7Z0JBQ3hCLElBQUk2SSxXQUFXTyxTQUFTLEVBQUVQLFdBQVdPLFNBQVMsQ0FBQ3BKO3FCQUFZNkksV0FBVzlFLEdBQUcsSUFBSS9EO1lBQy9FLE9BRUssSUFBSTZJLFdBQVdLLElBQUksSUFBSWxKLFNBQVNBLE1BQU1tRSxXQUFXLElBQ3RELDBGQUEwRjtZQUMxRixvREFBb0Q7WUFDcEQsMERBQTBEO1lBQzFELHNDQUFzQztZQUN0Q2lFLENBQUFBLFVBQVVTLFdBQVcxRSxXQUFXLENBQUM5SyxJQUFJLEtBQUsyRyxNQUFNbUUsV0FBVyxDQUFDOUssSUFBSSxHQUFHd1AsV0FBVzFFLFdBQVcsS0FBS25FLE1BQU1tRSxXQUFXLEdBQUc7Z0JBQ2hIMEUsV0FBV0ssSUFBSSxDQUFDbEo7WUFDbEIsT0FHSyxJQUFJQSxVQUFVdEcsV0FBVztnQkFDNUIsTUFBTTJQLFVBQVVSLHNCQUFzQnRSLHdDQUFXO2dCQUNqRCw4QkFBOEI7Z0JBQzlCLElBQUksQ0FBQzhSLFdBQVdSLFdBQVdVLFNBQVMsRUFBRVYsV0FBV1UsU0FBUyxDQUFDdko7cUJBRXRELElBQUk2SSxzQkFBc0J0Uix5Q0FBWSxJQUFJeUksaUJBQWlCekkseUNBQVksRUFBRXNSLFdBQVdXLElBQUksR0FBR3hKLE1BQU13SixJQUFJO3FCQUVyR1gsV0FBVzlFLEdBQUcsQ0FBQy9EO2dCQUNwQixtRUFBbUU7Z0JBQ25FLDJCQUEyQjtnQkFDM0IseURBQXlEO2dCQUN6RCxJQUFJLENBQUMyQyx3QkFBd0IsQ0FBQ3JGLFVBQVVtTSxNQUFNLElBQUlKLFNBQVNSLFdBQVdhLG1CQUFtQjtZQUMzRjtRQUNBLGlDQUFpQztRQUNuQyxPQUFPO1lBQ0xkLGVBQWUsQ0FBQzdCLElBQUksR0FBRy9HO1lBRXZCLDBDQUEwQztZQUMxQyx5REFBeUQ7WUFDekQsSUFBSTRJLGVBQWUsQ0FBQzdCLElBQUksWUFBWXhQLDBDQUFhLElBQ2pELHVGQUF1RjtZQUN2RnFSLGVBQWUsQ0FBQzdCLElBQUksQ0FBQzZDLE1BQU0sS0FBS3JTLDZDQUFnQixJQUFJcVIsZUFBZSxDQUFDN0IsSUFBSSxDQUFDL04sSUFBSSxLQUFLekIsbURBQXNCLEVBQUU7Z0JBQ3hHLE1BQU13UyxVQUFVbkIsZUFBZSxDQUFDN0IsSUFBSTtnQkFDcEMsSUFBSXJFLGNBQWNxSCxZQUFZckgsY0FBY3BGLFVBQVUwTSxFQUFFLEdBQUdELFFBQVFFLFVBQVUsR0FBRzNNLFVBQVUwTSxFQUFFLENBQUNFLGdCQUFnQjtxQkFBTUgsUUFBUUksUUFBUSxHQUFHN00sVUFBVTBNLEVBQUUsQ0FBQ0ksY0FBYztZQUNuSztRQUNGO1FBQ0FuUCxtQkFBbUJ6QjtJQUNyQjtJQUNBLElBQUkwRixXQUFXbkUsTUFBTSxJQUFJdUMsVUFBVUUsUUFBUSxJQUFJaEUsU0FBUzRELE9BQU8sSUFBSW1MLGlCQUFpQnJKLFdBQVc3QixVQUFVLEVBQUU7UUFDekcsaUVBQWlFO1FBQ2pFLE1BQU0xQixRQUFRMkIsVUFBVUUsUUFBUSxDQUFDQyxXQUFXLENBQUM3QixPQUFPLENBQUNwQztRQUNyRCxJQUFJbUMsUUFBUSxDQUFDLEdBQUcyQixVQUFVRSxRQUFRLENBQUNDLFdBQVcsQ0FBQzRNLE1BQU0sQ0FBQzFPLE9BQU87UUFDN0Qsd0VBQXdFO1FBQ3hFLElBQUl1RCxXQUFXN0IsVUFBVSxFQUFFQyxVQUFVRSxRQUFRLENBQUNDLFdBQVcsQ0FBQzNDLElBQUksQ0FBQ3RCO0lBQ2pFO0lBRUEsNkZBQTZGO0lBQzdGLDZDQUE2QztJQUM3QyxNQUFNOFEsYUFBYW5LLFFBQVFDLE1BQU0sS0FBSyxLQUFLRCxPQUFPLENBQUMsRUFBRSxDQUFDLEVBQUUsS0FBSztJQUM3RCxJQUFJLENBQUNtSyxjQUFjbkssUUFBUUMsTUFBTSxJQUFJLENBQUNPLG1CQUFtQm5ILFNBQVNVLEtBQUssS0FBSyxRQUFReUcsaUJBQWlCNUYsTUFBTSxFQUFFQyxlQUFleEI7SUFDNUgsT0FBT0E7QUFDVDtBQUNBLFNBQVN5QixtQkFBbUJ6QixRQUFRO0lBQ2xDLElBQUkySCxrQkFBa0JvSjtJQUN0QixNQUFNbkcsUUFBUSxDQUFDakQsbUJBQW1CM0gsU0FBU1UsS0FBSyxLQUFLLE9BQU8sS0FBSyxJQUFJLENBQUNxUSx3QkFBd0JwSixpQkFBaUIvSCxJQUFJLEtBQUssT0FBTyxLQUFLLElBQUltUixzQkFBc0JoTixRQUFRLElBQUksT0FBTyxLQUFLLElBQUlnTixzQkFBc0JoTixRQUFRO0lBQ3hOLElBQUk2RyxTQUFTQSxNQUFNNUcsUUFBUSxDQUFDZ04sTUFBTSxLQUFLLEdBQUdwRyxNQUFNcUcsVUFBVTtBQUM1RDtBQUNBLFNBQVN6UCxlQUFleEIsUUFBUTtJQUM5QkEsU0FBU2tSLFFBQVEsSUFBSSxPQUFPLEtBQUssSUFBSWxSLFNBQVNrUixRQUFRLENBQUNsUjtBQUN6RDtBQUNBLFNBQVNtUixhQUFhQyxNQUFNLEVBQUVDLElBQUk7SUFDaEMsd0RBQXdEO0lBQ3hELHdEQUF3RDtJQUN4RCxJQUFJLENBQUNELE9BQU9FLE1BQU0sRUFBRTtRQUNsQixJQUFJaEkscUJBQXFCOEgsU0FBUztZQUNoQ0EsT0FBT0csSUFBSSxHQUFHRixLQUFLRyxLQUFLLEdBQUcsQ0FBQztZQUM1QkosT0FBT0ssS0FBSyxHQUFHSixLQUFLRyxLQUFLLEdBQUc7WUFDNUJKLE9BQU9NLEdBQUcsR0FBR0wsS0FBS00sTUFBTSxHQUFHO1lBQzNCUCxPQUFPUSxNQUFNLEdBQUdQLEtBQUtNLE1BQU0sR0FBRyxDQUFDO1FBQ2pDLE9BQU87WUFDTFAsT0FBT1MsTUFBTSxHQUFHUixLQUFLRyxLQUFLLEdBQUdILEtBQUtNLE1BQU07UUFDMUM7UUFDQVAsT0FBT1Usc0JBQXNCO1FBQzdCLHlEQUF5RDtRQUN6RCx5REFBeUQ7UUFDekRWLE9BQU9XLGlCQUFpQjtJQUMxQjtBQUNGO0FBRUEsU0FBU0MsT0FBT0MsS0FBSztJQUNuQixPQUFPLENBQUNBLE1BQU1DLFdBQVcsSUFBSUQsTUFBTWhTLE1BQU0sRUFBRWtTLElBQUksR0FBRyxNQUFNRixNQUFNOVAsS0FBSyxHQUFHOFAsTUFBTUcsVUFBVTtBQUN4RjtBQUVBLGdHQUFnRztBQUNoRyxpRUFBaUU7QUFDakUsU0FBU0M7SUFDUCxJQUFJQztJQUNKLGlGQUFpRjtJQUNqRix3REFBd0Q7SUFDeEQsTUFBTUMsY0FBYyxPQUFPQyxTQUFTLGVBQWVBLFFBQVEsTUFBa0IsSUFBZTVJLENBQU1BO0lBQ2xHLElBQUksQ0FBQzJJLGFBQWEsT0FBT3RVLDRFQUFvQkE7SUFDN0MsTUFBTTRCLE9BQU8sQ0FBQ3lTLHFCQUFxQkMsWUFBWU4sS0FBSyxLQUFLLE9BQU8sS0FBSyxJQUFJSyxtQkFBbUI5UyxJQUFJO0lBQ2hHLE9BQVFLO1FBQ04sS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1lBQ0gsT0FBTzFCLDZFQUFxQkE7UUFDOUIsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1lBQ0gsT0FBT0QsK0VBQXVCQTtRQUNoQztZQUNFLE9BQU9ELDRFQUFvQkE7SUFDL0I7QUFDRjtBQUVBOzs7Q0FHQyxHQUNELFNBQVN3VSw4QkFBOEJDLFdBQVcsRUFBRWpKLEdBQUcsRUFBRWtKLFFBQVEsRUFBRUMsU0FBUztJQUMxRSxNQUFNQyxjQUFjRixTQUFTbEQsR0FBRyxDQUFDaEc7SUFDakMsSUFBSW9KLGFBQWE7UUFDZkYsU0FBU0csTUFBTSxDQUFDcko7UUFDaEIseURBQXlEO1FBQ3pELElBQUlrSixTQUFTdEIsSUFBSSxLQUFLLEdBQUc7WUFDdkJxQixZQUFZSSxNQUFNLENBQUNGO1lBQ25CQyxZQUFZdlMsTUFBTSxDQUFDeVMscUJBQXFCLENBQUNIO1FBQzNDO0lBQ0Y7QUFDRjtBQUNBLFNBQVMzUCxvQkFBb0IrUCxLQUFLLEVBQUUvUyxNQUFNO0lBQ3hDLE1BQU0sRUFDSitELFFBQVEsRUFDVCxHQUFHZ1AsTUFBTWpQLFFBQVE7SUFDbEIsdURBQXVEO0lBQ3ZEQyxTQUFTQyxXQUFXLEdBQUdELFNBQVNDLFdBQVcsQ0FBQ2hDLE1BQU0sQ0FBQ2dSLENBQUFBLElBQUtBLE1BQU1oVDtJQUM5RCtELFNBQVNrUCxXQUFXLEdBQUdsUCxTQUFTa1AsV0FBVyxDQUFDalIsTUFBTSxDQUFDZ1IsQ0FBQUEsSUFBS0EsTUFBTWhUO0lBQzlEK0QsU0FBU21QLE9BQU8sQ0FBQzNRLE9BQU8sQ0FBQyxDQUFDZ0UsT0FBTytHO1FBQy9CLElBQUkvRyxNQUFNMEwsV0FBVyxLQUFLalMsVUFBVXVHLE1BQU12RyxNQUFNLEtBQUtBLFFBQVE7WUFDM0QsaURBQWlEO1lBQ2pEK0QsU0FBU21QLE9BQU8sQ0FBQ0wsTUFBTSxDQUFDdkY7UUFDMUI7SUFDRjtJQUNBdkosU0FBUzBPLFdBQVcsQ0FBQ2xRLE9BQU8sQ0FBQyxDQUFDbVEsVUFBVUM7UUFDdENILDhCQUE4QnpPLFNBQVMwTyxXQUFXLEVBQUV6UyxRQUFRMFMsVUFBVUM7SUFDeEU7QUFDRjtBQUNBLFNBQVNRLGFBQWFKLEtBQUs7SUFDekIscUJBQXFCLEdBQ3JCLFNBQVNLLGtCQUFrQnBCLEtBQUs7UUFDOUIsTUFBTSxFQUNKak8sUUFBUSxFQUNULEdBQUdnUCxNQUFNalAsUUFBUTtRQUNsQixNQUFNdVAsS0FBS3JCLE1BQU1zQixPQUFPLEdBQUd2UCxTQUFTd1AsWUFBWSxDQUFDLEVBQUU7UUFDbkQsTUFBTUMsS0FBS3hCLE1BQU15QixPQUFPLEdBQUcxUCxTQUFTd1AsWUFBWSxDQUFDLEVBQUU7UUFDbkQsT0FBTzdILEtBQUtnSSxLQUFLLENBQUNoSSxLQUFLaUksSUFBSSxDQUFDTixLQUFLQSxLQUFLRyxLQUFLQTtJQUM3QztJQUVBLHVHQUF1RyxHQUN2RyxTQUFTSSxvQkFBb0IzVSxPQUFPO1FBQ2xDLE9BQU9BLFFBQVErQyxNQUFNLENBQUN3SCxDQUFBQSxNQUFPO2dCQUFDO2dCQUFRO2dCQUFRO2dCQUFTO2dCQUFPO2FBQVEsQ0FBQ2xELElBQUksQ0FBQzFHLENBQUFBO2dCQUMxRSxJQUFJa007Z0JBQ0osT0FBTyxDQUFDQSxPQUFPdEMsSUFBSS9JLEtBQUssS0FBSyxPQUFPLEtBQUssSUFBSXFMLEtBQUtuRyxRQUFRLENBQUMsY0FBYy9GLEtBQUs7WUFDaEY7SUFDRjtJQUNBLFNBQVNpVSxVQUFVN0IsS0FBSyxFQUFFaFEsTUFBTTtRQUM5QixNQUFNMkksUUFBUW9JLE1BQU1qUCxRQUFRO1FBQzVCLE1BQU1nUSxhQUFhLElBQUlDO1FBQ3ZCLE1BQU1DLGdCQUFnQixFQUFFO1FBQ3hCLDJDQUEyQztRQUMzQyxNQUFNQyxnQkFBZ0JqUyxTQUFTQSxPQUFPMkksTUFBTTVHLFFBQVEsQ0FBQ0MsV0FBVyxJQUFJMkcsTUFBTTVHLFFBQVEsQ0FBQ0MsV0FBVztRQUM5RiwyQ0FBMkM7UUFDM0MsSUFBSyxJQUFJMkksSUFBSSxHQUFHQSxJQUFJc0gsY0FBY3ROLE1BQU0sRUFBRWdHLElBQUs7WUFDN0MsTUFBTWhDLFFBQVFrQixhQUFhb0ksYUFBYSxDQUFDdEgsRUFBRTtZQUMzQyxJQUFJaEMsT0FBTztnQkFDVEEsTUFBTXVKLFNBQVMsQ0FBQy9DLE1BQU0sR0FBR2xSO1lBQzNCO1FBQ0Y7UUFDQSxJQUFJLENBQUMwSyxNQUFNd0osWUFBWSxFQUFFO1lBQ3ZCLGtEQUFrRDtZQUNsRHhKLE1BQU15SixNQUFNLENBQUNDLE9BQU8sSUFBSSxPQUFPLEtBQUssSUFBSTFKLE1BQU15SixNQUFNLENBQUNDLE9BQU8sQ0FBQ3JDLE9BQU9ySDtRQUN0RTtRQUNBLFNBQVMySixjQUFjOUssR0FBRztZQUN4QixNQUFNbUIsUUFBUWtCLGFBQWFyQztZQUMzQixrRkFBa0Y7WUFDbEYsSUFBSSxDQUFDbUIsU0FBUyxDQUFDQSxNQUFNeUosTUFBTSxDQUFDRyxPQUFPLElBQUk1SixNQUFNdUosU0FBUyxDQUFDL0MsTUFBTSxLQUFLLE1BQU0sT0FBTyxFQUFFO1lBRWpGLGdGQUFnRjtZQUNoRixJQUFJeEcsTUFBTXVKLFNBQVMsQ0FBQy9DLE1BQU0sS0FBS2xSLFdBQVc7Z0JBQ3hDLElBQUl1VTtnQkFDSjdKLE1BQU15SixNQUFNLENBQUNDLE9BQU8sSUFBSSxPQUFPLEtBQUssSUFBSTFKLE1BQU15SixNQUFNLENBQUNDLE9BQU8sQ0FBQ3JDLE9BQU9ySCxPQUFPLENBQUM2SixzQkFBc0I3SixNQUFNd0osWUFBWSxLQUFLLE9BQU8sS0FBSyxJQUFJSyxvQkFBb0IxUSxRQUFRO2dCQUNySyx1RUFBdUU7Z0JBQ3ZFLElBQUk2RyxNQUFNdUosU0FBUyxDQUFDL0MsTUFBTSxLQUFLbFIsV0FBVzBLLE1BQU11SixTQUFTLENBQUMvQyxNQUFNLEdBQUc7WUFDckU7WUFFQSw2QkFBNkI7WUFDN0IsT0FBT3hHLE1BQU11SixTQUFTLENBQUMvQyxNQUFNLEdBQUd4RyxNQUFNdUosU0FBUyxDQUFDTyxlQUFlLENBQUNqTCxLQUFLLFFBQVEsRUFBRTtRQUNqRjtRQUVBLGlCQUFpQjtRQUNqQixJQUFJa0wsT0FBT1QsYUFDWCxvQkFBb0I7U0FDbkJVLE9BQU8sQ0FBQ0wsY0FDVCxzQ0FBc0M7U0FDckNNLElBQUksQ0FBQyxDQUFDN0ksR0FBR087WUFDUixNQUFNdUksU0FBU2hKLGFBQWFFLEVBQUUvTCxNQUFNO1lBQ3BDLE1BQU04VSxTQUFTakosYUFBYVMsRUFBRXRNLE1BQU07WUFDcEMsSUFBSSxDQUFDNlUsVUFBVSxDQUFDQyxRQUFRLE9BQU8vSSxFQUFFZ0osUUFBUSxHQUFHekksRUFBRXlJLFFBQVE7WUFDdEQsT0FBT0QsT0FBT1YsTUFBTSxDQUFDWSxRQUFRLEdBQUdILE9BQU9ULE1BQU0sQ0FBQ1ksUUFBUSxJQUFJakosRUFBRWdKLFFBQVEsR0FBR3pJLEVBQUV5SSxRQUFRO1FBQ25GLEVBQ0Esd0JBQXdCO1NBQ3ZCL1MsTUFBTSxDQUFDaVQsQ0FBQUE7WUFDTixNQUFNQyxLQUFLbkQsT0FBT2tEO1lBQ2xCLElBQUluQixXQUFXcUIsR0FBRyxDQUFDRCxLQUFLLE9BQU87WUFDL0JwQixXQUFXMVMsR0FBRyxDQUFDOFQ7WUFDZixPQUFPO1FBQ1Q7UUFFQSxrREFBa0Q7UUFDbEQsOEZBQThGO1FBQzlGLElBQUl2SyxNQUFNeUosTUFBTSxDQUFDcFMsTUFBTSxFQUFFMFMsT0FBTy9KLE1BQU15SixNQUFNLENBQUNwUyxNQUFNLENBQUMwUyxNQUFNL0o7UUFFMUQsNERBQTREO1FBQzVELEtBQUssTUFBTXlLLE9BQU9WLEtBQU07WUFDdEIsSUFBSXpDLGNBQWNtRCxJQUFJcFYsTUFBTTtZQUM1QixrQkFBa0I7WUFDbEIsTUFBT2lTLFlBQWE7Z0JBQ2xCLElBQUlvRDtnQkFDSixJQUFJLENBQUNBLFFBQVFwRCxZQUFZeFIsS0FBSyxLQUFLLFFBQVE0VSxNQUFNelIsVUFBVSxFQUFFb1EsY0FBYzNTLElBQUksQ0FBQztvQkFDOUUsR0FBRytULEdBQUc7b0JBQ05uRDtnQkFDRjtnQkFDQUEsY0FBY0EsWUFBWTNRLE1BQU07WUFDbEM7UUFDRjtRQUVBLG9GQUFvRjtRQUNwRixJQUFJLGVBQWUwUSxTQUFTckgsTUFBTTVHLFFBQVEsQ0FBQzBPLFdBQVcsQ0FBQzBDLEdBQUcsQ0FBQ25ELE1BQU1XLFNBQVMsR0FBRztZQUMzRSxLQUFLLElBQUlDLGVBQWVqSSxNQUFNNUcsUUFBUSxDQUFDME8sV0FBVyxDQUFDakQsR0FBRyxDQUFDd0MsTUFBTVcsU0FBUyxFQUFFMkMsTUFBTSxHQUFJO2dCQUNoRixJQUFJLENBQUN4QixXQUFXcUIsR0FBRyxDQUFDcEQsT0FBT2EsWUFBWTJDLFlBQVksSUFBSXZCLGNBQWMzUyxJQUFJLENBQUN1UixZQUFZMkMsWUFBWTtZQUNwRztRQUNGO1FBQ0EsT0FBT3ZCO0lBQ1Q7SUFFQSwwREFBMEQsR0FDMUQsU0FBU3dCLGlCQUFpQnhCLGFBQWEsRUFBRWhDLEtBQUssRUFBRXlELEtBQUssRUFBRUMsUUFBUTtRQUM3RCxNQUFNN1IsWUFBWWtQLE1BQU1qUCxRQUFRO1FBRWhDLGdFQUFnRTtRQUNoRSxJQUFJa1EsY0FBY3JOLE1BQU0sRUFBRTtZQUN4QixNQUFNbEIsYUFBYTtnQkFDakJrUSxTQUFTO1lBQ1g7WUFDQSxLQUFLLE1BQU1QLE9BQU9wQixjQUFlO2dCQUMvQixNQUFNckosUUFBUWtCLGFBQWF1SixJQUFJcFYsTUFBTSxLQUFLNkQ7Z0JBQzFDLE1BQU0sRUFDSnFRLFNBQVMsRUFDVDBCLE9BQU8sRUFDUHpFLE1BQU0sRUFDTnBOLFFBQVEsRUFDVCxHQUFHNEc7Z0JBQ0osTUFBTWtMLG1CQUFtQixJQUFJL1gsMENBQWEsQ0FBQzhYLFFBQVFoVCxDQUFDLEVBQUVnVCxRQUFRRyxDQUFDLEVBQUUsR0FBR0MsU0FBUyxDQUFDN0U7Z0JBQzlFLE1BQU04RSxvQkFBb0JmLENBQUFBO29CQUN4QixJQUFJZ0IsdUJBQXVCQztvQkFDM0IsT0FBTyxDQUFDRCx3QkFBd0IsQ0FBQ0MseUJBQXlCcFMsU0FBUzBPLFdBQVcsQ0FBQ2pELEdBQUcsQ0FBQzBGLEdBQUUsS0FBTSxPQUFPLEtBQUssSUFBSWlCLHVCQUF1QmhCLEdBQUcsQ0FBQ0MsSUFBSW5ELFdBQVcsTUFBTSxPQUFPaUUsd0JBQXdCO2dCQUM1TDtnQkFDQSxNQUFNRSxvQkFBb0JsQixDQUFBQTtvQkFDeEIsTUFBTXRDLGNBQWM7d0JBQ2xCMkMsY0FBY0g7d0JBQ2QvVSxRQUFRMlIsTUFBTTNSLE1BQU07b0JBQ3RCO29CQUNBLElBQUkwRCxTQUFTME8sV0FBVyxDQUFDMEMsR0FBRyxDQUFDRCxLQUFLO3dCQUNoQyxrRUFBa0U7d0JBQ2xFLHFCQUFxQjt3QkFDckJuUixTQUFTME8sV0FBVyxDQUFDakQsR0FBRyxDQUFDMEYsSUFBSTVLLEdBQUcsQ0FBQzhLLElBQUluRCxXQUFXLEVBQUVXO29CQUNwRCxPQUFPO3dCQUNMLGdFQUFnRTt3QkFDaEUsK0RBQStEO3dCQUMvRCxpQkFBaUI7d0JBQ2pCN08sU0FBUzBPLFdBQVcsQ0FBQ25JLEdBQUcsQ0FBQzRLLElBQUksSUFBSS9KLElBQUk7NEJBQUM7Z0NBQUNpSyxJQUFJbkQsV0FBVztnQ0FBRVc7NkJBQVk7eUJBQUM7b0JBQ3ZFO29CQUNBWixNQUFNM1IsTUFBTSxDQUFDK1YsaUJBQWlCLENBQUNsQjtnQkFDakM7Z0JBQ0EsTUFBTXBDLHdCQUF3Qm9DLENBQUFBO29CQUM1QixNQUFNeEMsV0FBVzNPLFNBQVMwTyxXQUFXLENBQUNqRCxHQUFHLENBQUMwRjtvQkFDMUMsSUFBSXhDLFVBQVU7d0JBQ1pGLDhCQUE4QnpPLFNBQVMwTyxXQUFXLEVBQUUyQyxJQUFJbkQsV0FBVyxFQUFFUyxVQUFVd0M7b0JBQ2pGO2dCQUNGO2dCQUVBLHlCQUF5QjtnQkFDekIsSUFBSW1CLG9CQUFvQixDQUFDO2dCQUN6Qix1YUFBdWE7Z0JBQ3ZhLElBQUssSUFBSTVILFFBQVF1RCxNQUFPO29CQUN0QixJQUFJc0UsV0FBV3RFLEtBQUssQ0FBQ3ZELEtBQUs7b0JBQzFCLG1FQUFtRTtvQkFDbkUsbUNBQW1DO29CQUNuQyxJQUFJLE9BQU82SCxhQUFhLFlBQVlELGlCQUFpQixDQUFDNUgsS0FBSyxHQUFHNkg7Z0JBQ2hFO2dCQUNBLElBQUlDLGVBQWU7b0JBQ2pCLEdBQUduQixHQUFHO29CQUNOLEdBQUdpQixpQkFBaUI7b0JBQ3BCVDtvQkFDQTVCO29CQUNBMkIsU0FBU2xRLFdBQVdrUSxPQUFPO29CQUMzQkY7b0JBQ0FJO29CQUNBVyxLQUFLdEMsVUFBVXNDLEdBQUc7b0JBQ2xCckYsUUFBUUE7b0JBQ1IsaURBQWlEO29CQUNqRHNGO3dCQUNFLHlEQUF5RDt3QkFDekQsOEVBQThFO3dCQUM5RSxNQUFNQyxxQkFBcUIsZUFBZTFFLFNBQVNqTyxTQUFTME8sV0FBVyxDQUFDakQsR0FBRyxDQUFDd0MsTUFBTVcsU0FBUzt3QkFFM0YsdUNBQXVDO3dCQUN2QyxJQUNBLDBDQUEwQzt3QkFDMUMsQ0FBQytELHNCQUNELG9EQUFvRDt3QkFDcERBLG1CQUFtQnZCLEdBQUcsQ0FBQ0MsSUFBSW5ELFdBQVcsR0FBRzs0QkFDdkNzRSxhQUFhWixPQUFPLEdBQUdsUSxXQUFXa1EsT0FBTyxHQUFHOzRCQUM1Qyx5REFBeUQ7NEJBQ3pELG1GQUFtRjs0QkFDbkYsSUFBSTVSLFNBQVNtUCxPQUFPLENBQUM5QixJQUFJLElBQUk5USxNQUFNcVcsSUFBSSxDQUFDNVMsU0FBU21QLE9BQU8sQ0FBQ29DLE1BQU0sSUFBSXNCLElBQUksQ0FBQ2pLLENBQUFBLElBQUtBLEVBQUVzRixXQUFXLEtBQUttRCxJQUFJbkQsV0FBVyxHQUFHO2dDQUMvRyxnRkFBZ0Y7Z0NBQ2hGLE1BQU00RSxTQUFTN0MsY0FBY2xVLEtBQUssQ0FBQyxHQUFHa1UsY0FBYzdSLE9BQU8sQ0FBQ2lUO2dDQUM1RDBCLGNBQWM7dUNBQUlEO29DQUFRekI7aUNBQUk7NEJBQ2hDO3dCQUNGO29CQUNGO29CQUNBLGlFQUFpRTtvQkFDakUvVSxRQUFRO3dCQUNONFY7d0JBQ0FHO3dCQUNBdEQ7b0JBQ0Y7b0JBQ0FpRSxlQUFlO3dCQUNiZDt3QkFDQUc7d0JBQ0F0RDtvQkFDRjtvQkFDQWtFLGFBQWFoRjtnQkFDZjtnQkFFQSxtQkFBbUI7Z0JBQ25CMEQsU0FBU2E7Z0JBQ1QsdURBQXVEO2dCQUN2RCxJQUFJOVEsV0FBV2tRLE9BQU8sS0FBSyxNQUFNO1lBQ25DO1FBQ0Y7UUFDQSxPQUFPM0I7SUFDVDtJQUNBLFNBQVM4QyxjQUFjOUMsYUFBYTtRQUNsQyxNQUFNLEVBQ0pqUSxRQUFRLEVBQ1QsR0FBR2dQLE1BQU1qUCxRQUFRO1FBQ2xCLEtBQUssTUFBTW1ULGNBQWNsVCxTQUFTbVAsT0FBTyxDQUFDb0MsTUFBTSxHQUFJO1lBQ2xELHdGQUF3RjtZQUN4RiwyRUFBMkU7WUFDM0UsSUFBSSxDQUFDdEIsY0FBY3JOLE1BQU0sSUFBSSxDQUFDcU4sY0FBYzRDLElBQUksQ0FBQ3hCLENBQUFBLE1BQU9BLElBQUlwVixNQUFNLEtBQUtpWCxXQUFXalgsTUFBTSxJQUFJb1YsSUFBSWxULEtBQUssS0FBSytVLFdBQVcvVSxLQUFLLElBQUlrVCxJQUFJakQsVUFBVSxLQUFLOEUsV0FBVzlFLFVBQVUsR0FBRztnQkFDdkssTUFBTUYsY0FBY2dGLFdBQVdoRixXQUFXO2dCQUMxQyxNQUFNbFMsV0FBV2tTLFlBQVl4UixLQUFLO2dCQUNsQyxNQUFNa0YsV0FBVzVGLFlBQVksT0FBTyxLQUFLLElBQUlBLFNBQVM0RixRQUFRO2dCQUM5RDVCLFNBQVNtUCxPQUFPLENBQUNMLE1BQU0sQ0FBQ2QsT0FBT2tGO2dCQUMvQixJQUFJbFgsWUFBWSxRQUFRQSxTQUFTNkQsVUFBVSxFQUFFO29CQUMzQyxpREFBaUQ7b0JBQ2pELE1BQU1rSixPQUFPO3dCQUNYLEdBQUdtSyxVQUFVO3dCQUNiakQ7b0JBQ0Y7b0JBQ0FyTyxTQUFTdVIsWUFBWSxJQUFJLE9BQU8sS0FBSyxJQUFJdlIsU0FBU3VSLFlBQVksQ0FBQ3BLO29CQUMvRG5ILFNBQVN3UixjQUFjLElBQUksT0FBTyxLQUFLLElBQUl4UixTQUFTd1IsY0FBYyxDQUFDcks7Z0JBQ3JFO1lBQ0Y7UUFDRjtJQUNGO0lBQ0EsU0FBU3NLLGNBQWNwRixLQUFLLEVBQUUvUyxPQUFPO1FBQ25DLElBQUssSUFBSTBOLElBQUksR0FBR0EsSUFBSTFOLFFBQVEwSCxNQUFNLEVBQUVnRyxJQUFLO1lBQ3ZDLE1BQU01TSxXQUFXZCxPQUFPLENBQUMwTixFQUFFLENBQUNsTSxLQUFLO1lBQ2pDVixZQUFZLE9BQU8sS0FBSyxJQUFJQSxTQUFTNEYsUUFBUSxDQUFDMFIsZUFBZSxJQUFJLE9BQU8sS0FBSyxJQUFJdFgsU0FBUzRGLFFBQVEsQ0FBQzBSLGVBQWUsQ0FBQ3JGO1FBQ3JIO0lBQ0Y7SUFDQSxTQUFTc0YsY0FBYzFYLElBQUk7UUFDekIsd0JBQXdCO1FBQ3hCLE9BQVFBO1lBQ04sS0FBSztZQUNMLEtBQUs7Z0JBQ0gsT0FBTyxJQUFNa1gsY0FBYyxFQUFFO1lBQy9CLEtBQUs7Z0JBQ0gsT0FBTzlFLENBQUFBO29CQUNMLE1BQU0sRUFDSmpPLFFBQVEsRUFDVCxHQUFHZ1AsTUFBTWpQLFFBQVE7b0JBQ2xCLElBQUksZUFBZWtPLFNBQVNqTyxTQUFTME8sV0FBVyxDQUFDMEMsR0FBRyxDQUFDbkQsTUFBTVcsU0FBUyxHQUFHO3dCQUNyRSxxRkFBcUY7d0JBQ3JGLDJGQUEyRjt3QkFDM0YsMEZBQTBGO3dCQUMxRixxRkFBcUY7d0JBQ3JGNEUsc0JBQXNCOzRCQUNwQixrREFBa0Q7NEJBQ2xELElBQUl4VCxTQUFTME8sV0FBVyxDQUFDMEMsR0FBRyxDQUFDbkQsTUFBTVcsU0FBUyxHQUFHO2dDQUM3QzVPLFNBQVMwTyxXQUFXLENBQUNJLE1BQU0sQ0FBQ2IsTUFBTVcsU0FBUztnQ0FDM0NtRSxjQUFjLEVBQUU7NEJBQ2xCO3dCQUNGO29CQUNGO2dCQUNGO1FBQ0o7UUFFQSxrQ0FBa0M7UUFDbEMsT0FBTyxTQUFTVSxZQUFZeEYsS0FBSztZQUMvQixNQUFNLEVBQ0pxRixlQUFlLEVBQ2Z0VCxRQUFRLEVBQ1QsR0FBR2dQLE1BQU1qUCxRQUFRO1lBRWxCLG9CQUFvQjtZQUNwQkMsU0FBUzBULFNBQVMsQ0FBQ3JULE9BQU8sR0FBRzROO1lBRTdCLHVCQUF1QjtZQUN2QixNQUFNMEYsZ0JBQWdCOVgsU0FBUztZQUMvQixNQUFNK1gsZUFBZS9YLFNBQVMsYUFBYUEsU0FBUyxtQkFBbUJBLFNBQVM7WUFDaEYsTUFBTW9DLFNBQVMwVixnQkFBZ0I5RCxzQkFBc0IzVDtZQUNyRCxNQUFNeVUsT0FBT2IsVUFBVTdCLE9BQU9oUTtZQUM5QixNQUFNeVQsUUFBUWtDLGVBQWV2RSxrQkFBa0JwQixTQUFTO1lBRXhELDJDQUEyQztZQUMzQyxJQUFJcFMsU0FBUyxpQkFBaUI7Z0JBQzVCbUUsU0FBU3dQLFlBQVksR0FBRztvQkFBQ3ZCLE1BQU1zQixPQUFPO29CQUFFdEIsTUFBTXlCLE9BQU87aUJBQUM7Z0JBQ3REMVAsU0FBU2tQLFdBQVcsR0FBR3lCLEtBQUtrRCxHQUFHLENBQUN4QyxDQUFBQSxNQUFPQSxJQUFJbkQsV0FBVztZQUN4RDtZQUVBLG1FQUFtRTtZQUNuRSx3RkFBd0Y7WUFDeEYsSUFBSTBGLGdCQUFnQixDQUFDakQsS0FBSy9OLE1BQU0sRUFBRTtnQkFDaEMsSUFBSThPLFNBQVMsR0FBRztvQkFDZDJCLGNBQWNwRixPQUFPak8sU0FBU0MsV0FBVztvQkFDekMsSUFBSXFULGlCQUFpQkEsZ0JBQWdCckY7Z0JBQ3ZDO1lBQ0Y7WUFDQSx1QkFBdUI7WUFDdkIsSUFBSTBGLGVBQWVaLGNBQWNwQztZQUNqQyxTQUFTbUQsWUFBWS9LLElBQUk7Z0JBQ3ZCLE1BQU1tRixjQUFjbkYsS0FBS21GLFdBQVc7Z0JBQ3BDLE1BQU1sUyxXQUFXa1MsWUFBWXhSLEtBQUs7Z0JBQ2xDLE1BQU1rRixXQUFXNUYsWUFBWSxPQUFPLEtBQUssSUFBSUEsU0FBUzRGLFFBQVE7Z0JBRTlELDZCQUE2QjtnQkFDN0IsSUFBSSxDQUFFNUYsQ0FBQUEsWUFBWSxRQUFRQSxTQUFTNkQsVUFBVSxHQUFHO2dCQUVoRDs7Ozs7Ozs7Ozs7OztTQWFDLEdBRUQsSUFBSThULGVBQWU7b0JBQ2pCLGlCQUFpQjtvQkFDakIsSUFBSS9SLFNBQVNtUyxhQUFhLElBQUluUyxTQUFTb1MsY0FBYyxJQUFJcFMsU0FBU3VSLFlBQVksSUFBSXZSLFNBQVN3UixjQUFjLEVBQUU7d0JBQ3pHLHdEQUF3RDt3QkFDeEQsTUFBTWpDLEtBQUtuRCxPQUFPakY7d0JBQ2xCLE1BQU1rTCxjQUFjalUsU0FBU21QLE9BQU8sQ0FBQzFELEdBQUcsQ0FBQzBGO3dCQUN6QyxJQUFJLENBQUM4QyxhQUFhOzRCQUNoQix3RUFBd0U7NEJBQ3hFalUsU0FBU21QLE9BQU8sQ0FBQzVJLEdBQUcsQ0FBQzRLLElBQUlwSTs0QkFDekJuSCxTQUFTbVMsYUFBYSxJQUFJLE9BQU8sS0FBSyxJQUFJblMsU0FBU21TLGFBQWEsQ0FBQ2hMOzRCQUNqRW5ILFNBQVNvUyxjQUFjLElBQUksT0FBTyxLQUFLLElBQUlwUyxTQUFTb1MsY0FBYyxDQUFDakw7d0JBQ3JFLE9BQU8sSUFBSWtMLFlBQVlyQyxPQUFPLEVBQUU7NEJBQzlCLDhGQUE4Rjs0QkFDOUY3SSxLQUFLMkosZUFBZTt3QkFDdEI7b0JBQ0Y7b0JBQ0Esa0JBQWtCO29CQUNsQjlRLFNBQVNzUyxhQUFhLElBQUksT0FBTyxLQUFLLElBQUl0UyxTQUFTc1MsYUFBYSxDQUFDbkw7Z0JBQ25FLE9BQU87b0JBQ0wsdUJBQXVCO29CQUN2QixNQUFNb0wsVUFBVXZTLFFBQVEsQ0FBQy9GLEtBQUs7b0JBQzlCLElBQUlzWSxTQUFTO3dCQUNYLDJGQUEyRjt3QkFDM0Ysb0NBQW9DO3dCQUNwQyxJQUFJLENBQUNQLGdCQUFnQjVULFNBQVNrUCxXQUFXLENBQUMxRixRQUFRLENBQUMwRSxjQUFjOzRCQUMvRCxtQ0FBbUM7NEJBQ25DbUYsY0FBY3BGLE9BQU9qTyxTQUFTQyxXQUFXLENBQUNoQyxNQUFNLENBQUNoQyxDQUFBQSxTQUFVLENBQUMrRCxTQUFTa1AsV0FBVyxDQUFDMUYsUUFBUSxDQUFDdk47NEJBQzFGLHVCQUF1Qjs0QkFDdkJrWSxRQUFRcEw7d0JBQ1Y7b0JBQ0YsT0FBTzt3QkFDTCw2R0FBNkc7d0JBQzdHLElBQUk2SyxnQkFBZ0I1VCxTQUFTa1AsV0FBVyxDQUFDMUYsUUFBUSxDQUFDMEUsY0FBYzs0QkFDOURtRixjQUFjcEYsT0FBT2pPLFNBQVNDLFdBQVcsQ0FBQ2hDLE1BQU0sQ0FBQ2hDLENBQUFBLFNBQVUsQ0FBQytELFNBQVNrUCxXQUFXLENBQUMxRixRQUFRLENBQUN2Tjt3QkFDNUY7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUNBd1YsaUJBQWlCZCxNQUFNMUMsT0FBT3lELE9BQU9vQztRQUN2QztJQUNGO0lBQ0EsT0FBTztRQUNMUDtJQUNGO0FBQ0Y7QUFFQSxtREFBbUQ7QUFDbkQsTUFBTWEsY0FBYztJQUFDO0lBQU87SUFBTztJQUFXO0lBQWdCO0lBQVU7SUFBVTtJQUFjO0lBQVc7SUFBUTtDQUFXO0FBQzlILE1BQU1DLGFBQWE5TyxDQUFBQSxNQUFPLENBQUMsQ0FBRUEsQ0FBQUEsT0FBTyxRQUFRQSxJQUFJeUIsTUFBTTtBQUN0RCxNQUFNc04sVUFBVSxXQUFXLEdBQUV0YSxnREFBbUIsQ0FBQztBQUNqRCxNQUFNd2EsY0FBYyxDQUFDdkgsWUFBWXdIO0lBQy9CLE1BQU0zVSxZQUFZekYsbURBQU1BLENBQUMsQ0FBQ2tNLEtBQUtrRjtRQUM3QixNQUFNaUosV0FBVyxJQUFJM2EsMENBQWE7UUFDbEMsTUFBTTRhLGdCQUFnQixJQUFJNWEsMENBQWE7UUFDdkMsTUFBTTZhLGFBQWEsSUFBSTdhLDBDQUFhO1FBQ3BDLFNBQVM4YSxtQkFBbUJ6SCxTQUFTM0IsTUFBTTJCLE1BQU0sRUFBRTlRLFNBQVNxWSxhQUFhLEVBQUV0SCxPQUFPNUIsTUFBTTRCLElBQUk7WUFDMUYsTUFBTSxFQUNKRyxLQUFLLEVBQ0xHLE1BQU0sRUFDTkQsR0FBRyxFQUNISCxJQUFJLEVBQ0wsR0FBR0Y7WUFDSixNQUFNUSxTQUFTTCxRQUFRRztZQUN2QixJQUFJclIsa0JBQWtCdkMsMENBQWEsRUFBRTZhLFdBQVdsSixJQUFJLENBQUNwUDtpQkFBYXNZLFdBQVdyTyxHQUFHLElBQUlqSztZQUNwRixNQUFNMFUsV0FBVzVELE9BQU8wSCxnQkFBZ0IsQ0FBQ0osVUFBVUssVUFBVSxDQUFDSDtZQUM5RCxJQUFJdFAscUJBQXFCOEgsU0FBUztnQkFDaEMsT0FBTztvQkFDTEksT0FBT0EsUUFBUUosT0FBTzRILElBQUk7b0JBQzFCckgsUUFBUUEsU0FBU1AsT0FBTzRILElBQUk7b0JBQzVCdEg7b0JBQ0FIO29CQUNBMEgsUUFBUTtvQkFDUmpFO29CQUNBbkQ7Z0JBQ0Y7WUFDRixPQUFPO2dCQUNMLE1BQU1xSCxNQUFNOUgsT0FBTzhILEdBQUcsR0FBR3ZOLEtBQUt3TixFQUFFLEdBQUcsS0FBSyxrQ0FBa0M7Z0JBQzFFLE1BQU1DLElBQUksSUFBSXpOLEtBQUswTixHQUFHLENBQUNILE1BQU0sS0FBS2xFLFVBQVUsaUJBQWlCO2dCQUM3RCxNQUFNc0UsSUFBSUYsSUFBSzVILENBQUFBLFFBQVFHLE1BQUs7Z0JBQzVCLE9BQU87b0JBQ0xILE9BQU84SDtvQkFDUDNILFFBQVF5SDtvQkFDUjFIO29CQUNBSDtvQkFDQTBILFFBQVF6SCxRQUFROEg7b0JBQ2hCdEU7b0JBQ0FuRDtnQkFDRjtZQUNGO1FBQ0Y7UUFDQSxJQUFJMEgscUJBQXFCclo7UUFDekIsTUFBTXNaLHdCQUF3Qm5WLENBQUFBLFVBQVdrRyxJQUFJSyxDQUFBQSxRQUFVO29CQUNyRHJDLGFBQWE7d0JBQ1gsR0FBR3FDLE1BQU1yQyxXQUFXO3dCQUNwQmxFO29CQUNGO2dCQUNGO1FBQ0EsTUFBTXdSLFVBQVUsSUFBSTlYLDBDQUFhO1FBQ2pDLE1BQU0rRixZQUFZO1lBQ2hCeUc7WUFDQWtGO1lBQ0EsMENBQTBDO1lBQzFDZSxJQUFJO1lBQ0pZLFFBQVE7WUFDUitDLFdBQVc7WUFDWEUsUUFBUTtnQkFDTlksVUFBVTtnQkFDVlQsU0FBUztnQkFDVGtGLFdBQVc7WUFDYjtZQUNBQyxJQUFJO1lBQ0p6VSxPQUFPO1lBQ1ArTCxZQUFZLENBQUNELFNBQVMsQ0FBQyxHQUFLQyxXQUFXeEIsT0FBT3VCO1lBQzlDeUgsU0FBUyxDQUFDbUIsV0FBV0MsbUJBQXFCcEIsUUFBUW1CLFdBQVdDLGtCQUFrQnBLO1lBQy9FcUssUUFBUTtZQUNSN0osUUFBUTtZQUNSOEosTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLE9BQU8sSUFBSWxjLHdDQUFXO1lBQ3RCOFg7WUFDQXNFLE9BQU90RTtZQUNQdUUsV0FBVztZQUNYOUMsaUJBQWlCcFg7WUFDakJxSSxhQUFhO2dCQUNYbEUsU0FBUztnQkFDVHVILEtBQUs7Z0JBQ0xDLEtBQUs7Z0JBQ0x3TyxVQUFVO2dCQUNWQyxTQUFTO29CQUNQLE1BQU0xUCxRQUFRNkU7b0JBQ2QsZ0JBQWdCO29CQUNoQixJQUFJOEosb0JBQW9CelEsYUFBYXlRO29CQUNyQyw4QkFBOEI7b0JBQzlCLElBQUkzTyxNQUFNckMsV0FBVyxDQUFDbEUsT0FBTyxLQUFLdUcsTUFBTXJDLFdBQVcsQ0FBQ3FELEdBQUcsRUFBRTROLHNCQUFzQjVPLE1BQU1yQyxXQUFXLENBQUNxRCxHQUFHO29CQUNwRyx3RkFBd0Y7b0JBQ3hGMk4scUJBQXFCM1EsV0FBVyxJQUFNNFEsc0JBQXNCL0osTUFBTWxILFdBQVcsQ0FBQ3NELEdBQUcsR0FBR2pCLE1BQU1yQyxXQUFXLENBQUM4UixRQUFRO2dCQUNoSDtZQUNGO1lBQ0FoSixNQUFNO2dCQUNKRyxPQUFPO2dCQUNQRyxRQUFRO2dCQUNSRCxLQUFLO2dCQUNMSCxNQUFNO2dCQUNOZ0osYUFBYTtZQUNmO1lBQ0FDLFVBQVU7Z0JBQ1JDLFlBQVk7Z0JBQ1pqUCxLQUFLO2dCQUNMZ0csT0FBTztnQkFDUEcsUUFBUTtnQkFDUkQsS0FBSztnQkFDTEgsTUFBTTtnQkFDTk0sUUFBUTtnQkFDUm1ELFVBQVU7Z0JBQ1ZpRSxRQUFRO2dCQUNSSjtZQUNGO1lBQ0E2QixXQUFXckcsQ0FBQUEsU0FBVTlKLElBQUlLLENBQUFBLFFBQVU7d0JBQ2pDLEdBQUdBLEtBQUs7d0JBQ1J5SixRQUFROzRCQUNOLEdBQUd6SixNQUFNeUosTUFBTTs0QkFDZixHQUFHQSxNQUFNO3dCQUNYO29CQUNGO1lBQ0FzRyxTQUFTLENBQUNuSixPQUFPRyxRQUFRNEksYUFBYTdJLEtBQUtIO2dCQUN6QyxNQUFNSCxTQUFTM0IsTUFBTTJCLE1BQU07Z0JBQzNCLE1BQU1DLE9BQU87b0JBQ1hHO29CQUNBRztvQkFDQUQsS0FBS0EsT0FBTztvQkFDWkgsTUFBTUEsUUFBUTtvQkFDZGdKO2dCQUNGO2dCQUNBaFEsSUFBSUssQ0FBQUEsUUFBVTt3QkFDWnlHO3dCQUNBbUosVUFBVTs0QkFDUixHQUFHNVAsTUFBTTRQLFFBQVE7NEJBQ2pCLEdBQUczQixtQkFBbUJ6SCxRQUFRdUgsZUFBZXRILEtBQUs7d0JBQ3BEO29CQUNGO1lBQ0Y7WUFDQXVKLFFBQVFwUCxDQUFBQSxNQUFPakIsSUFBSUssQ0FBQUE7b0JBQ2pCLE1BQU1pUSxXQUFXdFAsYUFBYUM7b0JBQzlCLE9BQU87d0JBQ0xnUCxVQUFVOzRCQUNSLEdBQUc1UCxNQUFNNFAsUUFBUTs0QkFDakJoUCxLQUFLcVA7NEJBQ0xKLFlBQVk3UCxNQUFNNFAsUUFBUSxDQUFDQyxVQUFVLElBQUlJO3dCQUMzQztvQkFDRjtnQkFDRjtZQUNBQyxjQUFjLENBQUNWLFlBQVksUUFBUTtnQkFDakMsTUFBTUgsUUFBUXhLLE1BQU13SyxLQUFLO2dCQUV6QixpRkFBaUY7Z0JBQ2pGQSxNQUFNYyxJQUFJO2dCQUNWZCxNQUFNZSxXQUFXLEdBQUc7Z0JBQ3BCLElBQUlaLGNBQWMsU0FBUztvQkFDekJILE1BQU1nQixLQUFLO29CQUNYaEIsTUFBTWUsV0FBVyxHQUFHO2dCQUN0QjtnQkFDQXpRLElBQUksSUFBTzt3QkFDVDZQO29CQUNGO1lBQ0Y7WUFDQWhHLGNBQWNsVTtZQUNkOEQsVUFBVTtnQkFDUmtYLFFBQVE7Z0JBQ1JqRyxVQUFVO2dCQUNWakUsUUFBUTtnQkFDUjBHLFdBQVcsV0FBVyxHQUFFMVosNENBQWU7Z0JBQ3ZDaUcsYUFBYSxFQUFFO2dCQUNma1AsU0FBUyxJQUFJL0g7Z0JBQ2JnUSxhQUFhLEVBQUU7Z0JBQ2Y1SCxjQUFjO29CQUFDO29CQUFHO2lCQUFFO2dCQUNwQk4sYUFBYSxFQUFFO2dCQUNmUixhQUFhLElBQUl0SDtnQkFDakJpUSxXQUFXLENBQUNqWCxLQUFLNlEsVUFBVWpDO29CQUN6QixNQUFNaFAsV0FBV3lMLE1BQU16TCxRQUFRO29CQUMvQixtRkFBbUY7b0JBQ25GLGlGQUFpRjtvQkFDakYsNkVBQTZFO29CQUM3RSx1REFBdUQ7b0JBQ3ZEQSxTQUFTaVIsUUFBUSxHQUFHalIsU0FBU2lSLFFBQVEsR0FBSUEsQ0FBQUEsV0FBVyxJQUFJLElBQUk7b0JBQzVEalIsU0FBU29YLFdBQVcsQ0FBQzlaLElBQUksQ0FBQzt3QkFDeEI4Qzt3QkFDQTZRO3dCQUNBakM7b0JBQ0Y7b0JBQ0EsdUVBQXVFO29CQUN2RSw2REFBNkQ7b0JBQzdEaFAsU0FBU29YLFdBQVcsR0FBR3BYLFNBQVNvWCxXQUFXLENBQUN2RyxJQUFJLENBQUMsQ0FBQzdJLEdBQUdPLElBQU1QLEVBQUVpSixRQUFRLEdBQUcxSSxFQUFFMEksUUFBUTtvQkFDbEYsT0FBTzt3QkFDTCxNQUFNalIsV0FBV3lMLE1BQU16TCxRQUFRO3dCQUMvQixJQUFJQSxZQUFZLFFBQVFBLFNBQVNvWCxXQUFXLEVBQUU7NEJBQzVDLDJEQUEyRDs0QkFDM0RwWCxTQUFTaVIsUUFBUSxHQUFHalIsU0FBU2lSLFFBQVEsR0FBSUEsQ0FBQUEsV0FBVyxJQUFJLElBQUk7NEJBQzVELDhCQUE4Qjs0QkFDOUJqUixTQUFTb1gsV0FBVyxHQUFHcFgsU0FBU29YLFdBQVcsQ0FBQ25aLE1BQU0sQ0FBQ3FaLENBQUFBLElBQUtBLEVBQUVsWCxHQUFHLEtBQUtBO3dCQUNwRTtvQkFDRjtnQkFDRjtZQUNGO1FBQ0Y7UUFDQSxPQUFPTjtJQUNUO0lBQ0EsTUFBTThHLFFBQVE5RyxVQUFVQyxRQUFRO0lBQ2hDLElBQUl3WCxVQUFVM1EsTUFBTXlHLElBQUk7SUFDeEIsSUFBSW1LLFNBQVM1USxNQUFNNFAsUUFBUSxDQUFDaFAsR0FBRztJQUMvQixJQUFJaVEsWUFBWTdRLE1BQU13RyxNQUFNO0lBQzVCdE4sVUFBVXVYLFNBQVMsQ0FBQztRQUNsQixNQUFNLEVBQ0pqSyxNQUFNLEVBQ05DLElBQUksRUFDSm1KLFFBQVEsRUFDUmhLLEVBQUUsRUFDRmpHLEdBQUcsRUFDSixHQUFHekcsVUFBVUMsUUFBUTtRQUV0QiwrREFBK0Q7UUFDL0QsSUFBSXNOLFNBQVNrSyxXQUFXZixTQUFTaFAsR0FBRyxLQUFLZ1EsUUFBUTtZQUMvQyxJQUFJRTtZQUNKSCxVQUFVbEs7WUFDVm1LLFNBQVNoQixTQUFTaFAsR0FBRztZQUNyQiwyQkFBMkI7WUFDM0IyRixhQUFhQyxRQUFRQztZQUNyQmIsR0FBR21MLGFBQWEsQ0FBQ25CLFNBQVNoUCxHQUFHO1lBQzdCLE1BQU0rTyxjQUFjLENBQUNtQixvQkFBb0JySyxLQUFLa0osV0FBVyxLQUFLLE9BQU9tQixvQkFBb0IsT0FBT0Usc0JBQXNCLGVBQWVwTCxHQUFHcUwsVUFBVSxZQUFZRDtZQUM5SnBMLEdBQUdtSyxPQUFPLENBQUN0SixLQUFLRyxLQUFLLEVBQUVILEtBQUtNLE1BQU0sRUFBRTRJO1FBQ3RDO1FBRUEsMENBQTBDO1FBQzFDLElBQUluSixXQUFXcUssV0FBVztZQUN4QkEsWUFBWXJLO1lBQ1osa0JBQWtCO1lBQ2xCN0csSUFBSUssQ0FBQUEsUUFBVTtvQkFDWjRQLFVBQVU7d0JBQ1IsR0FBRzVQLE1BQU00UCxRQUFRO3dCQUNqQixHQUFHNVAsTUFBTTRQLFFBQVEsQ0FBQzNCLGtCQUFrQixDQUFDekgsT0FBTztvQkFDOUM7Z0JBQ0Y7UUFDRjtJQUNGO0lBRUEsMkJBQTJCO0lBQzNCdE4sVUFBVXVYLFNBQVMsQ0FBQ3pRLENBQUFBLFFBQVNxRyxXQUFXckc7SUFFeEMsb0JBQW9CO0lBQ3BCLE9BQU85RztBQUNUO0FBRUEsU0FBU2dZLFdBQVduRyxRQUFRLEVBQUVvRyxJQUFJO0lBQ2hDLE1BQU1DLE1BQU07UUFDVnJHO0lBQ0Y7SUFDQW9HLEtBQUsxYSxHQUFHLENBQUMyYTtJQUNULE9BQU8sSUFBTSxLQUFLRCxLQUFLakosTUFBTSxDQUFDa0o7QUFDaEM7QUFDQSxJQUFJcFA7QUFDSixJQUFJcVAsZ0JBQWdCLElBQUlqSTtBQUN4QixJQUFJa0kscUJBQXFCLElBQUlsSTtBQUM3QixJQUFJbUksb0JBQW9CLElBQUluSTtBQUU1Qjs7O0NBR0MsR0FDRCxNQUFNb0ksWUFBWXpHLENBQUFBLFdBQVltRyxXQUFXbkcsVUFBVXNHO0FBRW5EOzs7Q0FHQyxHQUNELE1BQU1JLGlCQUFpQjFHLENBQUFBLFdBQVltRyxXQUFXbkcsVUFBVXVHO0FBRXhEOzs7Q0FHQyxHQUNELE1BQU1JLFVBQVUzRyxDQUFBQSxXQUFZbUcsV0FBV25HLFVBQVV3RztBQUNqRCxTQUFTSSxJQUFJQyxPQUFPLEVBQUU1QyxTQUFTO0lBQzdCLElBQUksQ0FBQzRDLFFBQVFuTCxJQUFJLEVBQUU7SUFDbkIsS0FBSyxNQUFNLEVBQ1RzRSxRQUFRLEVBQ1QsSUFBSTZHLFFBQVFqSCxNQUFNLEdBQUk7UUFDckJJLFNBQVNpRTtJQUNYO0FBQ0Y7QUFDQSxTQUFTNkMsbUJBQW1CamQsSUFBSSxFQUFFb2EsU0FBUztJQUN6QyxPQUFRcGE7UUFDTixLQUFLO1lBQ0gsT0FBTytjLElBQUlOLGVBQWVyQztRQUM1QixLQUFLO1lBQ0gsT0FBTzJDLElBQUlMLG9CQUFvQnRDO1FBQ2pDLEtBQUs7WUFDSCxPQUFPMkMsSUFBSUosbUJBQW1CdkM7SUFDbEM7QUFDRjtBQUNBLElBQUl3QjtBQUNKLElBQUlzQjtBQUNKLFNBQVNDLFNBQVMvQyxTQUFTLEVBQUVoUCxLQUFLLEVBQUVnUyxLQUFLO0lBQ3ZDLG9CQUFvQjtJQUNwQixJQUFJbEgsUUFBUTlLLE1BQU1xUCxLQUFLLENBQUM0QyxRQUFRO0lBQ2hDLGtGQUFrRjtJQUNsRixJQUFJalMsTUFBTXdQLFNBQVMsS0FBSyxXQUFXLE9BQU9SLGNBQWMsVUFBVTtRQUNoRWxFLFFBQVFrRSxZQUFZaFAsTUFBTXFQLEtBQUssQ0FBQ2UsV0FBVztRQUMzQ3BRLE1BQU1xUCxLQUFLLENBQUM2QyxPQUFPLEdBQUdsUyxNQUFNcVAsS0FBSyxDQUFDZSxXQUFXO1FBQzdDcFEsTUFBTXFQLEtBQUssQ0FBQ2UsV0FBVyxHQUFHcEI7SUFDNUI7SUFDQSw4QkFBOEI7SUFDOUJ3QixjQUFjeFEsTUFBTTVHLFFBQVEsQ0FBQ29YLFdBQVc7SUFDeEMsSUFBS3hPLElBQUksR0FBR0EsSUFBSXdPLFlBQVl4VSxNQUFNLEVBQUVnRyxJQUFLO1FBQ3ZDOFAsZUFBZXRCLFdBQVcsQ0FBQ3hPLEVBQUU7UUFDN0I4UCxhQUFhdFksR0FBRyxDQUFDQyxPQUFPLENBQUNxWSxhQUFhMUosS0FBSyxDQUFDalAsUUFBUSxJQUFJMlIsT0FBT2tIO0lBQ2pFO0lBQ0EsaUJBQWlCO0lBQ2pCLElBQUksQ0FBQ2hTLE1BQU01RyxRQUFRLENBQUNpUixRQUFRLElBQUlySyxNQUFNNEYsRUFBRSxDQUFDeEYsTUFBTSxFQUFFSixNQUFNNEYsRUFBRSxDQUFDeEYsTUFBTSxDQUFDSixNQUFNMUYsS0FBSyxFQUFFMEYsTUFBTXdHLE1BQU07SUFDMUYsdUJBQXVCO0lBQ3ZCeEcsTUFBTTVHLFFBQVEsQ0FBQ2dOLE1BQU0sR0FBR3JGLEtBQUtFLEdBQUcsQ0FBQyxHQUFHakIsTUFBTTVHLFFBQVEsQ0FBQ2dOLE1BQU0sR0FBRztJQUM1RCxPQUFPcEcsTUFBTXdQLFNBQVMsS0FBSyxXQUFXLElBQUl4UCxNQUFNNUcsUUFBUSxDQUFDZ04sTUFBTTtBQUNqRTtBQUNBLFNBQVMrTCxXQUFXQyxLQUFLO0lBQ3ZCLElBQUlDLFVBQVU7SUFDZCxJQUFJQztJQUNKLElBQUlOO0lBQ0osSUFBSWhTO0lBQ0osU0FBU3VTLEtBQUt2RCxTQUFTO1FBQ3JCZ0QsUUFBUXBGLHNCQUFzQjJGO1FBQzlCRixVQUFVO1FBQ1ZDLFNBQVM7UUFFVCxjQUFjO1FBQ2RULG1CQUFtQixVQUFVN0M7UUFFN0IsbUJBQW1CO1FBQ25CLEtBQUssTUFBTWhhLFFBQVFvZCxNQUFNekgsTUFBTSxHQUFJO1lBQ2pDLElBQUk2SDtZQUNKeFMsUUFBUWhMLEtBQUtvVCxLQUFLLENBQUNqUCxRQUFRO1lBQzNCLDREQUE0RDtZQUM1RCxJQUFJNkcsTUFBTTVHLFFBQVEsQ0FBQ2tYLE1BQU0sSUFBS3RRLENBQUFBLE1BQU13UCxTQUFTLEtBQUssWUFBWXhQLE1BQU01RyxRQUFRLENBQUNnTixNQUFNLEdBQUcsTUFBTSxDQUFFLEVBQUNvTSxlQUFleFMsTUFBTTRGLEVBQUUsQ0FBQ21KLEVBQUUsS0FBSyxRQUFReUQsYUFBYUMsWUFBWSxHQUFHO2dCQUNoS0gsVUFBVVAsU0FBUy9DLFdBQVdoUDtZQUNoQztRQUNGO1FBRUEsb0JBQW9CO1FBQ3BCNlIsbUJBQW1CLFNBQVM3QztRQUU1QiwwQ0FBMEM7UUFDMUMsSUFBSXNELFdBQVcsR0FBRztZQUNoQiwwREFBMEQ7WUFDMURULG1CQUFtQixRQUFRN0M7WUFFM0Isd0JBQXdCO1lBQ3hCcUQsVUFBVTtZQUNWLE9BQU9LLHFCQUFxQlY7UUFDOUI7SUFDRjtJQUNBLFNBQVMzTCxXQUFXckcsS0FBSyxFQUFFb0csU0FBUyxDQUFDO1FBQ25DLElBQUl1TTtRQUNKLElBQUksQ0FBQzNTLE9BQU8sT0FBT29TLE1BQU14YSxPQUFPLENBQUM1QyxDQUFBQSxPQUFRcVIsV0FBV3JSLEtBQUtvVCxLQUFLLENBQUNqUCxRQUFRLEtBQUtpTjtRQUM1RSxJQUFJLENBQUN1TSxnQkFBZ0IzUyxNQUFNNEYsRUFBRSxDQUFDbUosRUFBRSxLQUFLLFFBQVE0RCxjQUFjRixZQUFZLElBQUksQ0FBQ3pTLE1BQU01RyxRQUFRLENBQUNrWCxNQUFNLElBQUl0USxNQUFNd1AsU0FBUyxLQUFLLFNBQVM7UUFDbEksNENBQTRDO1FBQzVDeFAsTUFBTTVHLFFBQVEsQ0FBQ2dOLE1BQU0sR0FBR3JGLEtBQUtDLEdBQUcsQ0FBQyxJQUFJaEIsTUFBTTVHLFFBQVEsQ0FBQ2dOLE1BQU0sR0FBR0E7UUFDN0QsNENBQTRDO1FBQzVDLElBQUksQ0FBQ2lNLFNBQVM7WUFDWkEsVUFBVTtZQUNWekYsc0JBQXNCMkY7UUFDeEI7SUFDRjtJQUNBLFNBQVMxRSxRQUFRbUIsU0FBUyxFQUFFQyxtQkFBbUIsSUFBSSxFQUFFalAsS0FBSyxFQUFFZ1MsS0FBSztRQUMvRCxJQUFJL0Msa0JBQWtCNEMsbUJBQW1CLFVBQVU3QztRQUNuRCxJQUFJLENBQUNoUCxPQUFPLEtBQUssTUFBTWhMLFFBQVFvZCxNQUFNekgsTUFBTSxHQUFJb0gsU0FBUy9DLFdBQVdoYSxLQUFLb1QsS0FBSyxDQUFDalAsUUFBUTthQUFTNFksU0FBUy9DLFdBQVdoUCxPQUFPZ1M7UUFDMUgsSUFBSS9DLGtCQUFrQjRDLG1CQUFtQixTQUFTN0M7SUFDcEQ7SUFDQSxPQUFPO1FBQ0x1RDtRQUNBOzs7S0FHQyxHQUNEbE07UUFDQTs7O0tBR0MsR0FDRHdIO0lBQ0Y7QUFDRjtBQUVBOzs7OztDQUtDLEdBQ0QsU0FBUytFLGtCQUFrQnBaLEdBQUc7SUFDNUIsTUFBTXBFLFdBQVdoQyx5Q0FBWSxDQUFDO0lBQzlCMkwsMEJBQTBCLElBQU0sS0FBTTNKLENBQUFBLFNBQVNxRSxPQUFPLEdBQUdELElBQUlDLE9BQU8sQ0FBQzNELEtBQUssR0FBRztRQUFDMEQ7S0FBSTtJQUNsRixPQUFPcEU7QUFDVDtBQUNBLFNBQVN5ZDtJQUNQLE1BQU16SyxRQUFRaFYsNkNBQWdCLENBQUNzYTtJQUMvQixJQUFJLENBQUN0RixPQUFPLE1BQU0sSUFBSTdTLE1BQU07SUFDNUIsT0FBTzZTO0FBQ1Q7QUFFQTs7O0NBR0MsR0FDRCxTQUFTMkssU0FBU0MsV0FBV2hULENBQUFBLFFBQVNBLEtBQUssRUFBRWlULFVBQVU7SUFDckQsT0FBT0osV0FBV0csVUFBVUM7QUFDOUI7QUFFQTs7OztDQUlDLEdBQ0QsU0FBU0MsU0FBU25JLFFBQVEsRUFBRW9JLGlCQUFpQixDQUFDO0lBQzVDLE1BQU0vSyxRQUFReUs7SUFDZCxNQUFNcEMsWUFBWXJJLE1BQU1qUCxRQUFRLEdBQUdDLFFBQVEsQ0FBQ3FYLFNBQVM7SUFDckQsY0FBYztJQUNkLE1BQU1qWCxNQUFNK0YsbUJBQW1Cd0w7SUFDL0IsNkNBQTZDO0lBQzdDaE0sMEJBQTBCLElBQU0wUixVQUFValgsS0FBSzJaLGdCQUFnQi9LLFFBQVE7UUFBQytLO1FBQWdCMUM7UUFBV3JJO0tBQU07SUFDekcsT0FBTztBQUNUO0FBRUE7OztDQUdDLEdBQ0QsU0FBU2dMLFNBQVMvZCxNQUFNO0lBQ3RCLE9BQU9qQywwQ0FBYSxDQUFDLElBQU04TyxXQUFXN00sU0FBUztRQUFDQTtLQUFPO0FBQ3pEO0FBQ0EsTUFBTWllLGtCQUFrQixJQUFJQztBQUM1QixTQUFTQyxVQUFVQyxVQUFVLEVBQUVDLFVBQVU7SUFDdkMsT0FBTyxTQUFVQyxLQUFLLEVBQUUsR0FBR0MsS0FBSztRQUM5QiwwQ0FBMEM7UUFDMUMsSUFBSUMsU0FBU1AsZ0JBQWdCek8sR0FBRyxDQUFDOE87UUFDakMsSUFBSSxDQUFDRSxRQUFRO1lBQ1hBLFNBQVMsSUFBSUY7WUFDYkwsZ0JBQWdCM1QsR0FBRyxDQUFDZ1UsT0FBT0U7UUFDN0I7UUFDQSxJQUFJSixZQUFZQSxXQUFXSTtRQUMzQixvQ0FBb0M7UUFDcEMsT0FBT2pVLFFBQVFrVSxHQUFHLENBQUNGLE1BQU0zRyxHQUFHLENBQUMyRyxDQUFBQSxRQUFTLElBQUloVSxRQUFRLENBQUNtVSxLQUFLQyxTQUFXSCxPQUFPSSxJQUFJLENBQUNMLE9BQU96UixDQUFBQTtvQkFDcEYsSUFBSUEsS0FBSzdILEtBQUssRUFBRXJHLE9BQU9NLE1BQU0sQ0FBQzROLE1BQU1ELFdBQVdDLEtBQUs3SCxLQUFLO29CQUN6RHlaLElBQUk1UjtnQkFDTixHQUFHdVIsWUFBWXpULENBQUFBLFFBQVMrVCxPQUFPLElBQUl6ZSxNQUFNLENBQUMsZUFBZSxFQUFFcWUsTUFBTSxFQUFFLEVBQUUzVCxTQUFTLE9BQU8sS0FBSyxJQUFJQSxNQUFNaVUsT0FBTyxDQUFDLENBQUMsT0FBT0MsT0FBTyxDQUFDLElBQU1OLE9BQU9sYyxPQUFPLElBQUksT0FBTyxLQUFLLElBQUlrYyxPQUFPbGMsT0FBTztJQUNwTDtBQUNGO0FBRUE7Ozs7O0NBS0MsR0FDRCxTQUFTeWMsVUFBVVQsS0FBSyxFQUFFQyxLQUFLLEVBQUVILFVBQVUsRUFBRUMsVUFBVTtJQUNyRCxvQ0FBb0M7SUFDcEMsTUFBTXpSLE9BQU90TSxNQUFNQyxPQUFPLENBQUNnZSxTQUFTQSxRQUFRO1FBQUNBO0tBQU07SUFDbkQsTUFBTVMsVUFBVXhnQixzREFBT0EsQ0FBQzJmLFVBQVVDLFlBQVlDLGFBQWE7UUFBQ0M7V0FBVTFSO0tBQUssRUFBRTtRQUMzRXFTLE9BQU8xVyxHQUFHOEQsR0FBRztJQUNmO0lBQ0Esc0JBQXNCO0lBQ3RCLE9BQU8vTCxNQUFNQyxPQUFPLENBQUNnZSxTQUFTUyxVQUFVQSxPQUFPLENBQUMsRUFBRTtBQUNwRDtBQUVBOztDQUVDLEdBQ0RELFVBQVV0Z0IsT0FBTyxHQUFHLFNBQVU2ZixLQUFLLEVBQUVDLEtBQUssRUFBRUgsVUFBVTtJQUNwRCxNQUFNeFIsT0FBT3RNLE1BQU1DLE9BQU8sQ0FBQ2dlLFNBQVNBLFFBQVE7UUFBQ0E7S0FBTTtJQUNuRCxPQUFPOWYsc0RBQU9BLENBQUMwZixVQUFVQyxhQUFhO1FBQUNFO1dBQVUxUjtLQUFLO0FBQ3hEO0FBRUE7O0NBRUMsR0FDRG1TLFVBQVVyZ0IsS0FBSyxHQUFHLFNBQVU0ZixLQUFLLEVBQUVDLEtBQUs7SUFDdEMsTUFBTTNSLE9BQU90TSxNQUFNQyxPQUFPLENBQUNnZSxTQUFTQSxRQUFRO1FBQUNBO0tBQU07SUFDbkQsT0FBTzdmLG9EQUFLQSxDQUFDO1FBQUM0ZjtXQUFVMVI7S0FBSztBQUMvQjtBQUVBLE1BQU1tUSxRQUFRLElBQUk1UjtBQUNsQixNQUFNLEVBQ0o2RixVQUFVLEVBQ1Z3SCxPQUFPLEVBQ1IsR0FBR3NFLFdBQVdDO0FBQ2YsTUFBTSxFQUNKdlksVUFBVSxFQUNWc0UsVUFBVSxFQUNYLEdBQUczSixlQUFlNGQsT0FBTzNLO0FBQzFCLE1BQU04TSxlQUFlO0lBQ25CamdCLFNBQVM7SUFDVHVOLFFBQVE7QUFDVjtBQUNBLE1BQU0yUyx5QkFBeUIsQ0FBQzVPLElBQUk2TztJQUNsQyxNQUFNQyxpQkFBaUIsT0FBTzlPLE9BQU8sYUFBYUEsR0FBRzZPLFVBQVU3TztJQUMvRCxJQUFJNkgsV0FBV2lILGlCQUFpQixPQUFPQTtTQUFvQixPQUFPLElBQUl2aEIsZ0RBQW1CLENBQUM7UUFDeEZ5aEIsaUJBQWlCO1FBQ2pCSCxRQUFRQTtRQUNSSSxXQUFXO1FBQ1hDLE9BQU87UUFDUCxHQUFHbFAsRUFBRTtJQUNQO0FBQ0Y7QUFDQSxTQUFTbVAsbUJBQW1CTixNQUFNLEVBQUVPLFdBQVc7SUFDN0MsSUFBSUEsYUFBYSxPQUFPQTtJQUN4QixJQUFJLE9BQU9oRSxzQkFBc0IsZUFBZXlELGtCQUFrQnpELHFCQUFxQnlELE9BQU9RLGFBQWEsRUFBRTtRQUMzRyxNQUFNLEVBQ0pyTyxLQUFLLEVBQ0xHLE1BQU0sRUFDTkQsR0FBRyxFQUNISCxJQUFJLEVBQ0wsR0FBRzhOLE9BQU9RLGFBQWEsQ0FBQ0MscUJBQXFCO1FBQzlDLE9BQU87WUFDTHRPO1lBQ0FHO1lBQ0FEO1lBQ0FIO1FBQ0Y7SUFDRixPQUFPLElBQUksT0FBT3dPLG9CQUFvQixlQUFlVixrQkFBa0JVLGlCQUFpQjtRQUN0RixPQUFPO1lBQ0x2TyxPQUFPNk4sT0FBTzdOLEtBQUs7WUFDbkJHLFFBQVEwTixPQUFPMU4sTUFBTTtZQUNyQkQsS0FBSztZQUNMSCxNQUFNO1FBQ1I7SUFDRjtJQUNBLE9BQU87UUFDTEMsT0FBTztRQUNQRyxRQUFRO1FBQ1JELEtBQUs7UUFDTEgsTUFBTTtJQUNSO0FBQ0Y7QUFDQSxTQUFTeU8sV0FBV1gsTUFBTTtJQUN4QiwyQ0FBMkM7SUFDM0MsTUFBTVksV0FBV2pELE1BQU12TixHQUFHLENBQUM0UDtJQUMzQixNQUFNYSxZQUFZRCxZQUFZLE9BQU8sS0FBSyxJQUFJQSxTQUFTemMsS0FBSztJQUM1RCxNQUFNMmMsWUFBWUYsWUFBWSxPQUFPLEtBQUssSUFBSUEsU0FBU2pOLEtBQUs7SUFDNUQsSUFBSWlOLFVBQVUxYixRQUFRQyxJQUFJLENBQUM7SUFFM0IseURBQXlEO0lBQ3pELHdEQUF3RDtJQUN4RCxNQUFNNGIsc0JBQXNCLE9BQU9DLGdCQUFnQixhQUNuRCxnRUFBZ0U7SUFDaEUsMENBQTBDO0lBQzFDQSxjQUNBLHNFQUFzRTtJQUN0RTliLFFBQVFzRyxLQUFLO0lBRWIsZUFBZTtJQUNmLE1BQU1tSSxRQUFRbU4sYUFBYTNILFlBQVl2SCxZQUFZd0g7SUFDbkQsa0JBQWtCO0lBQ2xCLE1BQU1qVixRQUFRMGMsYUFBYXpiLFdBQVc2YixlQUFlLENBQUN0TixPQUFPNVUsc0VBQWNBLEVBQUUsTUFBTSxPQUFPLE1BQU0sSUFBSWdpQixxQkFBcUI7SUFDekgsU0FBUztJQUNULElBQUksQ0FBQ0gsVUFBVWpELE1BQU16UyxHQUFHLENBQUM4VSxRQUFRO1FBQy9CN2I7UUFDQXdQO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsSUFBSXVOO0lBQ0osSUFBSUMsYUFBYTtJQUNqQixJQUFJQztJQUNKLE9BQU87UUFDTEMsV0FBVS9nQixRQUFRLENBQUMsQ0FBQztZQUNsQixJQUFJLEVBQ0Y2USxJQUFJbVEsUUFBUSxFQUNadFAsTUFBTXVQLFNBQVMsRUFDZjFiLE9BQU8yYixZQUFZLEVBQ25CeE0sTUFBTSxFQUNOa00sV0FBV08saUJBQWlCLEVBQzVCQyxVQUFVLEtBQUssRUFDZjlRLFNBQVMsS0FBSyxFQUNkOEosT0FBTyxLQUFLLEVBQ1pELFNBQVMsS0FBSyxFQUNka0gsZUFBZSxLQUFLLEVBQ3BCNUcsWUFBWSxRQUFRLEVBQ3BCNU8sTUFBTTtnQkFBQztnQkFBRzthQUFFLEVBQ1pqRCxhQUFBQSxZQUFXLEVBQ1g0TCxXQUFXOE0sY0FBYyxFQUN6QjdQLFFBQVE4UCxhQUFhLEVBQ3JCNUosZUFBZSxFQUNoQixHQUFHM1g7WUFDSixJQUFJaUwsUUFBUW9JLE1BQU1qUCxRQUFRO1lBRTFCLG1DQUFtQztZQUNuQyxJQUFJeU0sS0FBSzVGLE1BQU00RixFQUFFO1lBQ2pCLElBQUksQ0FBQzVGLE1BQU00RixFQUFFLEVBQUU1RixNQUFNTCxHQUFHLENBQUM7Z0JBQ3ZCaUcsSUFBSUEsS0FBSzRPLHVCQUF1QnVCLFVBQVV0QjtZQUM1QztZQUVBLG9DQUFvQztZQUNwQyxJQUFJbEwsWUFBWXZKLE1BQU11SixTQUFTO1lBQy9CLElBQUksQ0FBQ0EsV0FBV3ZKLE1BQU1MLEdBQUcsQ0FBQztnQkFDeEI0SixXQUFXQSxZQUFZLElBQUlwVyw0Q0FBZTtZQUM1QztZQUVBLHdCQUF3QjtZQUN4QixNQUFNLEVBQ0pxakIsTUFBTSxFQUNOLEdBQUdDLFNBQ0osR0FBR0osa0JBQWtCLENBQUM7WUFDdkIsSUFBSSxDQUFDelksR0FBRzhELEdBQUcsQ0FBQytVLFNBQVNsTixXQUFXZ0wsZUFBZXBXLFdBQVdvTCxXQUFXO2dCQUNuRSxHQUFHa04sT0FBTztZQUNaO1lBQ0EsSUFBSSxDQUFDN1ksR0FBRzhELEdBQUcsQ0FBQzhVLFFBQVFqTixVQUFVaU4sTUFBTSxFQUFFakMsZUFBZXBXLFdBQVdvTCxXQUFXO2dCQUN6RWlOLFFBQVE7b0JBQ04sR0FBR2pOLFVBQVVpTixNQUFNO29CQUNuQixHQUFHQSxNQUFNO2dCQUNYO1lBQ0Y7WUFFQSw0REFBNEQ7WUFDNUQsSUFBSSxDQUFDeFcsTUFBTXdHLE1BQU0sSUFBSXhHLE1BQU13RyxNQUFNLEtBQUtxUCxjQUFjLENBQUNqWSxHQUFHOEQsR0FBRyxDQUFDbVUsWUFBWVMsZUFBZS9CLGVBQWU7Z0JBQ3BHc0IsYUFBYVM7Z0JBQ2IsTUFBTUksV0FBV0oseUJBQXlCbmpCLHlDQUFZO2dCQUN0RCxNQUFNcVQsU0FBU2tRLFdBQVdKLGdCQUFnQkYsZUFBZSxJQUFJampCLHFEQUF3QixDQUFDLEdBQUcsR0FBRyxHQUFHLEdBQUcsS0FBSyxRQUFRLElBQUlBLG9EQUF1QixDQUFDLElBQUksR0FBRyxLQUFLO2dCQUN2SixJQUFJLENBQUN1akIsVUFBVTtvQkFDYmxRLE9BQU9zSCxRQUFRLENBQUNnSixDQUFDLEdBQUc7b0JBQ3BCLElBQUlSLGVBQWVuWSxXQUFXcUksUUFBUThQO29CQUN0QyxtQ0FBbUM7b0JBQ25DLElBQUksQ0FBQ3RXLE1BQU13RyxNQUFNLElBQUksQ0FBRThQLENBQUFBLGlCQUFpQixRQUFRQSxjQUFjUyxRQUFRLEdBQUd2USxPQUFPd1EsTUFBTSxDQUFDLEdBQUcsR0FBRztnQkFDL0Y7Z0JBQ0FoWCxNQUFNTCxHQUFHLENBQUM7b0JBQ1I2RztnQkFDRjtZQUNGO1lBRUEsZ0NBQWdDO1lBQ2hDLElBQUksQ0FBQ3hHLE1BQU0xRixLQUFLLEVBQUU7Z0JBQ2hCLElBQUlBO2dCQUNKLElBQUkyYix3QkFBd0I5aUIsd0NBQVcsRUFBRTtvQkFDdkNtSCxRQUFRMmI7Z0JBQ1YsT0FBTztvQkFDTDNiLFFBQVEsSUFBSW5ILHdDQUFXO29CQUN2QixJQUFJOGlCLGNBQWM5WCxXQUFXN0QsT0FBTzJiO2dCQUN0QztnQkFDQWpXLE1BQU1MLEdBQUcsQ0FBQztvQkFDUnJGLE9BQU85RSxRQUFROEU7Z0JBQ2pCO1lBQ0Y7WUFFQSw2QkFBNkI7WUFDN0IsSUFBSSxDQUFDMEYsTUFBTStPLEVBQUUsRUFBRTtnQkFDYixJQUFJbUk7Z0JBQ0osaUNBQWlDO2dCQUNqQyxNQUFNQyxnQkFBZ0IsQ0FBQ25JLFdBQVdnRDtvQkFDaEMsTUFBTWhTLFFBQVFvSSxNQUFNalAsUUFBUTtvQkFDNUIsSUFBSTZHLE1BQU13UCxTQUFTLEtBQUssU0FBUztvQkFDakMzQixRQUFRbUIsV0FBVyxNQUFNaFAsT0FBT2dTO2dCQUNsQztnQkFFQSxxQ0FBcUM7Z0JBQ3JDLE1BQU1vRixzQkFBc0I7b0JBQzFCLE1BQU1wWCxRQUFRb0ksTUFBTWpQLFFBQVE7b0JBQzVCNkcsTUFBTTRGLEVBQUUsQ0FBQ21KLEVBQUUsQ0FBQ25GLE9BQU8sR0FBRzVKLE1BQU00RixFQUFFLENBQUNtSixFQUFFLENBQUMwRCxZQUFZO29CQUM5Q3pTLE1BQU00RixFQUFFLENBQUNtSixFQUFFLENBQUNzSSxnQkFBZ0IsQ0FBQ3JYLE1BQU00RixFQUFFLENBQUNtSixFQUFFLENBQUMwRCxZQUFZLEdBQUcwRSxnQkFBZ0I7b0JBQ3hFLElBQUksQ0FBQ25YLE1BQU00RixFQUFFLENBQUNtSixFQUFFLENBQUMwRCxZQUFZLEVBQUVwTSxXQUFXckc7Z0JBQzVDO2dCQUVBLHdCQUF3QjtnQkFDeEIsTUFBTStPLEtBQUs7b0JBQ1R1STt3QkFDRSxNQUFNMVIsS0FBS3dDLE1BQU1qUCxRQUFRLEdBQUd5TSxFQUFFO3dCQUM5QkEsR0FBR21KLEVBQUUsQ0FBQ3dJLGdCQUFnQixDQUFDLGdCQUFnQkg7d0JBQ3ZDeFIsR0FBR21KLEVBQUUsQ0FBQ3dJLGdCQUFnQixDQUFDLGNBQWNIO29CQUN2QztvQkFDQUk7d0JBQ0UsTUFBTTVSLEtBQUt3QyxNQUFNalAsUUFBUSxHQUFHeU0sRUFBRTt3QkFDOUJBLEdBQUdtSixFQUFFLENBQUMwSSxtQkFBbUIsQ0FBQyxnQkFBZ0JMO3dCQUMxQ3hSLEdBQUdtSixFQUFFLENBQUMwSSxtQkFBbUIsQ0FBQyxjQUFjTDtvQkFDMUM7Z0JBQ0Y7Z0JBRUEsb0NBQW9DO2dCQUNwQyxJQUFJLE9BQVEsRUFBQ0YsU0FBU3RSLEdBQUdtSixFQUFFLEtBQUssT0FBTyxLQUFLLElBQUltSSxPQUFPSyxnQkFBZ0IsTUFBTSxZQUFZeEksR0FBR3VJLE9BQU87Z0JBQ25HdFgsTUFBTUwsR0FBRyxDQUFDO29CQUNSb1A7Z0JBQ0Y7WUFDRjtZQUVBLGdCQUFnQjtZQUNoQixJQUFJbkosR0FBRzhSLFNBQVMsRUFBRTtnQkFDaEIsTUFBTUMsYUFBYS9SLEdBQUc4UixTQUFTLENBQUM5TixPQUFPO2dCQUN2QyxNQUFNZ08sVUFBVWhTLEdBQUc4UixTQUFTLENBQUM5aUIsSUFBSTtnQkFDakNnUixHQUFHOFIsU0FBUyxDQUFDOU4sT0FBTyxHQUFHLENBQUMsQ0FBQ3VNO2dCQUN6QixJQUFJdlksR0FBRzRELEdBQUcsQ0FBQzJVLFVBQVU7b0JBQ25CdlEsR0FBRzhSLFNBQVMsQ0FBQzlpQixJQUFJLEdBQUd6QixtREFBc0I7Z0JBQzVDLE9BQU8sSUFBSXlLLEdBQUcwRCxHQUFHLENBQUM2VSxVQUFVO29CQUMxQixJQUFJMkI7b0JBQ0osTUFBTUMsUUFBUTt3QkFDWkMsT0FBTzdrQixpREFBb0I7d0JBQzNCK2tCLFlBQVkva0IsK0NBQWtCO3dCQUM5QmlsQixNQUFNamxCLG1EQUFzQjt3QkFDNUJrbEIsVUFBVWxsQiwrQ0FBa0I7b0JBQzlCO29CQUNBeVMsR0FBRzhSLFNBQVMsQ0FBQzlpQixJQUFJLEdBQUcsQ0FBQ2tqQixpQkFBaUJDLEtBQUssQ0FBQzVCLFFBQVEsS0FBSyxPQUFPMkIsaUJBQWlCM2tCLG1EQUFzQjtnQkFDekcsT0FBTyxJQUFJeUssR0FBR2lCLEdBQUcsQ0FBQ3NYLFVBQVU7b0JBQzFCbGlCLE9BQU9NLE1BQU0sQ0FBQ3FSLEdBQUc4UixTQUFTLEVBQUV2QjtnQkFDOUI7Z0JBQ0EsSUFBSXdCLGVBQWUvUixHQUFHOFIsU0FBUyxDQUFDOU4sT0FBTyxJQUFJZ08sWUFBWWhTLEdBQUc4UixTQUFTLENBQUM5aUIsSUFBSSxFQUFFZ1IsR0FBRzhSLFNBQVMsQ0FBQ2EsV0FBVyxHQUFHO1lBQ3ZHO1lBRUEsNENBQTRDO1lBQzVDLHlFQUF5RTtZQUN6RSxNQUFNOVosa0JBQWtCRjtZQUN4QixJQUFJRSxpQkFBaUI7Z0JBQ25CLElBQUksYUFBYUEsaUJBQWlCQSxnQkFBZ0JtTCxPQUFPLEdBQUcsQ0FBQ3NGO3FCQUFZLElBQUksZ0JBQWdCelEsaUJBQWlCQSxnQkFBZ0IrWixVQUFVLEdBQUd0SjtZQUM3STtZQUVBLDhDQUE4QztZQUM5QyxNQUFNdUosaUJBQWlCO1lBQ3ZCLE1BQU1wVSxlQUFlO1lBQ3JCbEcsV0FBV3lILElBQUk7Z0JBQ2JJLGdCQUFnQlgsU0FBU29ULGlCQUFpQnBVO2dCQUMxQ3FVLGFBQWF2SixPQUFPaGMsZ0RBQW1CLEdBQUdBLHdEQUEyQjtZQUN2RTtZQUVBLGdDQUFnQztZQUNoQyxJQUFJNk0sTUFBTWtQLE1BQU0sS0FBS0EsUUFBUWxQLE1BQU1MLEdBQUcsQ0FBQyxJQUFPO29CQUM1Q3VQO2dCQUNGO1lBQ0EsSUFBSWxQLE1BQU1xRixNQUFNLEtBQUtBLFFBQVFyRixNQUFNTCxHQUFHLENBQUMsSUFBTztvQkFDNUMwRjtnQkFDRjtZQUNBLElBQUlyRixNQUFNbVAsSUFBSSxLQUFLQSxNQUFNblAsTUFBTUwsR0FBRyxDQUFDLElBQU87b0JBQ3hDd1A7Z0JBQ0Y7WUFFQSxlQUFlO1lBQ2YsSUFBSTRHLFlBQVksQ0FBQ25ZLEdBQUdDLEdBQUcsQ0FBQ2tZLGFBQWEsQ0FBQ3RJLFdBQVdzSSxhQUFhLENBQUNuWSxHQUFHOEQsR0FBRyxDQUFDcVUsVUFBVW5RLElBQUkyTyxlQUFlcFcsV0FBV3lILElBQUltUTtZQUNsSCwwQkFBMEI7WUFDMUIsSUFBSXRNLFVBQVUsQ0FBQ3pKLE1BQU15SixNQUFNLENBQUN6TyxRQUFRLEVBQUVnRixNQUFNTCxHQUFHLENBQUM7Z0JBQzlDOEosUUFBUUEsT0FBT3JCO1lBQ2pCO1lBQ0EsNkRBQTZEO1lBQzdELE1BQU0zQixPQUFPc08sbUJBQW1CTixRQUFRdUI7WUFDeEMsSUFBSSxDQUFDcFksR0FBRzhELEdBQUcsQ0FBQytFLE1BQU16RyxNQUFNeUcsSUFBSSxFQUFFOE4sZUFBZTtnQkFDM0N2VSxNQUFNK1AsT0FBTyxDQUFDdEosS0FBS0csS0FBSyxFQUFFSCxLQUFLTSxNQUFNLEVBQUVOLEtBQUtrSixXQUFXLEVBQUVsSixLQUFLSyxHQUFHLEVBQUVMLEtBQUtFLElBQUk7WUFDOUU7WUFDQSxtQkFBbUI7WUFDbkIsSUFBSS9GLE9BQU9aLE1BQU00UCxRQUFRLENBQUNoUCxHQUFHLEtBQUtELGFBQWFDLE1BQU1aLE1BQU1nUSxNQUFNLENBQUNwUDtZQUNsRSxrQkFBa0I7WUFDbEIsSUFBSVosTUFBTXdQLFNBQVMsS0FBS0EsV0FBV3hQLE1BQU1rUSxZQUFZLENBQUNWO1lBQ3RELHVCQUF1QjtZQUN2QixJQUFJLENBQUN4UCxNQUFNME0sZUFBZSxFQUFFMU0sTUFBTUwsR0FBRyxDQUFDO2dCQUNwQytNO1lBQ0Y7WUFDQSxvQkFBb0I7WUFDcEIsSUFBSS9PLGdCQUFlLENBQUNDLEdBQUc4RCxHQUFHLENBQUMvRCxjQUFhcUMsTUFBTXJDLFdBQVcsRUFBRTRXLGVBQWV2VSxNQUFNTCxHQUFHLENBQUNLLENBQUFBLFFBQVU7b0JBQzVGckMsYUFBYTt3QkFDWCxHQUFHcUMsTUFBTXJDLFdBQVc7d0JBQ3BCLEdBQUdBLFlBQVc7b0JBQ2hCO2dCQUNGO1lBRUEsYUFBYTtZQUNiZ1ksWUFBWU87WUFDWk4sYUFBYTtZQUNiLE9BQU8sSUFBSTtRQUNiO1FBQ0F4VixRQUFPaEosUUFBUTtZQUNiLDBEQUEwRDtZQUMxRCxJQUFJLENBQUN3ZSxZQUFZLElBQUksQ0FBQ0UsU0FBUztZQUMvQmpjLFdBQVdnZixlQUFlLENBQUUsV0FBVyxHQUFFemxCLGdEQUFtQixDQUFDMGxCLFVBQVU7Z0JBQ3JFMVEsT0FBT0E7Z0JBQ1BoUixVQUFVQTtnQkFDVnVlLFdBQVdBO2dCQUNYb0QsYUFBYXRFO1lBQ2YsSUFBSTdiLE9BQU8sTUFBTSxJQUFNdEQ7WUFDdkIsT0FBTzhTO1FBQ1Q7UUFDQTRRO1lBQ0VDLHVCQUF1QnhFO1FBQ3pCO0lBQ0Y7QUFDRjtBQUNBLFNBQVNyVSxPQUFPaEosUUFBUSxFQUFFcWQsTUFBTSxFQUFFeUUsTUFBTTtJQUN0Q3ZmLFFBQVFDLElBQUksQ0FBQztJQUNiLE1BQU01RSxPQUFPb2dCLFdBQVdYO0lBQ3hCemYsS0FBSzhnQixTQUFTLENBQUNvRDtJQUNmLE9BQU9sa0IsS0FBS29MLE1BQU0sQ0FBQ2hKO0FBQ3JCO0FBQ0EsU0FBUzBoQixTQUFTLEVBQ2hCMVEsS0FBSyxFQUNMaFIsUUFBUSxFQUNSdWUsU0FBUyxFQUNUb0QsV0FBVyxFQUNaO0lBQ0NoYSwwQkFBMEI7UUFDeEIsTUFBTWlCLFFBQVFvSSxNQUFNalAsUUFBUTtRQUM1QixtREFBbUQ7UUFDbkQ2RyxNQUFNTCxHQUFHLENBQUNLLENBQUFBLFFBQVU7Z0JBQ2xCNUcsVUFBVTtvQkFDUixHQUFHNEcsTUFBTTVHLFFBQVE7b0JBQ2pCa1gsUUFBUTtnQkFDVjtZQUNGO1FBQ0EsdUZBQXVGO1FBQ3ZGLElBQUlxRixXQUFXQSxVQUFVM1Y7UUFDekIsd0ZBQXdGO1FBQ3hGLGdEQUFnRDtRQUNoRCxJQUFJLENBQUNvSSxNQUFNalAsUUFBUSxHQUFHc1EsTUFBTSxDQUFDcUYsU0FBUyxFQUFFOU8sTUFBTXlKLE1BQU0sQ0FBQzZOLE9BQU8sSUFBSSxPQUFPLEtBQUssSUFBSXRYLE1BQU15SixNQUFNLENBQUM2TixPQUFPLENBQUN5QjtJQUNyRyx1REFBdUQ7SUFDekQsR0FBRyxFQUFFO0lBQ0wsT0FBTyxXQUFXLEdBQUUzbEIsZ0RBQW1CLENBQUNzYSxRQUFRb0wsUUFBUSxFQUFFO1FBQ3hEbGQsT0FBT3dNO0lBQ1QsR0FBR2hSO0FBQ0w7QUFDQSxTQUFTNmhCLHVCQUF1QnhFLE1BQU0sRUFBRTFKLFFBQVE7SUFDOUMsTUFBTS9WLE9BQU9vZCxNQUFNdk4sR0FBRyxDQUFDNFA7SUFDdkIsTUFBTTdiLFFBQVE1RCxRQUFRLE9BQU8sS0FBSyxJQUFJQSxLQUFLNEQsS0FBSztJQUNoRCxJQUFJQSxPQUFPO1FBQ1QsTUFBTW9ILFFBQVFoTCxRQUFRLE9BQU8sS0FBSyxJQUFJQSxLQUFLb1QsS0FBSyxDQUFDalAsUUFBUTtRQUN6RCxJQUFJNkcsT0FBT0EsTUFBTTVHLFFBQVEsQ0FBQ2tYLE1BQU0sR0FBRztRQUNuQ3pXLFdBQVdnZixlQUFlLENBQUMsTUFBTWpnQixPQUFPLE1BQU07WUFDNUMsSUFBSW9ILE9BQU87Z0JBQ1RoQyxXQUFXO29CQUNULElBQUk7d0JBQ0YsSUFBSW1iLFdBQVdDLHVCQUF1QkMsWUFBWUM7d0JBQ2xEdFosTUFBTXlKLE1BQU0sQ0FBQytOLFVBQVUsSUFBSSxPQUFPLEtBQUssSUFBSXhYLE1BQU15SixNQUFNLENBQUMrTixVQUFVO3dCQUNqRTJCLENBQUFBLFlBQVluWixNQUFNNEYsRUFBRSxLQUFLLE9BQU8sS0FBSyxJQUFJLENBQUN3VCx3QkFBd0JELFVBQVVJLFdBQVcsS0FBSyxPQUFPLEtBQUssSUFBSUgsc0JBQXNCemhCLE9BQU8sSUFBSSxPQUFPLEtBQUssSUFBSXloQixzQkFBc0J6aEIsT0FBTzt3QkFDMUwwaEIsQ0FBQUEsYUFBYXJaLE1BQU00RixFQUFFLEtBQUssT0FBTyxLQUFLLElBQUl5VCxXQUFXRyxnQkFBZ0IsSUFBSSxPQUFPLEtBQUssSUFBSUgsV0FBV0csZ0JBQWdCO3dCQUNySCxJQUFJLENBQUNGLGFBQWF0WixNQUFNNEYsRUFBRSxLQUFLLFFBQVEwVCxXQUFXdkssRUFBRSxFQUFFL08sTUFBTStPLEVBQUUsQ0FBQ3lJLFVBQVU7d0JBQ3pFN2YsUUFBUXFJO3dCQUNSb1MsTUFBTWxLLE1BQU0sQ0FBQ3VNO3dCQUNiLElBQUkxSixVQUFVQSxTQUFTMEo7b0JBQ3pCLEVBQUUsT0FBT2hjLEdBQUc7b0JBQ1YsT0FBTyxHQUNUO2dCQUNGLEdBQUc7WUFDTDtRQUNGO0lBQ0Y7QUFDRjtBQUNBLFNBQVNnaEIsYUFBYXJpQixRQUFRLEVBQUVpRCxTQUFTLEVBQUUyRixLQUFLO0lBQzlDLE9BQU8sV0FBVyxHQUFFNU0sZ0RBQW1CLENBQUNzbUIsUUFBUTtRQUM5Qy9XLEtBQUt0SSxVQUFVa04sSUFBSTtRQUNuQm5RLFVBQVVBO1FBQ1ZpRCxXQUFXQTtRQUNYMkYsT0FBT0E7SUFDVDtBQUNGO0FBQ0EsU0FBUzBaLE9BQU8sRUFDZDFaLFFBQVEsQ0FBQyxDQUFDLEVBQ1Y1SSxRQUFRLEVBQ1JpRCxTQUFTLEVBQ1Y7SUFDQzs7Ozs0QkFJMEIsR0FDMUIsTUFBTSxFQUNKb1AsTUFBTSxFQUNOaEQsSUFBSSxFQUNKLEdBQUdrVCxNQUNKLEdBQUczWjtJQUNKLE1BQU13SixlQUFlcUo7SUFDckIsTUFBTSxDQUFDdEosVUFBVSxHQUFHblcsMkNBQWMsQ0FBQyxJQUFNLElBQUlELDRDQUFlO0lBQzVELE1BQU0sQ0FBQzhYLFFBQVEsR0FBRzdYLDJDQUFjLENBQUMsSUFBTSxJQUFJRCwwQ0FBYTtJQUN4RCxNQUFNMG1CLFNBQVN6bUIsOENBQWlCLENBQUMsQ0FBQzhGLFdBQVc2Z0I7UUFDM0MsTUFBTTdRLFlBQVk7WUFDaEIsR0FBR2hRLFNBQVM7UUFDZCxHQUFHLHVCQUF1QjtRQUUxQixxRUFBcUU7UUFDckUsa0NBQWtDO1FBQ2xDLDZGQUE2RjtRQUM3RmpGLE9BQU9nTyxJQUFJLENBQUMvSSxXQUFXdEIsT0FBTyxDQUFDK0ssQ0FBQUE7WUFDN0IsSUFDQSxrQ0FBa0M7WUFDbEM2SyxZQUFZNUssUUFBUSxDQUFDRCxRQUNyQiw2RkFBNkY7WUFDN0YsMEVBQTBFO1lBQzFFekosU0FBUyxDQUFDeUosSUFBSSxLQUFLb1gsV0FBVyxDQUFDcFgsSUFBSSxJQUFJb1gsV0FBVyxDQUFDcFgsSUFBSSxFQUFFO2dCQUN2RCxPQUFPdUcsU0FBUyxDQUFDdkcsSUFBSTtZQUN2QjtRQUNGO1FBQ0EsSUFBSWlOLFdBQVd0YTtRQUNmLElBQUl5a0IsZUFBZXRULE1BQU07WUFDdkIsTUFBTUQsU0FBU3VULFlBQVl2VCxNQUFNO1lBQ2pDLDhDQUE4QztZQUM5Q29KLFdBQVcxVyxVQUFVMFcsUUFBUSxDQUFDM0Isa0JBQWtCLENBQUN6SCxRQUFRLElBQUlyVCwwQ0FBYSxJQUFJc1Q7WUFDOUUsa0VBQWtFO1lBQ2xFLElBQUlELFdBQVd0TixVQUFVc04sTUFBTSxFQUFFRCxhQUFhQyxRQUFRQztRQUN4RDtRQUNBLE9BQU87WUFDTCxvREFBb0Q7WUFDcEQsR0FBR3lDLFNBQVM7WUFDWixnRkFBZ0Y7WUFDaEY1TyxPQUFPRDtZQUNQa1A7WUFDQTBCO1lBQ0FzRSxPQUFPdEU7WUFDUCw2Q0FBNkM7WUFDN0N6QjtZQUNBLGtFQUFrRTtZQUNsRUMsUUFBUTtnQkFDTixHQUFHdlEsVUFBVXVRLE1BQU07Z0JBQ25CLEdBQUlzUSxlQUFlLE9BQU8sS0FBSyxJQUFJQSxZQUFZdFEsTUFBTTtnQkFDckQsR0FBR0EsTUFBTTtZQUNYO1lBQ0FoRCxNQUFNO2dCQUNKLEdBQUd2TixVQUFVdU4sSUFBSTtnQkFDakIsR0FBR0EsSUFBSTtZQUNUO1lBQ0FtSixVQUFVO2dCQUNSLEdBQUcxVyxVQUFVMFcsUUFBUTtnQkFDckIsR0FBR0EsUUFBUTtZQUNiO1lBQ0EsR0FBRytKLElBQUk7UUFDVDtJQUNGLEdBQUc7UUFBQzNaO0tBQU07SUFDVixNQUFNLENBQUNnYSxlQUFlLEdBQUc1bUIsMkNBQWMsQ0FBQztRQUN0QywrRUFBK0U7UUFDL0UsTUFBTTZtQixnQkFBZ0J6USxhQUFhclEsUUFBUTtRQUMzQyxNQUFNaVAsUUFBUTNVLG1EQUFNQSxDQUFDLENBQUNrTSxLQUFLa0YsTUFBUztnQkFDbEMsR0FBR29WLGFBQWE7Z0JBQ2hCM2YsT0FBT0Q7Z0JBQ1BrUDtnQkFDQTBCO2dCQUNBc0UsT0FBT3RFO2dCQUNQekI7Z0JBQ0FDLFFBQVE7b0JBQ04sR0FBR3dRLGNBQWN4USxNQUFNO29CQUN2QixHQUFHQSxNQUFNO2dCQUNYO2dCQUNBaEQsTUFBTTtvQkFDSixHQUFHd1QsY0FBY3hULElBQUk7b0JBQ3JCLEdBQUdBLElBQUk7Z0JBQ1Q7Z0JBQ0EsR0FBR2tULElBQUk7Z0JBQ1AsdUNBQXVDO2dCQUN2Q2hhO2dCQUNBa0Y7Z0JBQ0Esd0NBQXdDO2dCQUN4Q2lMLFdBQVdyRyxDQUFBQSxTQUFVOUosSUFBSUssQ0FBQUEsUUFBVTs0QkFDakMsR0FBR0EsS0FBSzs0QkFDUnlKLFFBQVE7Z0NBQ04sR0FBR3pKLE1BQU15SixNQUFNO2dDQUNmLEdBQUdBLE1BQU07NEJBQ1g7d0JBQ0Y7WUFDRjtRQUNBLE9BQU9yQjtJQUNUO0lBQ0FoViw0Q0FBZSxDQUFDO1FBQ2Qsc0ZBQXNGO1FBQ3RGLE1BQU04bUIsUUFBUTFRLGFBQWFpSCxTQUFTLENBQUMwSixDQUFBQSxPQUFRSCxlQUFlSSxRQUFRLENBQUNwYSxDQUFBQSxRQUFTNlosT0FBT00sTUFBTW5hO1FBQzNGLE9BQU87WUFDTGthO1lBQ0FGLGVBQWVLLE9BQU87UUFDeEI7SUFDRixHQUFHLEVBQUU7SUFDTGpuQiw0Q0FBZSxDQUFDO1FBQ2Q0bUIsZUFBZUksUUFBUSxDQUFDTCxDQUFBQSxjQUFlRixPQUFPclEsYUFBYXJRLFFBQVEsSUFBSTRnQjtJQUN6RSxHQUFHO1FBQUNGO0tBQU87SUFDWCxPQUFPLFdBQVcsR0FBRXptQixnREFBbUIsQ0FBQ0EsMkNBQWMsRUFBRSxNQUFNeUcsV0FBVzRmLFlBQVksQ0FBRSxXQUFXLEdBQUVybUIsZ0RBQW1CLENBQUNzYSxRQUFRb0wsUUFBUSxFQUFFO1FBQ3hJbGQsT0FBT29lO0lBQ1QsR0FBRzVpQixXQUFXNGlCLGdCQUFnQjtBQUNoQztBQUNBbmdCLFdBQVcwZ0Isa0JBQWtCLENBQUM7SUFDNUJDLFlBQVl2VyxNQUF5QixHQUFlLENBQUMsR0FBRztJQUN4RHdXLHFCQUFxQjtJQUNyQkMsU0FBU3RuQiwwQ0FBYTtBQUN4QjtBQUNBLE1BQU11bkIsTUFBTXZuQiwrQ0FBa0I7QUFFdWhCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmFqYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZmliZXIvZGlzdC9pbmRleC01OTE4MDEyYS5lc20uanM/N2RjMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBUSFJFRSBmcm9tICd0aHJlZSc7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBEZWZhdWx0RXZlbnRQcmlvcml0eSwgQ29udGludW91c0V2ZW50UHJpb3JpdHksIERpc2NyZXRlRXZlbnRQcmlvcml0eSwgQ29uY3VycmVudFJvb3QgfSBmcm9tICdyZWFjdC1yZWNvbmNpbGVyL2NvbnN0YW50cyc7XG5pbXBvcnQgY3JlYXRlIGZyb20gJ3p1c3RhbmQnO1xuaW1wb3J0IFJlY29uY2lsZXIgZnJvbSAncmVhY3QtcmVjb25jaWxlcic7XG5pbXBvcnQgeyB1bnN0YWJsZV9zY2hlZHVsZUNhbGxiYWNrLCB1bnN0YWJsZV9JZGxlUHJpb3JpdHkgfSBmcm9tICdzY2hlZHVsZXInO1xuaW1wb3J0IHsgc3VzcGVuZCwgcHJlbG9hZCwgY2xlYXIgfSBmcm9tICdzdXNwZW5kLXJlYWN0JztcblxudmFyIHRocmVlVHlwZXMgPSAvKiNfX1BVUkVfXyovT2JqZWN0LmZyZWV6ZSh7XG4gIF9fcHJvdG9fXzogbnVsbFxufSk7XG5cbmNvbnN0IGNhdGFsb2d1ZSA9IHt9O1xuY29uc3QgZXh0ZW5kID0gb2JqZWN0cyA9PiB2b2lkIE9iamVjdC5hc3NpZ24oY2F0YWxvZ3VlLCBvYmplY3RzKTtcbmZ1bmN0aW9uIGNyZWF0ZVJlbmRlcmVyKF9yb290cywgX2dldEV2ZW50UHJpb3JpdHkpIHtcbiAgZnVuY3Rpb24gY3JlYXRlSW5zdGFuY2UodHlwZSwge1xuICAgIGFyZ3MgPSBbXSxcbiAgICBhdHRhY2gsXG4gICAgLi4ucHJvcHNcbiAgfSwgcm9vdCkge1xuICAgIGxldCBuYW1lID0gYCR7dHlwZVswXS50b1VwcGVyQ2FzZSgpfSR7dHlwZS5zbGljZSgxKX1gO1xuICAgIGxldCBpbnN0YW5jZTtcbiAgICBpZiAodHlwZSA9PT0gJ3ByaW1pdGl2ZScpIHtcbiAgICAgIGlmIChwcm9wcy5vYmplY3QgPT09IHVuZGVmaW5lZCkgdGhyb3cgbmV3IEVycm9yKFwiUjNGOiBQcmltaXRpdmVzIHdpdGhvdXQgJ29iamVjdCcgYXJlIGludmFsaWQhXCIpO1xuICAgICAgY29uc3Qgb2JqZWN0ID0gcHJvcHMub2JqZWN0O1xuICAgICAgaW5zdGFuY2UgPSBwcmVwYXJlKG9iamVjdCwge1xuICAgICAgICB0eXBlLFxuICAgICAgICByb290LFxuICAgICAgICBhdHRhY2gsXG4gICAgICAgIHByaW1pdGl2ZTogdHJ1ZVxuICAgICAgfSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnN0IHRhcmdldCA9IGNhdGFsb2d1ZVtuYW1lXTtcbiAgICAgIGlmICghdGFyZ2V0KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgUjNGOiAke25hbWV9IGlzIG5vdCBwYXJ0IG9mIHRoZSBUSFJFRSBuYW1lc3BhY2UhIERpZCB5b3UgZm9yZ2V0IHRvIGV4dGVuZD8gU2VlOiBodHRwczovL2RvY3MucG1uZC5ycy9yZWFjdC10aHJlZS1maWJlci9hcGkvb2JqZWN0cyN1c2luZy0zcmQtcGFydHktb2JqZWN0cy1kZWNsYXJhdGl2ZWx5YCk7XG4gICAgICB9XG5cbiAgICAgIC8vIFRocm93IGlmIGFuIG9iamVjdCBvciBsaXRlcmFsIHdhcyBwYXNzZWQgZm9yIGFyZ3NcbiAgICAgIGlmICghQXJyYXkuaXNBcnJheShhcmdzKSkgdGhyb3cgbmV3IEVycm9yKCdSM0Y6IFRoZSBhcmdzIHByb3AgbXVzdCBiZSBhbiBhcnJheSEnKTtcblxuICAgICAgLy8gSW5zdGFuY2lhdGUgbmV3IG9iamVjdCwgbGluayBpdCB0byB0aGUgcm9vdFxuICAgICAgLy8gQXBwZW5kIG1lbW9pemVkIHByb3BzIHdpdGggYXJncyBzbyBpdCdzIG5vdCBmb3Jnb3R0ZW5cbiAgICAgIGluc3RhbmNlID0gcHJlcGFyZShuZXcgdGFyZ2V0KC4uLmFyZ3MpLCB7XG4gICAgICAgIHR5cGUsXG4gICAgICAgIHJvb3QsXG4gICAgICAgIGF0dGFjaCxcbiAgICAgICAgLy8gU2F2ZSBhcmdzIGluIGNhc2Ugd2UgbmVlZCB0byByZWNvbnN0cnVjdCBsYXRlciBmb3IgSE1SXG4gICAgICAgIG1lbW9pemVkUHJvcHM6IHtcbiAgICAgICAgICBhcmdzXG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIEF1dG8tYXR0YWNoIGdlb21ldHJpZXMgYW5kIG1hdGVyaWFsc1xuICAgIGlmIChpbnN0YW5jZS5fX3IzZi5hdHRhY2ggPT09IHVuZGVmaW5lZCkge1xuICAgICAgaWYgKGluc3RhbmNlIGluc3RhbmNlb2YgVEhSRUUuQnVmZmVyR2VvbWV0cnkpIGluc3RhbmNlLl9fcjNmLmF0dGFjaCA9ICdnZW9tZXRyeSc7ZWxzZSBpZiAoaW5zdGFuY2UgaW5zdGFuY2VvZiBUSFJFRS5NYXRlcmlhbCkgaW5zdGFuY2UuX19yM2YuYXR0YWNoID0gJ21hdGVyaWFsJztcbiAgICB9XG5cbiAgICAvLyBJdCBzaG91bGQgTk9UIGNhbGwgb25VcGRhdGUgb24gb2JqZWN0IGluc3RhbmNpYXRpb24sIGJlY2F1c2UgaXQgaGFzbid0IGJlZW4gYWRkZWQgdG8gdGhlXG4gICAgLy8gdmlldyB5ZXQuIElmIHRoZSBjYWxsYmFjayByZWxpZXMgb24gcmVmZXJlbmNlcyBmb3IgaW5zdGFuY2UsIHRoZXkgd29uJ3QgYmUgcmVhZHkgeWV0LCB0aGlzIGlzXG4gICAgLy8gd2h5IGl0IHBhc3NlcyBcInRydWVcIiBoZXJlXG4gICAgLy8gVGhlcmUgaXMgbm8gcmVhc29uIHRvIGFwcGx5IHByb3BzIHRvIGluamVjdHNcbiAgICBpZiAobmFtZSAhPT0gJ2luamVjdCcpIGFwcGx5UHJvcHMkMShpbnN0YW5jZSwgcHJvcHMpO1xuICAgIHJldHVybiBpbnN0YW5jZTtcbiAgfVxuICBmdW5jdGlvbiBhcHBlbmRDaGlsZChwYXJlbnRJbnN0YW5jZSwgY2hpbGQpIHtcbiAgICBsZXQgYWRkZWQgPSBmYWxzZTtcbiAgICBpZiAoY2hpbGQpIHtcbiAgICAgIHZhciBfY2hpbGQkX19yM2YsIF9wYXJlbnRJbnN0YW5jZSRfX3IzZjtcbiAgICAgIC8vIFRoZSBhdHRhY2ggYXR0cmlidXRlIGltcGxpZXMgdGhhdCB0aGUgb2JqZWN0IGF0dGFjaGVzIGl0c2VsZiBvbiB0aGUgcGFyZW50XG4gICAgICBpZiAoKF9jaGlsZCRfX3IzZiA9IGNoaWxkLl9fcjNmKSAhPSBudWxsICYmIF9jaGlsZCRfX3IzZi5hdHRhY2gpIHtcbiAgICAgICAgYXR0YWNoKHBhcmVudEluc3RhbmNlLCBjaGlsZCwgY2hpbGQuX19yM2YuYXR0YWNoKTtcbiAgICAgIH0gZWxzZSBpZiAoY2hpbGQuaXNPYmplY3QzRCAmJiBwYXJlbnRJbnN0YW5jZS5pc09iamVjdDNEKSB7XG4gICAgICAgIC8vIGFkZCBpbiB0aGUgdXN1YWwgcGFyZW50LWNoaWxkIHdheVxuICAgICAgICBwYXJlbnRJbnN0YW5jZS5hZGQoY2hpbGQpO1xuICAgICAgICBhZGRlZCA9IHRydWU7XG4gICAgICB9XG4gICAgICAvLyBUaGlzIGlzIGZvciBhbnl0aGluZyB0aGF0IHVzZWQgYXR0YWNoLCBhbmQgZm9yIG5vbi1PYmplY3QzRHMgdGhhdCBkb24ndCBnZXQgYXR0YWNoZWQgdG8gcHJvcHM7XG4gICAgICAvLyB0aGF0IGlzLCBhbnl0aGluZyB0aGF0J3MgYSBjaGlsZCBpbiBSZWFjdCBidXQgbm90IGEgY2hpbGQgaW4gdGhlIHNjZW5lZ3JhcGguXG4gICAgICBpZiAoIWFkZGVkKSAoX3BhcmVudEluc3RhbmNlJF9fcjNmID0gcGFyZW50SW5zdGFuY2UuX19yM2YpID09IG51bGwgPyB2b2lkIDAgOiBfcGFyZW50SW5zdGFuY2UkX19yM2Yub2JqZWN0cy5wdXNoKGNoaWxkKTtcbiAgICAgIGlmICghY2hpbGQuX19yM2YpIHByZXBhcmUoY2hpbGQsIHt9KTtcbiAgICAgIGNoaWxkLl9fcjNmLnBhcmVudCA9IHBhcmVudEluc3RhbmNlO1xuICAgICAgdXBkYXRlSW5zdGFuY2UoY2hpbGQpO1xuICAgICAgaW52YWxpZGF0ZUluc3RhbmNlKGNoaWxkKTtcbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gaW5zZXJ0QmVmb3JlKHBhcmVudEluc3RhbmNlLCBjaGlsZCwgYmVmb3JlQ2hpbGQpIHtcbiAgICBsZXQgYWRkZWQgPSBmYWxzZTtcbiAgICBpZiAoY2hpbGQpIHtcbiAgICAgIHZhciBfY2hpbGQkX19yM2YyLCBfcGFyZW50SW5zdGFuY2UkX19yM2YyO1xuICAgICAgaWYgKChfY2hpbGQkX19yM2YyID0gY2hpbGQuX19yM2YpICE9IG51bGwgJiYgX2NoaWxkJF9fcjNmMi5hdHRhY2gpIHtcbiAgICAgICAgYXR0YWNoKHBhcmVudEluc3RhbmNlLCBjaGlsZCwgY2hpbGQuX19yM2YuYXR0YWNoKTtcbiAgICAgIH0gZWxzZSBpZiAoY2hpbGQuaXNPYmplY3QzRCAmJiBwYXJlbnRJbnN0YW5jZS5pc09iamVjdDNEKSB7XG4gICAgICAgIGNoaWxkLnBhcmVudCA9IHBhcmVudEluc3RhbmNlO1xuICAgICAgICBjaGlsZC5kaXNwYXRjaEV2ZW50KHtcbiAgICAgICAgICB0eXBlOiAnYWRkZWQnXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCByZXN0U2libGluZ3MgPSBwYXJlbnRJbnN0YW5jZS5jaGlsZHJlbi5maWx0ZXIoc2libGluZyA9PiBzaWJsaW5nICE9PSBjaGlsZCk7XG4gICAgICAgIGNvbnN0IGluZGV4ID0gcmVzdFNpYmxpbmdzLmluZGV4T2YoYmVmb3JlQ2hpbGQpO1xuICAgICAgICBwYXJlbnRJbnN0YW5jZS5jaGlsZHJlbiA9IFsuLi5yZXN0U2libGluZ3Muc2xpY2UoMCwgaW5kZXgpLCBjaGlsZCwgLi4ucmVzdFNpYmxpbmdzLnNsaWNlKGluZGV4KV07XG4gICAgICAgIGFkZGVkID0gdHJ1ZTtcbiAgICAgIH1cbiAgICAgIGlmICghYWRkZWQpIChfcGFyZW50SW5zdGFuY2UkX19yM2YyID0gcGFyZW50SW5zdGFuY2UuX19yM2YpID09IG51bGwgPyB2b2lkIDAgOiBfcGFyZW50SW5zdGFuY2UkX19yM2YyLm9iamVjdHMucHVzaChjaGlsZCk7XG4gICAgICBpZiAoIWNoaWxkLl9fcjNmKSBwcmVwYXJlKGNoaWxkLCB7fSk7XG4gICAgICBjaGlsZC5fX3IzZi5wYXJlbnQgPSBwYXJlbnRJbnN0YW5jZTtcbiAgICAgIHVwZGF0ZUluc3RhbmNlKGNoaWxkKTtcbiAgICAgIGludmFsaWRhdGVJbnN0YW5jZShjaGlsZCk7XG4gICAgfVxuICB9XG4gIGZ1bmN0aW9uIHJlbW92ZVJlY3Vyc2l2ZShhcnJheSwgcGFyZW50LCBkaXNwb3NlID0gZmFsc2UpIHtcbiAgICBpZiAoYXJyYXkpIFsuLi5hcnJheV0uZm9yRWFjaChjaGlsZCA9PiByZW1vdmVDaGlsZChwYXJlbnQsIGNoaWxkLCBkaXNwb3NlKSk7XG4gIH1cbiAgZnVuY3Rpb24gcmVtb3ZlQ2hpbGQocGFyZW50SW5zdGFuY2UsIGNoaWxkLCBkaXNwb3NlKSB7XG4gICAgaWYgKGNoaWxkKSB7XG4gICAgICB2YXIgX3BhcmVudEluc3RhbmNlJF9fcjNmMywgX2NoaWxkJF9fcjNmMywgX2NoaWxkJF9fcjNmNTtcbiAgICAgIC8vIENsZWFyIHRoZSBwYXJlbnQgcmVmZXJlbmNlXG4gICAgICBpZiAoY2hpbGQuX19yM2YpIGNoaWxkLl9fcjNmLnBhcmVudCA9IG51bGw7XG4gICAgICAvLyBSZW1vdmUgY2hpbGQgZnJvbSB0aGUgcGFyZW50cyBvYmplY3RzXG4gICAgICBpZiAoKF9wYXJlbnRJbnN0YW5jZSRfX3IzZjMgPSBwYXJlbnRJbnN0YW5jZS5fX3IzZikgIT0gbnVsbCAmJiBfcGFyZW50SW5zdGFuY2UkX19yM2YzLm9iamVjdHMpIHBhcmVudEluc3RhbmNlLl9fcjNmLm9iamVjdHMgPSBwYXJlbnRJbnN0YW5jZS5fX3IzZi5vYmplY3RzLmZpbHRlcih4ID0+IHggIT09IGNoaWxkKTtcbiAgICAgIC8vIFJlbW92ZSBhdHRhY2htZW50XG4gICAgICBpZiAoKF9jaGlsZCRfX3IzZjMgPSBjaGlsZC5fX3IzZikgIT0gbnVsbCAmJiBfY2hpbGQkX19yM2YzLmF0dGFjaCkge1xuICAgICAgICBkZXRhY2gocGFyZW50SW5zdGFuY2UsIGNoaWxkLCBjaGlsZC5fX3IzZi5hdHRhY2gpO1xuICAgICAgfSBlbHNlIGlmIChjaGlsZC5pc09iamVjdDNEICYmIHBhcmVudEluc3RhbmNlLmlzT2JqZWN0M0QpIHtcbiAgICAgICAgdmFyIF9jaGlsZCRfX3IzZjQ7XG4gICAgICAgIHBhcmVudEluc3RhbmNlLnJlbW92ZShjaGlsZCk7XG4gICAgICAgIC8vIFJlbW92ZSBpbnRlcmFjdGl2aXR5XG4gICAgICAgIGlmICgoX2NoaWxkJF9fcjNmNCA9IGNoaWxkLl9fcjNmKSAhPSBudWxsICYmIF9jaGlsZCRfX3IzZjQucm9vdCkge1xuICAgICAgICAgIHJlbW92ZUludGVyYWN0aXZpdHkoY2hpbGQuX19yM2Yucm9vdCwgY2hpbGQpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIEFsbG93IG9iamVjdHMgdG8gYmFpbCBvdXQgb2YgcmVjdXJzaXZlIGRpc3Bvc2UgYWx0b2dldGhlciBieSBwYXNzaW5nIGRpc3Bvc2U9e251bGx9XG4gICAgICAvLyBOZXZlciBkaXNwb3NlIG9mIHByaW1pdGl2ZXMgYmVjYXVzZSB0aGVpciBzdGF0ZSBtYXkgYmUga2VwdCBvdXRzaWRlIG9mIFJlYWN0IVxuICAgICAgLy8gSW4gb3JkZXIgZm9yIGFuIG9iamVjdCB0byBiZSBhYmxlIHRvIGRpc3Bvc2UgaXQgaGFzIHRvIGhhdmVcbiAgICAgIC8vICAgLSBhIGRpc3Bvc2UgbWV0aG9kLFxuICAgICAgLy8gICAtIGl0IGNhbm5vdCBiZSBhIDxwcmltaXRpdmUgb2JqZWN0PXsuLi59IC8+XG4gICAgICAvLyAgIC0gaXQgY2Fubm90IGJlIGEgVEhSRUUuU2NlbmUsIGJlY2F1c2UgdGhyZWUgaGFzIGJyb2tlbiBpdCdzIG93biBhcGlcbiAgICAgIC8vXG4gICAgICAvLyBTaW5jZSBkaXNwb3NhbCBpcyByZWN1cnNpdmUsIHdlIGNhbiBjaGVjayB0aGUgb3B0aW9uYWwgZGlzcG9zZSBhcmcsIHdoaWNoIHdpbGwgYmUgdW5kZWZpbmVkXG4gICAgICAvLyB3aGVuIHRoZSByZWNvbmNpbGVyIGNhbGxzIGl0LCBidXQgdGhlbiBjYXJyeSBvdXIgb3duIGNoZWNrIHJlY3Vyc2l2ZWx5XG4gICAgICBjb25zdCBpc1ByaW1pdGl2ZSA9IChfY2hpbGQkX19yM2Y1ID0gY2hpbGQuX19yM2YpID09IG51bGwgPyB2b2lkIDAgOiBfY2hpbGQkX19yM2Y1LnByaW1pdGl2ZTtcbiAgICAgIGNvbnN0IHNob3VsZERpc3Bvc2UgPSBkaXNwb3NlID09PSB1bmRlZmluZWQgPyBjaGlsZC5kaXNwb3NlICE9PSBudWxsICYmICFpc1ByaW1pdGl2ZSA6IGRpc3Bvc2U7XG5cbiAgICAgIC8vIFJlbW92ZSBuZXN0ZWQgY2hpbGQgb2JqZWN0cy4gUHJpbWl0aXZlcyBzaG91bGQgbm90IGhhdmUgb2JqZWN0cyBhbmQgY2hpbGRyZW4gdGhhdCBhcmVcbiAgICAgIC8vIGF0dGFjaGVkIHRvIHRoZW0gZGVjbGFyYXRpdmVseSAuLi5cbiAgICAgIGlmICghaXNQcmltaXRpdmUpIHtcbiAgICAgICAgdmFyIF9jaGlsZCRfX3IzZjY7XG4gICAgICAgIHJlbW92ZVJlY3Vyc2l2ZSgoX2NoaWxkJF9fcjNmNiA9IGNoaWxkLl9fcjNmKSA9PSBudWxsID8gdm9pZCAwIDogX2NoaWxkJF9fcjNmNi5vYmplY3RzLCBjaGlsZCwgc2hvdWxkRGlzcG9zZSk7XG4gICAgICAgIHJlbW92ZVJlY3Vyc2l2ZShjaGlsZC5jaGlsZHJlbiwgY2hpbGQsIHNob3VsZERpc3Bvc2UpO1xuICAgICAgfVxuXG4gICAgICAvLyBSZW1vdmUgcmVmZXJlbmNlc1xuICAgICAgZGVsZXRlIGNoaWxkLl9fcjNmO1xuXG4gICAgICAvLyBEaXNwb3NlIGl0ZW0gd2hlbmV2ZXIgdGhlIHJlY29uY2lsZXIgZmVlbHMgbGlrZSBpdFxuICAgICAgaWYgKHNob3VsZERpc3Bvc2UgJiYgY2hpbGQuZGlzcG9zZSAmJiBjaGlsZC50eXBlICE9PSAnU2NlbmUnKSB7XG4gICAgICAgIHVuc3RhYmxlX3NjaGVkdWxlQ2FsbGJhY2sodW5zdGFibGVfSWRsZVByaW9yaXR5LCAoKSA9PiB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNoaWxkLmRpc3Bvc2UoKTtcbiAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAvKiAuLi4gKi9cbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgICAgaW52YWxpZGF0ZUluc3RhbmNlKHBhcmVudEluc3RhbmNlKTtcbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gc3dpdGNoSW5zdGFuY2UoaW5zdGFuY2UsIHR5cGUsIG5ld1Byb3BzLCBmaWJlcikge1xuICAgIHZhciBfaW5zdGFuY2UkX19yM2Y7XG4gICAgY29uc3QgcGFyZW50ID0gKF9pbnN0YW5jZSRfX3IzZiA9IGluc3RhbmNlLl9fcjNmKSA9PSBudWxsID8gdm9pZCAwIDogX2luc3RhbmNlJF9fcjNmLnBhcmVudDtcbiAgICBpZiAoIXBhcmVudCkgcmV0dXJuO1xuICAgIGNvbnN0IG5ld0luc3RhbmNlID0gY3JlYXRlSW5zdGFuY2UodHlwZSwgbmV3UHJvcHMsIGluc3RhbmNlLl9fcjNmLnJvb3QpO1xuXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3BtbmRycy9yZWFjdC10aHJlZS1maWJlci9pc3N1ZXMvMTM0OFxuICAgIC8vIFdoZW4gYXJncyBjaGFuZ2UgdGhlIGluc3RhbmNlIGhhcyB0byBiZSByZS1jb25zdHJ1Y3RlZCwgd2hpY2ggdGhlblxuICAgIC8vIGZvcmNlcyByM2YgdG8gcmUtcGFyZW50IHRoZSBjaGlsZHJlbiBhbmQgbm9uLXNjZW5lIG9iamVjdHNcbiAgICBpZiAoaW5zdGFuY2UuY2hpbGRyZW4pIHtcbiAgICAgIGZvciAoY29uc3QgY2hpbGQgb2YgaW5zdGFuY2UuY2hpbGRyZW4pIHtcbiAgICAgICAgaWYgKGNoaWxkLl9fcjNmKSBhcHBlbmRDaGlsZChuZXdJbnN0YW5jZSwgY2hpbGQpO1xuICAgICAgfVxuICAgICAgaW5zdGFuY2UuY2hpbGRyZW4gPSBpbnN0YW5jZS5jaGlsZHJlbi5maWx0ZXIoY2hpbGQgPT4gIWNoaWxkLl9fcjNmKTtcbiAgICB9XG4gICAgaW5zdGFuY2UuX19yM2Yub2JqZWN0cy5mb3JFYWNoKGNoaWxkID0+IGFwcGVuZENoaWxkKG5ld0luc3RhbmNlLCBjaGlsZCkpO1xuICAgIGluc3RhbmNlLl9fcjNmLm9iamVjdHMgPSBbXTtcbiAgICBpZiAoIWluc3RhbmNlLl9fcjNmLmF1dG9SZW1vdmVkQmVmb3JlQXBwZW5kKSB7XG4gICAgICByZW1vdmVDaGlsZChwYXJlbnQsIGluc3RhbmNlKTtcbiAgICB9XG4gICAgaWYgKG5ld0luc3RhbmNlLnBhcmVudCkge1xuICAgICAgbmV3SW5zdGFuY2UuX19yM2YuYXV0b1JlbW92ZWRCZWZvcmVBcHBlbmQgPSB0cnVlO1xuICAgIH1cbiAgICBhcHBlbmRDaGlsZChwYXJlbnQsIG5ld0luc3RhbmNlKTtcblxuICAgIC8vIFJlLWJpbmQgZXZlbnQgaGFuZGxlcnNcbiAgICBpZiAobmV3SW5zdGFuY2UucmF5Y2FzdCAmJiBuZXdJbnN0YW5jZS5fX3IzZi5ldmVudENvdW50KSB7XG4gICAgICBjb25zdCByb290U3RhdGUgPSBuZXdJbnN0YW5jZS5fX3IzZi5yb290LmdldFN0YXRlKCk7XG4gICAgICByb290U3RhdGUuaW50ZXJuYWwuaW50ZXJhY3Rpb24ucHVzaChuZXdJbnN0YW5jZSk7XG4gICAgfVxuICAgIFtmaWJlciwgZmliZXIuYWx0ZXJuYXRlXS5mb3JFYWNoKGZpYmVyID0+IHtcbiAgICAgIGlmIChmaWJlciAhPT0gbnVsbCkge1xuICAgICAgICBmaWJlci5zdGF0ZU5vZGUgPSBuZXdJbnN0YW5jZTtcbiAgICAgICAgaWYgKGZpYmVyLnJlZikge1xuICAgICAgICAgIGlmICh0eXBlb2YgZmliZXIucmVmID09PSAnZnVuY3Rpb24nKSBmaWJlci5yZWYobmV3SW5zdGFuY2UpO2Vsc2UgZmliZXIucmVmLmN1cnJlbnQgPSBuZXdJbnN0YW5jZTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pO1xuICB9XG5cbiAgLy8gRG9uJ3QgaGFuZGxlIHRleHQgaW5zdGFuY2VzLCB3YXJuIG9uIHVuZGVmaW5lZCBiZWhhdmlvclxuICBjb25zdCBoYW5kbGVUZXh0SW5zdGFuY2UgPSAoKSA9PiBjb25zb2xlLndhcm4oJ1RleHQgaXMgbm90IGFsbG93ZWQgaW4gdGhlIFIzRiB0cmVlISBUaGlzIGNvdWxkIGJlIHN0cmF5IHdoaXRlc3BhY2Ugb3IgY2hhcmFjdGVycy4nKTtcbiAgY29uc3QgcmVjb25jaWxlciA9IFJlY29uY2lsZXIoe1xuICAgIGNyZWF0ZUluc3RhbmNlLFxuICAgIHJlbW92ZUNoaWxkLFxuICAgIGFwcGVuZENoaWxkLFxuICAgIGFwcGVuZEluaXRpYWxDaGlsZDogYXBwZW5kQ2hpbGQsXG4gICAgaW5zZXJ0QmVmb3JlLFxuICAgIHN1cHBvcnRzTXV0YXRpb246IHRydWUsXG4gICAgaXNQcmltYXJ5UmVuZGVyZXI6IGZhbHNlLFxuICAgIHN1cHBvcnRzUGVyc2lzdGVuY2U6IGZhbHNlLFxuICAgIHN1cHBvcnRzSHlkcmF0aW9uOiBmYWxzZSxcbiAgICBub1RpbWVvdXQ6IC0xLFxuICAgIGFwcGVuZENoaWxkVG9Db250YWluZXI6IChjb250YWluZXIsIGNoaWxkKSA9PiB7XG4gICAgICBpZiAoIWNoaWxkKSByZXR1cm47XG5cbiAgICAgIC8vIERvbid0IGFwcGVuZCB0byB1bm1vdW50ZWQgY29udGFpbmVyXG4gICAgICBjb25zdCBzY2VuZSA9IGNvbnRhaW5lci5nZXRTdGF0ZSgpLnNjZW5lO1xuICAgICAgaWYgKCFzY2VuZS5fX3IzZikgcmV0dXJuO1xuXG4gICAgICAvLyBMaW5rIGN1cnJlbnQgcm9vdCB0byB0aGUgZGVmYXVsdCBzY2VuZVxuICAgICAgc2NlbmUuX19yM2Yucm9vdCA9IGNvbnRhaW5lcjtcbiAgICAgIGFwcGVuZENoaWxkKHNjZW5lLCBjaGlsZCk7XG4gICAgfSxcbiAgICByZW1vdmVDaGlsZEZyb21Db250YWluZXI6IChjb250YWluZXIsIGNoaWxkKSA9PiB7XG4gICAgICBpZiAoIWNoaWxkKSByZXR1cm47XG4gICAgICByZW1vdmVDaGlsZChjb250YWluZXIuZ2V0U3RhdGUoKS5zY2VuZSwgY2hpbGQpO1xuICAgIH0sXG4gICAgaW5zZXJ0SW5Db250YWluZXJCZWZvcmU6IChjb250YWluZXIsIGNoaWxkLCBiZWZvcmVDaGlsZCkgPT4ge1xuICAgICAgaWYgKCFjaGlsZCB8fCAhYmVmb3JlQ2hpbGQpIHJldHVybjtcblxuICAgICAgLy8gRG9uJ3QgYXBwZW5kIHRvIHVubW91bnRlZCBjb250YWluZXJcbiAgICAgIGNvbnN0IHNjZW5lID0gY29udGFpbmVyLmdldFN0YXRlKCkuc2NlbmU7XG4gICAgICBpZiAoIXNjZW5lLl9fcjNmKSByZXR1cm47XG4gICAgICBpbnNlcnRCZWZvcmUoc2NlbmUsIGNoaWxkLCBiZWZvcmVDaGlsZCk7XG4gICAgfSxcbiAgICBnZXRSb290SG9zdENvbnRleHQ6ICgpID0+IG51bGwsXG4gICAgZ2V0Q2hpbGRIb3N0Q29udGV4dDogcGFyZW50SG9zdENvbnRleHQgPT4gcGFyZW50SG9zdENvbnRleHQsXG4gICAgZmluYWxpemVJbml0aWFsQ2hpbGRyZW4oaW5zdGFuY2UpIHtcbiAgICAgIHZhciBfaW5zdGFuY2UkX19yM2YyO1xuICAgICAgY29uc3QgbG9jYWxTdGF0ZSA9IChfaW5zdGFuY2UkX19yM2YyID0gaW5zdGFuY2UgPT0gbnVsbCA/IHZvaWQgMCA6IGluc3RhbmNlLl9fcjNmKSAhPSBudWxsID8gX2luc3RhbmNlJF9fcjNmMiA6IHt9O1xuICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2lzc3Vlcy8yMDI3MVxuICAgICAgLy8gUmV0dXJuaW5nIHRydWUgd2lsbCB0cmlnZ2VyIGNvbW1pdE1vdW50XG4gICAgICByZXR1cm4gQm9vbGVhbihsb2NhbFN0YXRlLmhhbmRsZXJzKTtcbiAgICB9LFxuICAgIHByZXBhcmVVcGRhdGUoaW5zdGFuY2UsIF90eXBlLCBvbGRQcm9wcywgbmV3UHJvcHMpIHtcbiAgICAgIHZhciBfaW5zdGFuY2UkX19yM2YzO1xuICAgICAgY29uc3QgbG9jYWxTdGF0ZSA9IChfaW5zdGFuY2UkX19yM2YzID0gaW5zdGFuY2UgPT0gbnVsbCA/IHZvaWQgMCA6IGluc3RhbmNlLl9fcjNmKSAhPSBudWxsID8gX2luc3RhbmNlJF9fcjNmMyA6IHt9O1xuXG4gICAgICAvLyBDcmVhdGUgZGlmZi1zZXRzXG4gICAgICBpZiAobG9jYWxTdGF0ZS5wcmltaXRpdmUgJiYgbmV3UHJvcHMub2JqZWN0ICYmIG5ld1Byb3BzLm9iamVjdCAhPT0gaW5zdGFuY2UpIHtcbiAgICAgICAgcmV0dXJuIFt0cnVlXTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIFRoaXMgaXMgYSBkYXRhIG9iamVjdCwgbGV0J3MgZXh0cmFjdCBjcml0aWNhbCBpbmZvcm1hdGlvbiBhYm91dCBpdFxuICAgICAgICBjb25zdCB7XG4gICAgICAgICAgYXJnczogYXJnc05ldyA9IFtdLFxuICAgICAgICAgIGNoaWxkcmVuOiBjTixcbiAgICAgICAgICAuLi5yZXN0TmV3XG4gICAgICAgIH0gPSBuZXdQcm9wcztcbiAgICAgICAgY29uc3Qge1xuICAgICAgICAgIGFyZ3M6IGFyZ3NPbGQgPSBbXSxcbiAgICAgICAgICBjaGlsZHJlbjogY08sXG4gICAgICAgICAgLi4ucmVzdE9sZFxuICAgICAgICB9ID0gb2xkUHJvcHM7XG5cbiAgICAgICAgLy8gVGhyb3cgaWYgYW4gb2JqZWN0IG9yIGxpdGVyYWwgd2FzIHBhc3NlZCBmb3IgYXJnc1xuICAgICAgICBpZiAoIUFycmF5LmlzQXJyYXkoYXJnc05ldykpIHRocm93IG5ldyBFcnJvcignUjNGOiB0aGUgYXJncyBwcm9wIG11c3QgYmUgYW4gYXJyYXkhJyk7XG5cbiAgICAgICAgLy8gSWYgaXQgaGFzIG5ldyBwcm9wcyBvciBhcmd1bWVudHMsIHRoZW4gaXQgbmVlZHMgdG8gYmUgcmUtaW5zdGFudGlhdGVkXG4gICAgICAgIGlmIChhcmdzTmV3LnNvbWUoKHZhbHVlLCBpbmRleCkgPT4gdmFsdWUgIT09IGFyZ3NPbGRbaW5kZXhdKSkgcmV0dXJuIFt0cnVlXTtcbiAgICAgICAgLy8gQ3JlYXRlIGEgZGlmZi1zZXQsIGZsYWcgaWYgdGhlcmUgYXJlIGFueSBjaGFuZ2VzXG4gICAgICAgIGNvbnN0IGRpZmYgPSBkaWZmUHJvcHMoaW5zdGFuY2UsIHJlc3ROZXcsIHJlc3RPbGQsIHRydWUpO1xuICAgICAgICBpZiAoZGlmZi5jaGFuZ2VzLmxlbmd0aCkgcmV0dXJuIFtmYWxzZSwgZGlmZl07XG5cbiAgICAgICAgLy8gT3RoZXJ3aXNlIGRvIG5vdCB0b3VjaCB0aGUgaW5zdGFuY2VcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgfSxcbiAgICBjb21taXRVcGRhdGUoaW5zdGFuY2UsIFtyZWNvbnN0cnVjdCwgZGlmZl0sIHR5cGUsIF9vbGRQcm9wcywgbmV3UHJvcHMsIGZpYmVyKSB7XG4gICAgICAvLyBSZWNvbnN0cnVjdCB3aGVuIGFyZ3Mgb3IgPHByaW1pdGl2ZSBvYmplY3Q9ey4uLn0gaGF2ZSBjaGFuZ2VzXG4gICAgICBpZiAocmVjb25zdHJ1Y3QpIHN3aXRjaEluc3RhbmNlKGluc3RhbmNlLCB0eXBlLCBuZXdQcm9wcywgZmliZXIpO1xuICAgICAgLy8gT3RoZXJ3aXNlIGp1c3Qgb3ZlcndyaXRlIHByb3BzXG4gICAgICBlbHNlIGFwcGx5UHJvcHMkMShpbnN0YW5jZSwgZGlmZik7XG4gICAgfSxcbiAgICBjb21taXRNb3VudChpbnN0YW5jZSwgX3R5cGUsIF9wcm9wcywgX2ludCkge1xuICAgICAgdmFyIF9pbnN0YW5jZSRfX3IzZjQ7XG4gICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVhY3QvaXNzdWVzLzIwMjcxXG4gICAgICAvLyBUaGlzIHdpbGwgbWFrZSBzdXJlIGV2ZW50cyBhcmUgb25seSBhZGRlZCBvbmNlIHRvIHRoZSBjZW50cmFsIGNvbnRhaW5lclxuICAgICAgY29uc3QgbG9jYWxTdGF0ZSA9IChfaW5zdGFuY2UkX19yM2Y0ID0gaW5zdGFuY2UuX19yM2YpICE9IG51bGwgPyBfaW5zdGFuY2UkX19yM2Y0IDoge307XG4gICAgICBpZiAoaW5zdGFuY2UucmF5Y2FzdCAmJiBsb2NhbFN0YXRlLmhhbmRsZXJzICYmIGxvY2FsU3RhdGUuZXZlbnRDb3VudCkge1xuICAgICAgICBpbnN0YW5jZS5fX3IzZi5yb290LmdldFN0YXRlKCkuaW50ZXJuYWwuaW50ZXJhY3Rpb24ucHVzaChpbnN0YW5jZSk7XG4gICAgICB9XG4gICAgfSxcbiAgICBnZXRQdWJsaWNJbnN0YW5jZTogaW5zdGFuY2UgPT4gaW5zdGFuY2UsXG4gICAgcHJlcGFyZUZvckNvbW1pdDogKCkgPT4gbnVsbCxcbiAgICBwcmVwYXJlUG9ydGFsTW91bnQ6IGNvbnRhaW5lciA9PiBwcmVwYXJlKGNvbnRhaW5lci5nZXRTdGF0ZSgpLnNjZW5lKSxcbiAgICByZXNldEFmdGVyQ29tbWl0OiAoKSA9PiB7fSxcbiAgICBzaG91bGRTZXRUZXh0Q29udGVudDogKCkgPT4gZmFsc2UsXG4gICAgY2xlYXJDb250YWluZXI6ICgpID0+IGZhbHNlLFxuICAgIGhpZGVJbnN0YW5jZShpbnN0YW5jZSkge1xuICAgICAgdmFyIF9pbnN0YW5jZSRfX3IzZjU7XG4gICAgICAvLyBEZXRhY2ggd2hpbGUgdGhlIGluc3RhbmNlIGlzIGhpZGRlblxuICAgICAgY29uc3Qge1xuICAgICAgICBhdHRhY2g6IHR5cGUsXG4gICAgICAgIHBhcmVudFxuICAgICAgfSA9IChfaW5zdGFuY2UkX19yM2Y1ID0gaW5zdGFuY2UuX19yM2YpICE9IG51bGwgPyBfaW5zdGFuY2UkX19yM2Y1IDoge307XG4gICAgICBpZiAodHlwZSAmJiBwYXJlbnQpIGRldGFjaChwYXJlbnQsIGluc3RhbmNlLCB0eXBlKTtcbiAgICAgIGlmIChpbnN0YW5jZS5pc09iamVjdDNEKSBpbnN0YW5jZS52aXNpYmxlID0gZmFsc2U7XG4gICAgICBpbnZhbGlkYXRlSW5zdGFuY2UoaW5zdGFuY2UpO1xuICAgIH0sXG4gICAgdW5oaWRlSW5zdGFuY2UoaW5zdGFuY2UsIHByb3BzKSB7XG4gICAgICB2YXIgX2luc3RhbmNlJF9fcjNmNjtcbiAgICAgIC8vIFJlLWF0dGFjaCB3aGVuIHRoZSBpbnN0YW5jZSBpcyB1bmhpZGRlblxuICAgICAgY29uc3Qge1xuICAgICAgICBhdHRhY2g6IHR5cGUsXG4gICAgICAgIHBhcmVudFxuICAgICAgfSA9IChfaW5zdGFuY2UkX19yM2Y2ID0gaW5zdGFuY2UuX19yM2YpICE9IG51bGwgPyBfaW5zdGFuY2UkX19yM2Y2IDoge307XG4gICAgICBpZiAodHlwZSAmJiBwYXJlbnQpIGF0dGFjaChwYXJlbnQsIGluc3RhbmNlLCB0eXBlKTtcbiAgICAgIGlmIChpbnN0YW5jZS5pc09iamVjdDNEICYmIHByb3BzLnZpc2libGUgPT0gbnVsbCB8fCBwcm9wcy52aXNpYmxlKSBpbnN0YW5jZS52aXNpYmxlID0gdHJ1ZTtcbiAgICAgIGludmFsaWRhdGVJbnN0YW5jZShpbnN0YW5jZSk7XG4gICAgfSxcbiAgICBjcmVhdGVUZXh0SW5zdGFuY2U6IGhhbmRsZVRleHRJbnN0YW5jZSxcbiAgICBoaWRlVGV4dEluc3RhbmNlOiBoYW5kbGVUZXh0SW5zdGFuY2UsXG4gICAgdW5oaWRlVGV4dEluc3RhbmNlOiBoYW5kbGVUZXh0SW5zdGFuY2UsXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3BtbmRycy9yZWFjdC10aHJlZS1maWJlci9wdWxsLzIzNjAjZGlzY3Vzc2lvbl9yOTE2MzU2ODc0XG4gICAgLy8gQHRzLWlnbm9yZVxuICAgIGdldEN1cnJlbnRFdmVudFByaW9yaXR5OiAoKSA9PiBfZ2V0RXZlbnRQcmlvcml0eSA/IF9nZXRFdmVudFByaW9yaXR5KCkgOiBEZWZhdWx0RXZlbnRQcmlvcml0eSxcbiAgICBiZWZvcmVBY3RpdmVJbnN0YW5jZUJsdXI6ICgpID0+IHt9LFxuICAgIGFmdGVyQWN0aXZlSW5zdGFuY2VCbHVyOiAoKSA9PiB7fSxcbiAgICBkZXRhY2hEZWxldGVkSW5zdGFuY2U6ICgpID0+IHt9LFxuICAgIG5vdzogdHlwZW9mIHBlcmZvcm1hbmNlICE9PSAndW5kZWZpbmVkJyAmJiBpcy5mdW4ocGVyZm9ybWFuY2Uubm93KSA/IHBlcmZvcm1hbmNlLm5vdyA6IGlzLmZ1bihEYXRlLm5vdykgPyBEYXRlLm5vdyA6ICgpID0+IDAsXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3BtbmRycy9yZWFjdC10aHJlZS1maWJlci9wdWxsLzIzNjAjZGlzY3Vzc2lvbl9yOTIwODgzNTAzXG4gICAgc2NoZWR1bGVUaW1lb3V0OiBpcy5mdW4oc2V0VGltZW91dCkgPyBzZXRUaW1lb3V0IDogdW5kZWZpbmVkLFxuICAgIGNhbmNlbFRpbWVvdXQ6IGlzLmZ1bihjbGVhclRpbWVvdXQpID8gY2xlYXJUaW1lb3V0IDogdW5kZWZpbmVkXG4gIH0pO1xuICByZXR1cm4ge1xuICAgIHJlY29uY2lsZXIsXG4gICAgYXBwbHlQcm9wczogYXBwbHlQcm9wcyQxXG4gIH07XG59XG5cbnZhciBfd2luZG93JGRvY3VtZW50LCBfd2luZG93JG5hdmlnYXRvcjtcbi8qKlxyXG4gKiBSZXR1cm5zIGB0cnVlYCB3aXRoIGNvcnJlY3QgVFMgdHlwZSBpbmZlcmVuY2UgaWYgYW4gb2JqZWN0IGhhcyBhIGNvbmZpZ3VyYWJsZSBjb2xvciBzcGFjZSAoc2luY2UgcjE1MikuXHJcbiAqL1xuY29uc3QgaGFzQ29sb3JTcGFjZSA9IG9iamVjdCA9PiAnY29sb3JTcGFjZScgaW4gb2JqZWN0IHx8ICdvdXRwdXRDb2xvclNwYWNlJyBpbiBvYmplY3Q7XG4vKipcclxuICogVGhlIGN1cnJlbnQgVEhSRUUuQ29sb3JNYW5hZ2VtZW50IGluc3RhbmNlLCBpZiBwcmVzZW50LlxyXG4gKi9cbmNvbnN0IGdldENvbG9yTWFuYWdlbWVudCA9ICgpID0+IHtcbiAgdmFyIF9Db2xvck1hbmFnZW1lbnQ7XG4gIHJldHVybiAoX0NvbG9yTWFuYWdlbWVudCA9IGNhdGFsb2d1ZS5Db2xvck1hbmFnZW1lbnQpICE9IG51bGwgPyBfQ29sb3JNYW5hZ2VtZW50IDogbnVsbDtcbn07XG5jb25zdCBpc09ydGhvZ3JhcGhpY0NhbWVyYSA9IGRlZiA9PiBkZWYgJiYgZGVmLmlzT3J0aG9ncmFwaGljQ2FtZXJhO1xuY29uc3QgaXNSZWYgPSBvYmogPT4gb2JqICYmIG9iai5oYXNPd25Qcm9wZXJ0eSgnY3VycmVudCcpO1xuXG4vKipcclxuICogQW4gU1NSLWZyaWVuZGx5IHVzZUxheW91dEVmZmVjdC5cclxuICpcclxuICogUmVhY3QgY3VycmVudGx5IHRocm93cyBhIHdhcm5pbmcgd2hlbiB1c2luZyB1c2VMYXlvdXRFZmZlY3Qgb24gdGhlIHNlcnZlci5cclxuICogVG8gZ2V0IGFyb3VuZCBpdCwgd2UgY2FuIGNvbmRpdGlvbmFsbHkgdXNlRWZmZWN0IG9uIHRoZSBzZXJ2ZXIgKG5vLW9wKSBhbmRcclxuICogdXNlTGF5b3V0RWZmZWN0IGVsc2V3aGVyZS5cclxuICpcclxuICogQHNlZSBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVhY3QvaXNzdWVzLzE0OTI3XHJcbiAqL1xuY29uc3QgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICgoX3dpbmRvdyRkb2N1bWVudCA9IHdpbmRvdy5kb2N1bWVudCkgIT0gbnVsbCAmJiBfd2luZG93JGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQgfHwgKChfd2luZG93JG5hdmlnYXRvciA9IHdpbmRvdy5uYXZpZ2F0b3IpID09IG51bGwgPyB2b2lkIDAgOiBfd2luZG93JG5hdmlnYXRvci5wcm9kdWN0KSA9PT0gJ1JlYWN0TmF0aXZlJykgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiBSZWFjdC51c2VFZmZlY3Q7XG5mdW5jdGlvbiB1c2VNdXRhYmxlQ2FsbGJhY2soZm4pIHtcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKGZuKTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB2b2lkIChyZWYuY3VycmVudCA9IGZuKSwgW2ZuXSk7XG4gIHJldHVybiByZWY7XG59XG5mdW5jdGlvbiBCbG9jayh7XG4gIHNldFxufSkge1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBzZXQobmV3IFByb21pc2UoKCkgPT4gbnVsbCkpO1xuICAgIHJldHVybiAoKSA9PiBzZXQoZmFsc2UpO1xuICB9LCBbc2V0XSk7XG4gIHJldHVybiBudWxsO1xufVxuY2xhc3MgRXJyb3JCb3VuZGFyeSBleHRlbmRzIFJlYWN0LkNvbXBvbmVudCB7XG4gIGNvbnN0cnVjdG9yKC4uLmFyZ3MpIHtcbiAgICBzdXBlciguLi5hcmdzKTtcbiAgICB0aGlzLnN0YXRlID0ge1xuICAgICAgZXJyb3I6IGZhbHNlXG4gICAgfTtcbiAgfVxuICBjb21wb25lbnREaWRDYXRjaChlcnIpIHtcbiAgICB0aGlzLnByb3BzLnNldChlcnIpO1xuICB9XG4gIHJlbmRlcigpIHtcbiAgICByZXR1cm4gdGhpcy5zdGF0ZS5lcnJvciA/IG51bGwgOiB0aGlzLnByb3BzLmNoaWxkcmVuO1xuICB9XG59XG5FcnJvckJvdW5kYXJ5LmdldERlcml2ZWRTdGF0ZUZyb21FcnJvciA9ICgpID0+ICh7XG4gIGVycm9yOiB0cnVlXG59KTtcbmNvbnN0IERFRkFVTFQgPSAnX19kZWZhdWx0JztcbmNvbnN0IERFRkFVTFRTID0gbmV3IE1hcCgpO1xuY29uc3QgaXNEaWZmU2V0ID0gZGVmID0+IGRlZiAmJiAhIWRlZi5tZW1vaXplZCAmJiAhIWRlZi5jaGFuZ2VzO1xuZnVuY3Rpb24gY2FsY3VsYXRlRHByKGRwcikge1xuICB2YXIgX3dpbmRvdyRkZXZpY2VQaXhlbFJhO1xuICAvLyBFcnIgb24gdGhlIHNpZGUgb2YgcHJvZ3Jlc3MgYnkgYXNzdW1pbmcgMnggZHByIGlmIHdlIGNhbid0IGRldGVjdCBpdFxuICAvLyBUaGlzIHdpbGwgaGFwcGVuIGluIHdvcmtlcnMgd2hlcmUgd2luZG93IGlzIGRlZmluZWQgYnV0IGRwciBpc24ndC5cbiAgY29uc3QgdGFyZ2V0ID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyAoX3dpbmRvdyRkZXZpY2VQaXhlbFJhID0gd2luZG93LmRldmljZVBpeGVsUmF0aW8pICE9IG51bGwgPyBfd2luZG93JGRldmljZVBpeGVsUmEgOiAyIDogMTtcbiAgcmV0dXJuIEFycmF5LmlzQXJyYXkoZHByKSA/IE1hdGgubWluKE1hdGgubWF4KGRwclswXSwgdGFyZ2V0KSwgZHByWzFdKSA6IGRwcjtcbn1cblxuLyoqXHJcbiAqIFJldHVybnMgaW5zdGFuY2Ugcm9vdCBzdGF0ZVxyXG4gKi9cbmNvbnN0IGdldFJvb3RTdGF0ZSA9IG9iaiA9PiB7XG4gIHZhciBfcjNmO1xuICByZXR1cm4gKF9yM2YgPSBvYmouX19yM2YpID09IG51bGwgPyB2b2lkIDAgOiBfcjNmLnJvb3QuZ2V0U3RhdGUoKTtcbn07XG4vLyBBIGNvbGxlY3Rpb24gb2YgY29tcGFyZSBmdW5jdGlvbnNcbmNvbnN0IGlzID0ge1xuICBvYmo6IGEgPT4gYSA9PT0gT2JqZWN0KGEpICYmICFpcy5hcnIoYSkgJiYgdHlwZW9mIGEgIT09ICdmdW5jdGlvbicsXG4gIGZ1bjogYSA9PiB0eXBlb2YgYSA9PT0gJ2Z1bmN0aW9uJyxcbiAgc3RyOiBhID0+IHR5cGVvZiBhID09PSAnc3RyaW5nJyxcbiAgbnVtOiBhID0+IHR5cGVvZiBhID09PSAnbnVtYmVyJyxcbiAgYm9vOiBhID0+IHR5cGVvZiBhID09PSAnYm9vbGVhbicsXG4gIHVuZDogYSA9PiBhID09PSB2b2lkIDAsXG4gIGFycjogYSA9PiBBcnJheS5pc0FycmF5KGEpLFxuICBlcXUoYSwgYiwge1xuICAgIGFycmF5cyA9ICdzaGFsbG93JyxcbiAgICBvYmplY3RzID0gJ3JlZmVyZW5jZScsXG4gICAgc3RyaWN0ID0gdHJ1ZVxuICB9ID0ge30pIHtcbiAgICAvLyBXcm9uZyB0eXBlIG9yIG9uZSBvZiB0aGUgdHdvIHVuZGVmaW5lZCwgZG9lc24ndCBtYXRjaFxuICAgIGlmICh0eXBlb2YgYSAhPT0gdHlwZW9mIGIgfHwgISFhICE9PSAhIWIpIHJldHVybiBmYWxzZTtcbiAgICAvLyBBdG9taWMsIGp1c3QgY29tcGFyZSBhIGFnYWluc3QgYlxuICAgIGlmIChpcy5zdHIoYSkgfHwgaXMubnVtKGEpKSByZXR1cm4gYSA9PT0gYjtcbiAgICBjb25zdCBpc09iaiA9IGlzLm9iaihhKTtcbiAgICBpZiAoaXNPYmogJiYgb2JqZWN0cyA9PT0gJ3JlZmVyZW5jZScpIHJldHVybiBhID09PSBiO1xuICAgIGNvbnN0IGlzQXJyID0gaXMuYXJyKGEpO1xuICAgIGlmIChpc0FyciAmJiBhcnJheXMgPT09ICdyZWZlcmVuY2UnKSByZXR1cm4gYSA9PT0gYjtcbiAgICAvLyBBcnJheSBvciBPYmplY3QsIHNoYWxsb3cgY29tcGFyZSBmaXJzdCB0byBzZWUgaWYgaXQncyBhIG1hdGNoXG4gICAgaWYgKChpc0FyciB8fCBpc09iaikgJiYgYSA9PT0gYikgcmV0dXJuIHRydWU7XG4gICAgLy8gTGFzdCByZXNvcnQsIGdvIHRocm91Z2gga2V5c1xuICAgIGxldCBpO1xuICAgIC8vIENoZWNrIGlmIGEgaGFzIGFsbCB0aGUga2V5cyBvZiBiXG4gICAgZm9yIChpIGluIGEpIGlmICghKGkgaW4gYikpIHJldHVybiBmYWxzZTtcbiAgICAvLyBDaGVjayBpZiB2YWx1ZXMgYmV0d2VlbiBrZXlzIG1hdGNoXG4gICAgaWYgKGlzT2JqICYmIGFycmF5cyA9PT0gJ3NoYWxsb3cnICYmIG9iamVjdHMgPT09ICdzaGFsbG93Jykge1xuICAgICAgZm9yIChpIGluIHN0cmljdCA/IGIgOiBhKSBpZiAoIWlzLmVxdShhW2ldLCBiW2ldLCB7XG4gICAgICAgIHN0cmljdCxcbiAgICAgICAgb2JqZWN0czogJ3JlZmVyZW5jZSdcbiAgICAgIH0pKSByZXR1cm4gZmFsc2U7XG4gICAgfSBlbHNlIHtcbiAgICAgIGZvciAoaSBpbiBzdHJpY3QgPyBiIDogYSkgaWYgKGFbaV0gIT09IGJbaV0pIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgLy8gSWYgaSBpcyB1bmRlZmluZWRcbiAgICBpZiAoaXMudW5kKGkpKSB7XG4gICAgICAvLyBJZiBib3RoIGFycmF5cyBhcmUgZW1wdHkgd2UgY29uc2lkZXIgdGhlbSBlcXVhbFxuICAgICAgaWYgKGlzQXJyICYmIGEubGVuZ3RoID09PSAwICYmIGIubGVuZ3RoID09PSAwKSByZXR1cm4gdHJ1ZTtcbiAgICAgIC8vIElmIGJvdGggb2JqZWN0cyBhcmUgZW1wdHkgd2UgY29uc2lkZXIgdGhlbSBlcXVhbFxuICAgICAgaWYgKGlzT2JqICYmIE9iamVjdC5rZXlzKGEpLmxlbmd0aCA9PT0gMCAmJiBPYmplY3Qua2V5cyhiKS5sZW5ndGggPT09IDApIHJldHVybiB0cnVlO1xuICAgICAgLy8gT3RoZXJ3aXNlIG1hdGNoIHRoZW0gYnkgdmFsdWVcbiAgICAgIGlmIChhICE9PSBiKSByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9XG59O1xuXG4vKipcclxuICogQ29sbGVjdHMgbm9kZXMgYW5kIG1hdGVyaWFscyBmcm9tIGEgVEhSRUUuT2JqZWN0M0QuXHJcbiAqL1xuZnVuY3Rpb24gYnVpbGRHcmFwaChvYmplY3QpIHtcbiAgY29uc3QgZGF0YSA9IHtcbiAgICBub2Rlczoge30sXG4gICAgbWF0ZXJpYWxzOiB7fVxuICB9O1xuICBpZiAob2JqZWN0KSB7XG4gICAgb2JqZWN0LnRyYXZlcnNlKG9iaiA9PiB7XG4gICAgICBpZiAob2JqLm5hbWUpIGRhdGEubm9kZXNbb2JqLm5hbWVdID0gb2JqO1xuICAgICAgaWYgKG9iai5tYXRlcmlhbCAmJiAhZGF0YS5tYXRlcmlhbHNbb2JqLm1hdGVyaWFsLm5hbWVdKSBkYXRhLm1hdGVyaWFsc1tvYmoubWF0ZXJpYWwubmFtZV0gPSBvYmoubWF0ZXJpYWw7XG4gICAgfSk7XG4gIH1cbiAgcmV0dXJuIGRhdGE7XG59XG5cbi8vIERpc3Bvc2VzIGFuIG9iamVjdCBhbmQgYWxsIGl0cyBwcm9wZXJ0aWVzXG5mdW5jdGlvbiBkaXNwb3NlKG9iaikge1xuICBpZiAob2JqLmRpc3Bvc2UgJiYgb2JqLnR5cGUgIT09ICdTY2VuZScpIG9iai5kaXNwb3NlKCk7XG4gIGZvciAoY29uc3QgcCBpbiBvYmopIHtcbiAgICBwLmRpc3Bvc2UgPT0gbnVsbCA/IHZvaWQgMCA6IHAuZGlzcG9zZSgpO1xuICAgIGRlbGV0ZSBvYmpbcF07XG4gIH1cbn1cblxuLy8gRWFjaCBvYmplY3QgaW4gdGhlIHNjZW5lIGNhcnJpZXMgYSBzbWFsbCBMb2NhbFN0YXRlIGRlc2NyaXB0b3JcbmZ1bmN0aW9uIHByZXBhcmUob2JqZWN0LCBzdGF0ZSkge1xuICBjb25zdCBpbnN0YW5jZSA9IG9iamVjdDtcbiAgaW5zdGFuY2UuX19yM2YgPSB7XG4gICAgdHlwZTogJycsXG4gICAgcm9vdDogbnVsbCxcbiAgICBwcmV2aW91c0F0dGFjaDogbnVsbCxcbiAgICBtZW1vaXplZFByb3BzOiB7fSxcbiAgICBldmVudENvdW50OiAwLFxuICAgIGhhbmRsZXJzOiB7fSxcbiAgICBvYmplY3RzOiBbXSxcbiAgICBwYXJlbnQ6IG51bGwsXG4gICAgLi4uc3RhdGVcbiAgfTtcbiAgcmV0dXJuIG9iamVjdDtcbn1cbmZ1bmN0aW9uIHJlc29sdmUoaW5zdGFuY2UsIGtleSkge1xuICBsZXQgdGFyZ2V0ID0gaW5zdGFuY2U7XG4gIGlmIChrZXkuaW5jbHVkZXMoJy0nKSkge1xuICAgIGNvbnN0IGVudHJpZXMgPSBrZXkuc3BsaXQoJy0nKTtcbiAgICBjb25zdCBsYXN0ID0gZW50cmllcy5wb3AoKTtcbiAgICB0YXJnZXQgPSBlbnRyaWVzLnJlZHVjZSgoYWNjLCBrZXkpID0+IGFjY1trZXldLCBpbnN0YW5jZSk7XG4gICAgcmV0dXJuIHtcbiAgICAgIHRhcmdldCxcbiAgICAgIGtleTogbGFzdFxuICAgIH07XG4gIH0gZWxzZSByZXR1cm4ge1xuICAgIHRhcmdldCxcbiAgICBrZXlcbiAgfTtcbn1cblxuLy8gQ2hlY2tzIGlmIGEgZGFzaC1jYXNlZCBzdHJpbmcgZW5kcyB3aXRoIGFuIGludGVnZXJcbmNvbnN0IElOREVYX1JFR0VYID0gLy1cXGQrJC87XG5mdW5jdGlvbiBhdHRhY2gocGFyZW50LCBjaGlsZCwgdHlwZSkge1xuICBpZiAoaXMuc3RyKHR5cGUpKSB7XG4gICAgLy8gSWYgYXR0YWNoaW5nIGludG8gYW4gYXJyYXkgKGZvby0wKSwgY3JlYXRlIG9uZVxuICAgIGlmIChJTkRFWF9SRUdFWC50ZXN0KHR5cGUpKSB7XG4gICAgICBjb25zdCByb290ID0gdHlwZS5yZXBsYWNlKElOREVYX1JFR0VYLCAnJyk7XG4gICAgICBjb25zdCB7XG4gICAgICAgIHRhcmdldCxcbiAgICAgICAga2V5XG4gICAgICB9ID0gcmVzb2x2ZShwYXJlbnQsIHJvb3QpO1xuICAgICAgaWYgKCFBcnJheS5pc0FycmF5KHRhcmdldFtrZXldKSkgdGFyZ2V0W2tleV0gPSBbXTtcbiAgICB9XG4gICAgY29uc3Qge1xuICAgICAgdGFyZ2V0LFxuICAgICAga2V5XG4gICAgfSA9IHJlc29sdmUocGFyZW50LCB0eXBlKTtcbiAgICBjaGlsZC5fX3IzZi5wcmV2aW91c0F0dGFjaCA9IHRhcmdldFtrZXldO1xuICAgIHRhcmdldFtrZXldID0gY2hpbGQ7XG4gIH0gZWxzZSBjaGlsZC5fX3IzZi5wcmV2aW91c0F0dGFjaCA9IHR5cGUocGFyZW50LCBjaGlsZCk7XG59XG5mdW5jdGlvbiBkZXRhY2gocGFyZW50LCBjaGlsZCwgdHlwZSkge1xuICB2YXIgX2NoaWxkJF9fcjNmLCBfY2hpbGQkX19yM2YyO1xuICBpZiAoaXMuc3RyKHR5cGUpKSB7XG4gICAgY29uc3Qge1xuICAgICAgdGFyZ2V0LFxuICAgICAga2V5XG4gICAgfSA9IHJlc29sdmUocGFyZW50LCB0eXBlKTtcbiAgICBjb25zdCBwcmV2aW91cyA9IGNoaWxkLl9fcjNmLnByZXZpb3VzQXR0YWNoO1xuICAgIC8vIFdoZW4gdGhlIHByZXZpb3VzIHZhbHVlIHdhcyB1bmRlZmluZWQsIGl0IG1lYW5zIHRoZSB2YWx1ZSB3YXMgbmV2ZXIgc2V0IHRvIGJlZ2luIHdpdGhcbiAgICBpZiAocHJldmlvdXMgPT09IHVuZGVmaW5lZCkgZGVsZXRlIHRhcmdldFtrZXldO1xuICAgIC8vIE90aGVyd2lzZSBzZXQgdGhlIHByZXZpb3VzIHZhbHVlXG4gICAgZWxzZSB0YXJnZXRba2V5XSA9IHByZXZpb3VzO1xuICB9IGVsc2UgKF9jaGlsZCRfX3IzZiA9IGNoaWxkLl9fcjNmKSA9PSBudWxsID8gdm9pZCAwIDogX2NoaWxkJF9fcjNmLnByZXZpb3VzQXR0YWNoID09IG51bGwgPyB2b2lkIDAgOiBfY2hpbGQkX19yM2YucHJldmlvdXNBdHRhY2gocGFyZW50LCBjaGlsZCk7XG4gIChfY2hpbGQkX19yM2YyID0gY2hpbGQuX19yM2YpID09IG51bGwgPyB0cnVlIDogZGVsZXRlIF9jaGlsZCRfX3IzZjIucHJldmlvdXNBdHRhY2g7XG59XG5cbi8vIFRoaXMgZnVuY3Rpb24gcHJlcGFyZXMgYSBzZXQgb2YgY2hhbmdlcyB0byBiZSBhcHBsaWVkIHRvIHRoZSBpbnN0YW5jZVxuZnVuY3Rpb24gZGlmZlByb3BzKGluc3RhbmNlLCB7XG4gIGNoaWxkcmVuOiBjTixcbiAga2V5OiBrTixcbiAgcmVmOiByTixcbiAgLi4ucHJvcHNcbn0sIHtcbiAgY2hpbGRyZW46IGNQLFxuICBrZXk6IGtQLFxuICByZWY6IHJQLFxuICAuLi5wcmV2aW91c1xufSA9IHt9LCByZW1vdmUgPSBmYWxzZSkge1xuICB2YXIgX2luc3RhbmNlJF9fcjNmO1xuICBjb25zdCBsb2NhbFN0YXRlID0gKF9pbnN0YW5jZSRfX3IzZiA9IGluc3RhbmNlID09IG51bGwgPyB2b2lkIDAgOiBpbnN0YW5jZS5fX3IzZikgIT0gbnVsbCA/IF9pbnN0YW5jZSRfX3IzZiA6IHt9O1xuICBjb25zdCBlbnRyaWVzID0gT2JqZWN0LmVudHJpZXMocHJvcHMpO1xuICBjb25zdCBjaGFuZ2VzID0gW107XG5cbiAgLy8gQ2F0Y2ggcmVtb3ZlZCBwcm9wcywgcHJlcGVuZCB0aGVtIHNvIHRoZXkgY2FuIGJlIHJlc2V0IG9yIHJlbW92ZWRcbiAgaWYgKHJlbW92ZSkge1xuICAgIGNvbnN0IHByZXZpb3VzS2V5cyA9IE9iamVjdC5rZXlzKHByZXZpb3VzKTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHByZXZpb3VzS2V5cy5sZW5ndGg7IGkrKykge1xuICAgICAgaWYgKCFwcm9wcy5oYXNPd25Qcm9wZXJ0eShwcmV2aW91c0tleXNbaV0pKSBlbnRyaWVzLnVuc2hpZnQoW3ByZXZpb3VzS2V5c1tpXSwgREVGQVVMVCArICdyZW1vdmUnXSk7XG4gICAgfVxuICB9XG4gIGVudHJpZXMuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7XG4gICAgdmFyIF9pbnN0YW5jZSRfX3IzZjI7XG4gICAgLy8gQmFpbCBvdXQgb24gcHJpbWl0aXZlIG9iamVjdFxuICAgIGlmICgoX2luc3RhbmNlJF9fcjNmMiA9IGluc3RhbmNlLl9fcjNmKSAhPSBudWxsICYmIF9pbnN0YW5jZSRfX3IzZjIucHJpbWl0aXZlICYmIGtleSA9PT0gJ29iamVjdCcpIHJldHVybjtcbiAgICAvLyBXaGVuIHByb3BzIG1hdGNoIGJhaWwgb3V0XG4gICAgaWYgKGlzLmVxdSh2YWx1ZSwgcHJldmlvdXNba2V5XSkpIHJldHVybjtcbiAgICAvLyBDb2xsZWN0IGhhbmRsZXJzIGFuZCBiYWlsIG91dFxuICAgIGlmICgvXm9uKFBvaW50ZXJ8Q2xpY2t8RG91YmxlQ2xpY2t8Q29udGV4dE1lbnV8V2hlZWwpLy50ZXN0KGtleSkpIHJldHVybiBjaGFuZ2VzLnB1c2goW2tleSwgdmFsdWUsIHRydWUsIFtdXSk7XG4gICAgLy8gU3BsaXQgZGFzaGVkIHByb3BzXG4gICAgbGV0IGVudHJpZXMgPSBbXTtcbiAgICBpZiAoa2V5LmluY2x1ZGVzKCctJykpIGVudHJpZXMgPSBrZXkuc3BsaXQoJy0nKTtcbiAgICBjaGFuZ2VzLnB1c2goW2tleSwgdmFsdWUsIGZhbHNlLCBlbnRyaWVzXSk7XG5cbiAgICAvLyBSZXNldCBwaWVyY2VkIHByb3BzXG4gICAgZm9yIChjb25zdCBwcm9wIGluIHByb3BzKSB7XG4gICAgICBjb25zdCB2YWx1ZSA9IHByb3BzW3Byb3BdO1xuICAgICAgaWYgKHByb3Auc3RhcnRzV2l0aChgJHtrZXl9LWApKSBjaGFuZ2VzLnB1c2goW3Byb3AsIHZhbHVlLCBmYWxzZSwgcHJvcC5zcGxpdCgnLScpXSk7XG4gICAgfVxuICB9KTtcbiAgY29uc3QgbWVtb2l6ZWQgPSB7XG4gICAgLi4ucHJvcHNcbiAgfTtcbiAgaWYgKGxvY2FsU3RhdGUubWVtb2l6ZWRQcm9wcyAmJiBsb2NhbFN0YXRlLm1lbW9pemVkUHJvcHMuYXJncykgbWVtb2l6ZWQuYXJncyA9IGxvY2FsU3RhdGUubWVtb2l6ZWRQcm9wcy5hcmdzO1xuICBpZiAobG9jYWxTdGF0ZS5tZW1vaXplZFByb3BzICYmIGxvY2FsU3RhdGUubWVtb2l6ZWRQcm9wcy5hdHRhY2gpIG1lbW9pemVkLmF0dGFjaCA9IGxvY2FsU3RhdGUubWVtb2l6ZWRQcm9wcy5hdHRhY2g7XG4gIHJldHVybiB7XG4gICAgbWVtb2l6ZWQsXG4gICAgY2hhbmdlc1xuICB9O1xufVxuY29uc3QgX19ERVZfXyA9IHR5cGVvZiBwcm9jZXNzICE9PSAndW5kZWZpbmVkJyAmJiBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nO1xuXG4vLyBUaGlzIGZ1bmN0aW9uIGFwcGxpZXMgYSBzZXQgb2YgY2hhbmdlcyB0byB0aGUgaW5zdGFuY2VcbmZ1bmN0aW9uIGFwcGx5UHJvcHMkMShpbnN0YW5jZSwgZGF0YSkge1xuICB2YXIgX2luc3RhbmNlJF9fcjNmMywgX3Jvb3QkZ2V0U3RhdGUsIF9pbnN0YW5jZSRfX3IzZjQ7XG4gIC8vIEZpbHRlciBlcXVhbHMsIGV2ZW50cyBhbmQgcmVzZXJ2ZWQgcHJvcHNcbiAgY29uc3QgbG9jYWxTdGF0ZSA9IChfaW5zdGFuY2UkX19yM2YzID0gaW5zdGFuY2UuX19yM2YpICE9IG51bGwgPyBfaW5zdGFuY2UkX19yM2YzIDoge307XG4gIGNvbnN0IHJvb3QgPSBsb2NhbFN0YXRlLnJvb3Q7XG4gIGNvbnN0IHJvb3RTdGF0ZSA9IChfcm9vdCRnZXRTdGF0ZSA9IHJvb3QgPT0gbnVsbCA/IHZvaWQgMCA6IHJvb3QuZ2V0U3RhdGUgPT0gbnVsbCA/IHZvaWQgMCA6IHJvb3QuZ2V0U3RhdGUoKSkgIT0gbnVsbCA/IF9yb290JGdldFN0YXRlIDoge307XG4gIGNvbnN0IHtcbiAgICBtZW1vaXplZCxcbiAgICBjaGFuZ2VzXG4gIH0gPSBpc0RpZmZTZXQoZGF0YSkgPyBkYXRhIDogZGlmZlByb3BzKGluc3RhbmNlLCBkYXRhKTtcbiAgY29uc3QgcHJldkhhbmRsZXJzID0gbG9jYWxTdGF0ZS5ldmVudENvdW50O1xuXG4gIC8vIFByZXBhcmUgbWVtb2l6ZWQgcHJvcHNcbiAgaWYgKGluc3RhbmNlLl9fcjNmKSBpbnN0YW5jZS5fX3IzZi5tZW1vaXplZFByb3BzID0gbWVtb2l6ZWQ7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgY2hhbmdlcy5sZW5ndGg7IGkrKykge1xuICAgIGxldCBba2V5LCB2YWx1ZSwgaXNFdmVudCwga2V5c10gPSBjaGFuZ2VzW2ldO1xuXG4gICAgLy8gQWxpYXMgKG91dHB1dCllbmNvZGluZyA9PiAob3V0cHV0KWNvbG9yU3BhY2UgKHNpbmNlIHIxNTIpXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3BtbmRycy9yZWFjdC10aHJlZS1maWJlci9wdWxsLzI4MjlcbiAgICBpZiAoaGFzQ29sb3JTcGFjZShpbnN0YW5jZSkpIHtcbiAgICAgIGNvbnN0IHNSR0JFbmNvZGluZyA9IDMwMDE7XG4gICAgICBjb25zdCBTUkdCQ29sb3JTcGFjZSA9ICdzcmdiJztcbiAgICAgIGNvbnN0IExpbmVhclNSR0JDb2xvclNwYWNlID0gJ3NyZ2ItbGluZWFyJztcbiAgICAgIGlmIChrZXkgPT09ICdlbmNvZGluZycpIHtcbiAgICAgICAga2V5ID0gJ2NvbG9yU3BhY2UnO1xuICAgICAgICB2YWx1ZSA9IHZhbHVlID09PSBzUkdCRW5jb2RpbmcgPyBTUkdCQ29sb3JTcGFjZSA6IExpbmVhclNSR0JDb2xvclNwYWNlO1xuICAgICAgfSBlbHNlIGlmIChrZXkgPT09ICdvdXRwdXRFbmNvZGluZycpIHtcbiAgICAgICAga2V5ID0gJ291dHB1dENvbG9yU3BhY2UnO1xuICAgICAgICB2YWx1ZSA9IHZhbHVlID09PSBzUkdCRW5jb2RpbmcgPyBTUkdCQ29sb3JTcGFjZSA6IExpbmVhclNSR0JDb2xvclNwYWNlO1xuICAgICAgfVxuICAgIH1cbiAgICBsZXQgY3VycmVudEluc3RhbmNlID0gaW5zdGFuY2U7XG4gICAgbGV0IHRhcmdldFByb3AgPSBjdXJyZW50SW5zdGFuY2Vba2V5XTtcblxuICAgIC8vIFJldm9sdmUgZGFzaGVkIHByb3BzXG4gICAgaWYgKGtleXMubGVuZ3RoKSB7XG4gICAgICB0YXJnZXRQcm9wID0ga2V5cy5yZWR1Y2UoKGFjYywga2V5KSA9PiBhY2Nba2V5XSwgaW5zdGFuY2UpO1xuICAgICAgLy8gSWYgdGhlIHRhcmdldCBpcyBhdG9taWMsIGl0IGZvcmNlcyB1cyB0byBzd2l0Y2ggdGhlIHJvb3RcbiAgICAgIGlmICghKHRhcmdldFByb3AgJiYgdGFyZ2V0UHJvcC5zZXQpKSB7XG4gICAgICAgIGNvbnN0IFtuYW1lLCAuLi5yZXZlcnNlRW50cmllc10gPSBrZXlzLnJldmVyc2UoKTtcbiAgICAgICAgY3VycmVudEluc3RhbmNlID0gcmV2ZXJzZUVudHJpZXMucmV2ZXJzZSgpLnJlZHVjZSgoYWNjLCBrZXkpID0+IGFjY1trZXldLCBpbnN0YW5jZSk7XG4gICAgICAgIGtleSA9IG5hbWU7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL21yZG9vYi90aHJlZS5qcy9pc3N1ZXMvMjEyMDlcbiAgICAvLyBITVIvZmFzdC1yZWZyZXNoIHJlbGllcyBvbiB0aGUgYWJpbGl0eSB0byBjYW5jZWwgb3V0IHByb3BzLCBidXQgdGhyZWVqc1xuICAgIC8vIGhhcyBubyBtZWFucyB0byBkbyB0aGlzLiBIZW5jZSB3ZSBjdXJhdGUgYSBzbWFsbCBjb2xsZWN0aW9uIG9mIHZhbHVlLWNsYXNzZXNcbiAgICAvLyB3aXRoIHRoZWlyIHJlc3BlY3RpdmUgY29uc3RydWN0b3Ivc2V0IGFyZ3VtZW50c1xuICAgIC8vIEZvciByZW1vdmVkIHByb3BzLCB0cnkgdG8gc2V0IGRlZmF1bHQgdmFsdWVzLCBpZiBwb3NzaWJsZVxuICAgIGlmICh2YWx1ZSA9PT0gREVGQVVMVCArICdyZW1vdmUnKSB7XG4gICAgICBpZiAoY3VycmVudEluc3RhbmNlLmNvbnN0cnVjdG9yKSB7XG4gICAgICAgIC8vIGNyZWF0ZSBhIGJsYW5rIHNsYXRlIG9mIHRoZSBpbnN0YW5jZSBhbmQgY29weSB0aGUgcGFydGljdWxhciBwYXJhbWV0ZXIuXG4gICAgICAgIGxldCBjdG9yID0gREVGQVVMVFMuZ2V0KGN1cnJlbnRJbnN0YW5jZS5jb25zdHJ1Y3Rvcik7XG4gICAgICAgIGlmICghY3Rvcikge1xuICAgICAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgICAgICBjdG9yID0gbmV3IGN1cnJlbnRJbnN0YW5jZS5jb25zdHJ1Y3RvcigpO1xuICAgICAgICAgIERFRkFVTFRTLnNldChjdXJyZW50SW5zdGFuY2UuY29uc3RydWN0b3IsIGN0b3IpO1xuICAgICAgICB9XG4gICAgICAgIHZhbHVlID0gY3RvcltrZXldO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gaW5zdGFuY2UgZG9lcyBub3QgaGF2ZSBjb25zdHJ1Y3RvciwganVzdCBzZXQgaXQgdG8gMFxuICAgICAgICB2YWx1ZSA9IDA7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gRGVhbCB3aXRoIHBvaW50ZXIgZXZlbnRzIC4uLlxuICAgIGlmIChpc0V2ZW50KSB7XG4gICAgICBpZiAodmFsdWUpIGxvY2FsU3RhdGUuaGFuZGxlcnNba2V5XSA9IHZhbHVlO2Vsc2UgZGVsZXRlIGxvY2FsU3RhdGUuaGFuZGxlcnNba2V5XTtcbiAgICAgIGxvY2FsU3RhdGUuZXZlbnRDb3VudCA9IE9iamVjdC5rZXlzKGxvY2FsU3RhdGUuaGFuZGxlcnMpLmxlbmd0aDtcbiAgICB9XG4gICAgLy8gU3BlY2lhbCB0cmVhdG1lbnQgZm9yIG9iamVjdHMgd2l0aCBzdXBwb3J0IGZvciBzZXQvY29weSwgYW5kIGxheWVyc1xuICAgIGVsc2UgaWYgKHRhcmdldFByb3AgJiYgdGFyZ2V0UHJvcC5zZXQgJiYgKHRhcmdldFByb3AuY29weSB8fCB0YXJnZXRQcm9wIGluc3RhbmNlb2YgVEhSRUUuTGF5ZXJzKSkge1xuICAgICAgLy8gSWYgdmFsdWUgaXMgYW4gYXJyYXlcbiAgICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICBpZiAodGFyZ2V0UHJvcC5mcm9tQXJyYXkpIHRhcmdldFByb3AuZnJvbUFycmF5KHZhbHVlKTtlbHNlIHRhcmdldFByb3Auc2V0KC4uLnZhbHVlKTtcbiAgICAgIH1cbiAgICAgIC8vIFRlc3QgYWdhaW4gdGFyZ2V0LmNvcHkoY2xhc3MpIG5leHQgLi4uXG4gICAgICBlbHNlIGlmICh0YXJnZXRQcm9wLmNvcHkgJiYgdmFsdWUgJiYgdmFsdWUuY29uc3RydWN0b3IgJiYgKFxuICAgICAgLy8gU29tZSBlbnZpcm9ubWVudHMgbWF5IGJyZWFrIHN0cmljdCBpZGVudGl0eSBjaGVja3MgYnkgZHVwbGljYXRpbmcgdmVyc2lvbnMgb2YgdGhyZWUuanMuXG4gICAgICAvLyBMb29zZW4gdG8gdW5taW5pZmllZCBuYW1lcywgaWdub3JpbmcgZGVzY2VuZGVudHMuXG4gICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vcG1uZHJzL3JlYWN0LXRocmVlLWZpYmVyL2lzc3Vlcy8yODU2XG4gICAgICAvLyBUT0RPOiBmaXggdXBzdHJlYW0gYW5kIHJlbW92ZSBpbiB2OVxuICAgICAgX19ERVZfXyA/IHRhcmdldFByb3AuY29uc3RydWN0b3IubmFtZSA9PT0gdmFsdWUuY29uc3RydWN0b3IubmFtZSA6IHRhcmdldFByb3AuY29uc3RydWN0b3IgPT09IHZhbHVlLmNvbnN0cnVjdG9yKSkge1xuICAgICAgICB0YXJnZXRQcm9wLmNvcHkodmFsdWUpO1xuICAgICAgfVxuICAgICAgLy8gSWYgbm90aGluZyBlbHNlIGZpdHMsIGp1c3Qgc2V0IHRoZSBzaW5nbGUgdmFsdWUsIGlnbm9yZSB1bmRlZmluZWRcbiAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9wbW5kcnMvcmVhY3QtdGhyZWUtZmliZXIvaXNzdWVzLzI3NFxuICAgICAgZWxzZSBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBjb25zdCBpc0NvbG9yID0gdGFyZ2V0UHJvcCBpbnN0YW5jZW9mIFRIUkVFLkNvbG9yO1xuICAgICAgICAvLyBBbGxvdyBzZXR0aW5nIGFycmF5IHNjYWxhcnNcbiAgICAgICAgaWYgKCFpc0NvbG9yICYmIHRhcmdldFByb3Auc2V0U2NhbGFyKSB0YXJnZXRQcm9wLnNldFNjYWxhcih2YWx1ZSk7XG4gICAgICAgIC8vIExheWVycyBoYXZlIG5vIGNvcHkgZnVuY3Rpb24sIHdlIG11c3QgdGhlcmVmb3JlIGNvcHkgdGhlIG1hc2sgcHJvcGVydHlcbiAgICAgICAgZWxzZSBpZiAodGFyZ2V0UHJvcCBpbnN0YW5jZW9mIFRIUkVFLkxheWVycyAmJiB2YWx1ZSBpbnN0YW5jZW9mIFRIUkVFLkxheWVycykgdGFyZ2V0UHJvcC5tYXNrID0gdmFsdWUubWFzaztcbiAgICAgICAgLy8gT3RoZXJ3aXNlIGp1c3Qgc2V0IC4uLlxuICAgICAgICBlbHNlIHRhcmdldFByb3Auc2V0KHZhbHVlKTtcbiAgICAgICAgLy8gRm9yIHZlcnNpb25zIG9mIHRocmVlIHdoaWNoIGRvbid0IHN1cHBvcnQgVEhSRUUuQ29sb3JNYW5hZ2VtZW50LFxuICAgICAgICAvLyBBdXRvLWNvbnZlcnQgc1JHQiBjb2xvcnNcbiAgICAgICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3BtbmRycy9yZWFjdC10aHJlZS1maWJlci9pc3N1ZXMvMzQ0XG4gICAgICAgIGlmICghZ2V0Q29sb3JNYW5hZ2VtZW50KCkgJiYgIXJvb3RTdGF0ZS5saW5lYXIgJiYgaXNDb2xvcikgdGFyZ2V0UHJvcC5jb252ZXJ0U1JHQlRvTGluZWFyKCk7XG4gICAgICB9XG4gICAgICAvLyBFbHNlLCBqdXN0IG92ZXJ3cml0ZSB0aGUgdmFsdWVcbiAgICB9IGVsc2Uge1xuICAgICAgY3VycmVudEluc3RhbmNlW2tleV0gPSB2YWx1ZTtcblxuICAgICAgLy8gQXV0by1jb252ZXJ0IHNSR0IgdGV4dHVyZXMsIGZvciBub3cgLi4uXG4gICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vcG1uZHJzL3JlYWN0LXRocmVlLWZpYmVyL2lzc3Vlcy8zNDRcbiAgICAgIGlmIChjdXJyZW50SW5zdGFuY2Vba2V5XSBpbnN0YW5jZW9mIFRIUkVFLlRleHR1cmUgJiZcbiAgICAgIC8vIHNSR0IgdGV4dHVyZXMgbXVzdCBiZSBSR0JBOCBzaW5jZSByMTM3IGh0dHBzOi8vZ2l0aHViLmNvbS9tcmRvb2IvdGhyZWUuanMvcHVsbC8yMzEyOVxuICAgICAgY3VycmVudEluc3RhbmNlW2tleV0uZm9ybWF0ID09PSBUSFJFRS5SR0JBRm9ybWF0ICYmIGN1cnJlbnRJbnN0YW5jZVtrZXldLnR5cGUgPT09IFRIUkVFLlVuc2lnbmVkQnl0ZVR5cGUpIHtcbiAgICAgICAgY29uc3QgdGV4dHVyZSA9IGN1cnJlbnRJbnN0YW5jZVtrZXldO1xuICAgICAgICBpZiAoaGFzQ29sb3JTcGFjZSh0ZXh0dXJlKSAmJiBoYXNDb2xvclNwYWNlKHJvb3RTdGF0ZS5nbCkpIHRleHR1cmUuY29sb3JTcGFjZSA9IHJvb3RTdGF0ZS5nbC5vdXRwdXRDb2xvclNwYWNlO2Vsc2UgdGV4dHVyZS5lbmNvZGluZyA9IHJvb3RTdGF0ZS5nbC5vdXRwdXRFbmNvZGluZztcbiAgICAgIH1cbiAgICB9XG4gICAgaW52YWxpZGF0ZUluc3RhbmNlKGluc3RhbmNlKTtcbiAgfVxuICBpZiAobG9jYWxTdGF0ZS5wYXJlbnQgJiYgcm9vdFN0YXRlLmludGVybmFsICYmIGluc3RhbmNlLnJheWNhc3QgJiYgcHJldkhhbmRsZXJzICE9PSBsb2NhbFN0YXRlLmV2ZW50Q291bnQpIHtcbiAgICAvLyBQcmUtZW1wdGl2ZWx5IHJlbW92ZSB0aGUgaW5zdGFuY2UgZnJvbSB0aGUgaW50ZXJhY3Rpb24gbWFuYWdlclxuICAgIGNvbnN0IGluZGV4ID0gcm9vdFN0YXRlLmludGVybmFsLmludGVyYWN0aW9uLmluZGV4T2YoaW5zdGFuY2UpO1xuICAgIGlmIChpbmRleCA+IC0xKSByb290U3RhdGUuaW50ZXJuYWwuaW50ZXJhY3Rpb24uc3BsaWNlKGluZGV4LCAxKTtcbiAgICAvLyBBZGQgdGhlIGluc3RhbmNlIHRvIHRoZSBpbnRlcmFjdGlvbiBtYW5hZ2VyIG9ubHkgd2hlbiBpdCBoYXMgaGFuZGxlcnNcbiAgICBpZiAobG9jYWxTdGF0ZS5ldmVudENvdW50KSByb290U3RhdGUuaW50ZXJuYWwuaW50ZXJhY3Rpb24ucHVzaChpbnN0YW5jZSk7XG4gIH1cblxuICAvLyBDYWxsIHRoZSB1cGRhdGUgbGlmZWN5Y2xlIHdoZW4gaXQgaXMgYmVpbmcgdXBkYXRlZCwgYnV0IG9ubHkgd2hlbiBpdCBpcyBwYXJ0IG9mIHRoZSBzY2VuZS5cbiAgLy8gU2tpcCB1cGRhdGVzIHRvIHRoZSBgb25VcGRhdGVgIHByb3AgaXRzZWxmXG4gIGNvbnN0IGlzQ2lyY3VsYXIgPSBjaGFuZ2VzLmxlbmd0aCA9PT0gMSAmJiBjaGFuZ2VzWzBdWzBdID09PSAnb25VcGRhdGUnO1xuICBpZiAoIWlzQ2lyY3VsYXIgJiYgY2hhbmdlcy5sZW5ndGggJiYgKF9pbnN0YW5jZSRfX3IzZjQgPSBpbnN0YW5jZS5fX3IzZikgIT0gbnVsbCAmJiBfaW5zdGFuY2UkX19yM2Y0LnBhcmVudCkgdXBkYXRlSW5zdGFuY2UoaW5zdGFuY2UpO1xuICByZXR1cm4gaW5zdGFuY2U7XG59XG5mdW5jdGlvbiBpbnZhbGlkYXRlSW5zdGFuY2UoaW5zdGFuY2UpIHtcbiAgdmFyIF9pbnN0YW5jZSRfX3IzZjUsIF9pbnN0YW5jZSRfX3IzZjUkcm9vdDtcbiAgY29uc3Qgc3RhdGUgPSAoX2luc3RhbmNlJF9fcjNmNSA9IGluc3RhbmNlLl9fcjNmKSA9PSBudWxsID8gdm9pZCAwIDogKF9pbnN0YW5jZSRfX3IzZjUkcm9vdCA9IF9pbnN0YW5jZSRfX3IzZjUucm9vdCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9pbnN0YW5jZSRfX3IzZjUkcm9vdC5nZXRTdGF0ZSA9PSBudWxsID8gdm9pZCAwIDogX2luc3RhbmNlJF9fcjNmNSRyb290LmdldFN0YXRlKCk7XG4gIGlmIChzdGF0ZSAmJiBzdGF0ZS5pbnRlcm5hbC5mcmFtZXMgPT09IDApIHN0YXRlLmludmFsaWRhdGUoKTtcbn1cbmZ1bmN0aW9uIHVwZGF0ZUluc3RhbmNlKGluc3RhbmNlKSB7XG4gIGluc3RhbmNlLm9uVXBkYXRlID09IG51bGwgPyB2b2lkIDAgOiBpbnN0YW5jZS5vblVwZGF0ZShpbnN0YW5jZSk7XG59XG5mdW5jdGlvbiB1cGRhdGVDYW1lcmEoY2FtZXJhLCBzaXplKSB7XG4gIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9wbW5kcnMvcmVhY3QtdGhyZWUtZmliZXIvaXNzdWVzLzkyXG4gIC8vIERvIG5vdCBtZXNzIHdpdGggdGhlIGNhbWVyYSBpZiBpdCBiZWxvbmdzIHRvIHRoZSB1c2VyXG4gIGlmICghY2FtZXJhLm1hbnVhbCkge1xuICAgIGlmIChpc09ydGhvZ3JhcGhpY0NhbWVyYShjYW1lcmEpKSB7XG4gICAgICBjYW1lcmEubGVmdCA9IHNpemUud2lkdGggLyAtMjtcbiAgICAgIGNhbWVyYS5yaWdodCA9IHNpemUud2lkdGggLyAyO1xuICAgICAgY2FtZXJhLnRvcCA9IHNpemUuaGVpZ2h0IC8gMjtcbiAgICAgIGNhbWVyYS5ib3R0b20gPSBzaXplLmhlaWdodCAvIC0yO1xuICAgIH0gZWxzZSB7XG4gICAgICBjYW1lcmEuYXNwZWN0ID0gc2l6ZS53aWR0aCAvIHNpemUuaGVpZ2h0O1xuICAgIH1cbiAgICBjYW1lcmEudXBkYXRlUHJvamVjdGlvbk1hdHJpeCgpO1xuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9wbW5kcnMvcmVhY3QtdGhyZWUtZmliZXIvaXNzdWVzLzE3OFxuICAgIC8vIFVwZGF0ZSBtYXRyaXggd29ybGQgc2luY2UgdGhlIHJlbmRlcmVyIGlzIGEgZnJhbWUgbGF0ZVxuICAgIGNhbWVyYS51cGRhdGVNYXRyaXhXb3JsZCgpO1xuICB9XG59XG5cbmZ1bmN0aW9uIG1ha2VJZChldmVudCkge1xuICByZXR1cm4gKGV2ZW50LmV2ZW50T2JqZWN0IHx8IGV2ZW50Lm9iamVjdCkudXVpZCArICcvJyArIGV2ZW50LmluZGV4ICsgZXZlbnQuaW5zdGFuY2VJZDtcbn1cblxuLy8gaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L3RyZWUvbWFpbi9wYWNrYWdlcy9yZWFjdC1yZWNvbmNpbGVyI2dldGN1cnJlbnRldmVudHByaW9yaXR5XG4vLyBHaXZlcyBSZWFjdCBhIGNsdWUgYXMgdG8gaG93IGltcG9ydCB0aGUgY3VycmVudCBpbnRlcmFjdGlvbiBpc1xuZnVuY3Rpb24gZ2V0RXZlbnRQcmlvcml0eSgpIHtcbiAgdmFyIF9nbG9iYWxTY29wZSRldmVudDtcbiAgLy8gR2V0IGEgaGFuZGxlIHRvIHRoZSBjdXJyZW50IGdsb2JhbCBzY29wZSBpbiB3aW5kb3cgYW5kIHdvcmtlciBjb250ZXh0cyBpZiBhYmxlXG4gIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9wbW5kcnMvcmVhY3QtdGhyZWUtZmliZXIvcHVsbC8yNDkzXG4gIGNvbnN0IGdsb2JhbFNjb3BlID0gdHlwZW9mIHNlbGYgIT09ICd1bmRlZmluZWQnICYmIHNlbGYgfHwgdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93O1xuICBpZiAoIWdsb2JhbFNjb3BlKSByZXR1cm4gRGVmYXVsdEV2ZW50UHJpb3JpdHk7XG4gIGNvbnN0IG5hbWUgPSAoX2dsb2JhbFNjb3BlJGV2ZW50ID0gZ2xvYmFsU2NvcGUuZXZlbnQpID09IG51bGwgPyB2b2lkIDAgOiBfZ2xvYmFsU2NvcGUkZXZlbnQudHlwZTtcbiAgc3dpdGNoIChuYW1lKSB7XG4gICAgY2FzZSAnY2xpY2snOlxuICAgIGNhc2UgJ2NvbnRleHRtZW51JzpcbiAgICBjYXNlICdkYmxjbGljayc6XG4gICAgY2FzZSAncG9pbnRlcmNhbmNlbCc6XG4gICAgY2FzZSAncG9pbnRlcmRvd24nOlxuICAgIGNhc2UgJ3BvaW50ZXJ1cCc6XG4gICAgICByZXR1cm4gRGlzY3JldGVFdmVudFByaW9yaXR5O1xuICAgIGNhc2UgJ3BvaW50ZXJtb3ZlJzpcbiAgICBjYXNlICdwb2ludGVyb3V0JzpcbiAgICBjYXNlICdwb2ludGVyb3Zlcic6XG4gICAgY2FzZSAncG9pbnRlcmVudGVyJzpcbiAgICBjYXNlICdwb2ludGVybGVhdmUnOlxuICAgIGNhc2UgJ3doZWVsJzpcbiAgICAgIHJldHVybiBDb250aW51b3VzRXZlbnRQcmlvcml0eTtcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIERlZmF1bHRFdmVudFByaW9yaXR5O1xuICB9XG59XG5cbi8qKlxyXG4gKiBSZWxlYXNlIHBvaW50ZXIgY2FwdHVyZXMuXHJcbiAqIFRoaXMgaXMgY2FsbGVkIGJ5IHJlbGVhc2VQb2ludGVyQ2FwdHVyZSBpbiB0aGUgQVBJLCBhbmQgd2hlbiBhbiBvYmplY3QgaXMgcmVtb3ZlZC5cclxuICovXG5mdW5jdGlvbiByZWxlYXNlSW50ZXJuYWxQb2ludGVyQ2FwdHVyZShjYXB0dXJlZE1hcCwgb2JqLCBjYXB0dXJlcywgcG9pbnRlcklkKSB7XG4gIGNvbnN0IGNhcHR1cmVEYXRhID0gY2FwdHVyZXMuZ2V0KG9iaik7XG4gIGlmIChjYXB0dXJlRGF0YSkge1xuICAgIGNhcHR1cmVzLmRlbGV0ZShvYmopO1xuICAgIC8vIElmIHRoaXMgd2FzIHRoZSBsYXN0IGNhcHR1cmluZyBvYmplY3QgZm9yIHRoaXMgcG9pbnRlclxuICAgIGlmIChjYXB0dXJlcy5zaXplID09PSAwKSB7XG4gICAgICBjYXB0dXJlZE1hcC5kZWxldGUocG9pbnRlcklkKTtcbiAgICAgIGNhcHR1cmVEYXRhLnRhcmdldC5yZWxlYXNlUG9pbnRlckNhcHR1cmUocG9pbnRlcklkKTtcbiAgICB9XG4gIH1cbn1cbmZ1bmN0aW9uIHJlbW92ZUludGVyYWN0aXZpdHkoc3RvcmUsIG9iamVjdCkge1xuICBjb25zdCB7XG4gICAgaW50ZXJuYWxcbiAgfSA9IHN0b3JlLmdldFN0YXRlKCk7XG4gIC8vIFJlbW92ZXMgZXZlcnkgdHJhY2Ugb2YgYW4gb2JqZWN0IGZyb20gdGhlIGRhdGEgc3RvcmVcbiAgaW50ZXJuYWwuaW50ZXJhY3Rpb24gPSBpbnRlcm5hbC5pbnRlcmFjdGlvbi5maWx0ZXIobyA9PiBvICE9PSBvYmplY3QpO1xuICBpbnRlcm5hbC5pbml0aWFsSGl0cyA9IGludGVybmFsLmluaXRpYWxIaXRzLmZpbHRlcihvID0+IG8gIT09IG9iamVjdCk7XG4gIGludGVybmFsLmhvdmVyZWQuZm9yRWFjaCgodmFsdWUsIGtleSkgPT4ge1xuICAgIGlmICh2YWx1ZS5ldmVudE9iamVjdCA9PT0gb2JqZWN0IHx8IHZhbHVlLm9iamVjdCA9PT0gb2JqZWN0KSB7XG4gICAgICAvLyBDbGVhciBvdXQgaW50ZXJzZWN0cywgdGhleSBhcmUgb3V0ZGF0ZWQgYnkgbm93XG4gICAgICBpbnRlcm5hbC5ob3ZlcmVkLmRlbGV0ZShrZXkpO1xuICAgIH1cbiAgfSk7XG4gIGludGVybmFsLmNhcHR1cmVkTWFwLmZvckVhY2goKGNhcHR1cmVzLCBwb2ludGVySWQpID0+IHtcbiAgICByZWxlYXNlSW50ZXJuYWxQb2ludGVyQ2FwdHVyZShpbnRlcm5hbC5jYXB0dXJlZE1hcCwgb2JqZWN0LCBjYXB0dXJlcywgcG9pbnRlcklkKTtcbiAgfSk7XG59XG5mdW5jdGlvbiBjcmVhdGVFdmVudHMoc3RvcmUpIHtcbiAgLyoqIENhbGN1bGF0ZXMgZGVsdGEgKi9cbiAgZnVuY3Rpb24gY2FsY3VsYXRlRGlzdGFuY2UoZXZlbnQpIHtcbiAgICBjb25zdCB7XG4gICAgICBpbnRlcm5hbFxuICAgIH0gPSBzdG9yZS5nZXRTdGF0ZSgpO1xuICAgIGNvbnN0IGR4ID0gZXZlbnQub2Zmc2V0WCAtIGludGVybmFsLmluaXRpYWxDbGlja1swXTtcbiAgICBjb25zdCBkeSA9IGV2ZW50Lm9mZnNldFkgLSBpbnRlcm5hbC5pbml0aWFsQ2xpY2tbMV07XG4gICAgcmV0dXJuIE1hdGgucm91bmQoTWF0aC5zcXJ0KGR4ICogZHggKyBkeSAqIGR5KSk7XG4gIH1cblxuICAvKiogUmV0dXJucyB0cnVlIGlmIGFuIGluc3RhbmNlIGhhcyBhIHZhbGlkIHBvaW50ZXItZXZlbnQgcmVnaXN0ZXJlZCwgdGhpcyBleGNsdWRlcyBzY3JvbGwsIGNsaWNrcyBldGMgKi9cbiAgZnVuY3Rpb24gZmlsdGVyUG9pbnRlckV2ZW50cyhvYmplY3RzKSB7XG4gICAgcmV0dXJuIG9iamVjdHMuZmlsdGVyKG9iaiA9PiBbJ01vdmUnLCAnT3ZlcicsICdFbnRlcicsICdPdXQnLCAnTGVhdmUnXS5zb21lKG5hbWUgPT4ge1xuICAgICAgdmFyIF9yM2Y7XG4gICAgICByZXR1cm4gKF9yM2YgPSBvYmouX19yM2YpID09IG51bGwgPyB2b2lkIDAgOiBfcjNmLmhhbmRsZXJzWydvblBvaW50ZXInICsgbmFtZV07XG4gICAgfSkpO1xuICB9XG4gIGZ1bmN0aW9uIGludGVyc2VjdChldmVudCwgZmlsdGVyKSB7XG4gICAgY29uc3Qgc3RhdGUgPSBzdG9yZS5nZXRTdGF0ZSgpO1xuICAgIGNvbnN0IGR1cGxpY2F0ZXMgPSBuZXcgU2V0KCk7XG4gICAgY29uc3QgaW50ZXJzZWN0aW9ucyA9IFtdO1xuICAgIC8vIEFsbG93IGNhbGxlcnMgdG8gZWxpbWluYXRlIGV2ZW50IG9iamVjdHNcbiAgICBjb25zdCBldmVudHNPYmplY3RzID0gZmlsdGVyID8gZmlsdGVyKHN0YXRlLmludGVybmFsLmludGVyYWN0aW9uKSA6IHN0YXRlLmludGVybmFsLmludGVyYWN0aW9uO1xuICAgIC8vIFJlc2V0IGFsbCByYXljYXN0ZXIgY2FtZXJhcyB0byB1bmRlZmluZWRcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGV2ZW50c09iamVjdHMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IHN0YXRlID0gZ2V0Um9vdFN0YXRlKGV2ZW50c09iamVjdHNbaV0pO1xuICAgICAgaWYgKHN0YXRlKSB7XG4gICAgICAgIHN0YXRlLnJheWNhc3Rlci5jYW1lcmEgPSB1bmRlZmluZWQ7XG4gICAgICB9XG4gICAgfVxuICAgIGlmICghc3RhdGUucHJldmlvdXNSb290KSB7XG4gICAgICAvLyBNYWtlIHN1cmUgcm9vdC1sZXZlbCBwb2ludGVyIGFuZCByYXkgYXJlIHNldCB1cFxuICAgICAgc3RhdGUuZXZlbnRzLmNvbXB1dGUgPT0gbnVsbCA/IHZvaWQgMCA6IHN0YXRlLmV2ZW50cy5jb21wdXRlKGV2ZW50LCBzdGF0ZSk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGhhbmRsZVJheWNhc3Qob2JqKSB7XG4gICAgICBjb25zdCBzdGF0ZSA9IGdldFJvb3RTdGF0ZShvYmopO1xuICAgICAgLy8gU2tpcCBldmVudCBoYW5kbGluZyB3aGVuIG5vRXZlbnRzIGlzIHNldCwgb3Igd2hlbiB0aGUgcmF5Y2FzdGVycyBjYW1lcmEgaXMgbnVsbFxuICAgICAgaWYgKCFzdGF0ZSB8fCAhc3RhdGUuZXZlbnRzLmVuYWJsZWQgfHwgc3RhdGUucmF5Y2FzdGVyLmNhbWVyYSA9PT0gbnVsbCkgcmV0dXJuIFtdO1xuXG4gICAgICAvLyBXaGVuIHRoZSBjYW1lcmEgaXMgdW5kZWZpbmVkIHdlIGhhdmUgdG8gY2FsbCB0aGUgZXZlbnQgbGF5ZXJzIHVwZGF0ZSBmdW5jdGlvblxuICAgICAgaWYgKHN0YXRlLnJheWNhc3Rlci5jYW1lcmEgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICB2YXIgX3N0YXRlJHByZXZpb3VzUm9vdDtcbiAgICAgICAgc3RhdGUuZXZlbnRzLmNvbXB1dGUgPT0gbnVsbCA/IHZvaWQgMCA6IHN0YXRlLmV2ZW50cy5jb21wdXRlKGV2ZW50LCBzdGF0ZSwgKF9zdGF0ZSRwcmV2aW91c1Jvb3QgPSBzdGF0ZS5wcmV2aW91c1Jvb3QpID09IG51bGwgPyB2b2lkIDAgOiBfc3RhdGUkcHJldmlvdXNSb290LmdldFN0YXRlKCkpO1xuICAgICAgICAvLyBJZiB0aGUgY2FtZXJhIGlzIHN0aWxsIHVuZGVmaW5lZCB3ZSBoYXZlIHRvIHNraXAgdGhpcyBsYXllciBlbnRpcmVseVxuICAgICAgICBpZiAoc3RhdGUucmF5Y2FzdGVyLmNhbWVyYSA9PT0gdW5kZWZpbmVkKSBzdGF0ZS5yYXljYXN0ZXIuY2FtZXJhID0gbnVsbDtcbiAgICAgIH1cblxuICAgICAgLy8gSW50ZXJzZWN0IG9iamVjdCBieSBvYmplY3RcbiAgICAgIHJldHVybiBzdGF0ZS5yYXljYXN0ZXIuY2FtZXJhID8gc3RhdGUucmF5Y2FzdGVyLmludGVyc2VjdE9iamVjdChvYmosIHRydWUpIDogW107XG4gICAgfVxuXG4gICAgLy8gQ29sbGVjdCBldmVudHNcbiAgICBsZXQgaGl0cyA9IGV2ZW50c09iamVjdHNcbiAgICAvLyBJbnRlcnNlY3Qgb2JqZWN0c1xuICAgIC5mbGF0TWFwKGhhbmRsZVJheWNhc3QpXG4gICAgLy8gU29ydCBieSBldmVudCBwcmlvcml0eSBhbmQgZGlzdGFuY2VcbiAgICAuc29ydCgoYSwgYikgPT4ge1xuICAgICAgY29uc3QgYVN0YXRlID0gZ2V0Um9vdFN0YXRlKGEub2JqZWN0KTtcbiAgICAgIGNvbnN0IGJTdGF0ZSA9IGdldFJvb3RTdGF0ZShiLm9iamVjdCk7XG4gICAgICBpZiAoIWFTdGF0ZSB8fCAhYlN0YXRlKSByZXR1cm4gYS5kaXN0YW5jZSAtIGIuZGlzdGFuY2U7XG4gICAgICByZXR1cm4gYlN0YXRlLmV2ZW50cy5wcmlvcml0eSAtIGFTdGF0ZS5ldmVudHMucHJpb3JpdHkgfHwgYS5kaXN0YW5jZSAtIGIuZGlzdGFuY2U7XG4gICAgfSlcbiAgICAvLyBGaWx0ZXIgb3V0IGR1cGxpY2F0ZXNcbiAgICAuZmlsdGVyKGl0ZW0gPT4ge1xuICAgICAgY29uc3QgaWQgPSBtYWtlSWQoaXRlbSk7XG4gICAgICBpZiAoZHVwbGljYXRlcy5oYXMoaWQpKSByZXR1cm4gZmFsc2U7XG4gICAgICBkdXBsaWNhdGVzLmFkZChpZCk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9KTtcblxuICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9tcmRvb2IvdGhyZWUuanMvaXNzdWVzLzE2MDMxXG4gICAgLy8gQWxsb3cgY3VzdG9tIHVzZXJsYW5kIGludGVyc2VjdCBzb3J0IG9yZGVyLCB0aGlzIGxpa2VseSBvbmx5IG1ha2VzIHNlbnNlIG9uIHRoZSByb290IGZpbHRlclxuICAgIGlmIChzdGF0ZS5ldmVudHMuZmlsdGVyKSBoaXRzID0gc3RhdGUuZXZlbnRzLmZpbHRlcihoaXRzLCBzdGF0ZSk7XG5cbiAgICAvLyBCdWJibGUgdXAgdGhlIGV2ZW50cywgZmluZCB0aGUgZXZlbnQgc291cmNlIChldmVudE9iamVjdClcbiAgICBmb3IgKGNvbnN0IGhpdCBvZiBoaXRzKSB7XG4gICAgICBsZXQgZXZlbnRPYmplY3QgPSBoaXQub2JqZWN0O1xuICAgICAgLy8gQnViYmxlIGV2ZW50IHVwXG4gICAgICB3aGlsZSAoZXZlbnRPYmplY3QpIHtcbiAgICAgICAgdmFyIF9yM2YyO1xuICAgICAgICBpZiAoKF9yM2YyID0gZXZlbnRPYmplY3QuX19yM2YpICE9IG51bGwgJiYgX3IzZjIuZXZlbnRDb3VudCkgaW50ZXJzZWN0aW9ucy5wdXNoKHtcbiAgICAgICAgICAuLi5oaXQsXG4gICAgICAgICAgZXZlbnRPYmplY3RcbiAgICAgICAgfSk7XG4gICAgICAgIGV2ZW50T2JqZWN0ID0gZXZlbnRPYmplY3QucGFyZW50O1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIElmIHRoZSBpbnRlcmFjdGlvbiBpcyBjYXB0dXJlZCwgbWFrZSBhbGwgY2FwdHVyaW5nIHRhcmdldHMgcGFydCBvZiB0aGUgaW50ZXJzZWN0LlxuICAgIGlmICgncG9pbnRlcklkJyBpbiBldmVudCAmJiBzdGF0ZS5pbnRlcm5hbC5jYXB0dXJlZE1hcC5oYXMoZXZlbnQucG9pbnRlcklkKSkge1xuICAgICAgZm9yIChsZXQgY2FwdHVyZURhdGEgb2Ygc3RhdGUuaW50ZXJuYWwuY2FwdHVyZWRNYXAuZ2V0KGV2ZW50LnBvaW50ZXJJZCkudmFsdWVzKCkpIHtcbiAgICAgICAgaWYgKCFkdXBsaWNhdGVzLmhhcyhtYWtlSWQoY2FwdHVyZURhdGEuaW50ZXJzZWN0aW9uKSkpIGludGVyc2VjdGlvbnMucHVzaChjYXB0dXJlRGF0YS5pbnRlcnNlY3Rpb24pO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gaW50ZXJzZWN0aW9ucztcbiAgfVxuXG4gIC8qKiAgSGFuZGxlcyBpbnRlcnNlY3Rpb25zIGJ5IGZvcndhcmRpbmcgdGhlbSB0byBoYW5kbGVycyAqL1xuICBmdW5jdGlvbiBoYW5kbGVJbnRlcnNlY3RzKGludGVyc2VjdGlvbnMsIGV2ZW50LCBkZWx0YSwgY2FsbGJhY2spIHtcbiAgICBjb25zdCByb290U3RhdGUgPSBzdG9yZS5nZXRTdGF0ZSgpO1xuXG4gICAgLy8gSWYgYW55dGhpbmcgaGFzIGJlZW4gZm91bmQsIGZvcndhcmQgaXQgdG8gdGhlIGV2ZW50IGxpc3RlbmVyc1xuICAgIGlmIChpbnRlcnNlY3Rpb25zLmxlbmd0aCkge1xuICAgICAgY29uc3QgbG9jYWxTdGF0ZSA9IHtcbiAgICAgICAgc3RvcHBlZDogZmFsc2VcbiAgICAgIH07XG4gICAgICBmb3IgKGNvbnN0IGhpdCBvZiBpbnRlcnNlY3Rpb25zKSB7XG4gICAgICAgIGNvbnN0IHN0YXRlID0gZ2V0Um9vdFN0YXRlKGhpdC5vYmplY3QpIHx8IHJvb3RTdGF0ZTtcbiAgICAgICAgY29uc3Qge1xuICAgICAgICAgIHJheWNhc3RlcixcbiAgICAgICAgICBwb2ludGVyLFxuICAgICAgICAgIGNhbWVyYSxcbiAgICAgICAgICBpbnRlcm5hbFxuICAgICAgICB9ID0gc3RhdGU7XG4gICAgICAgIGNvbnN0IHVucHJvamVjdGVkUG9pbnQgPSBuZXcgVEhSRUUuVmVjdG9yMyhwb2ludGVyLngsIHBvaW50ZXIueSwgMCkudW5wcm9qZWN0KGNhbWVyYSk7XG4gICAgICAgIGNvbnN0IGhhc1BvaW50ZXJDYXB0dXJlID0gaWQgPT4ge1xuICAgICAgICAgIHZhciBfaW50ZXJuYWwkY2FwdHVyZWRNYXAsIF9pbnRlcm5hbCRjYXB0dXJlZE1hcDI7XG4gICAgICAgICAgcmV0dXJuIChfaW50ZXJuYWwkY2FwdHVyZWRNYXAgPSAoX2ludGVybmFsJGNhcHR1cmVkTWFwMiA9IGludGVybmFsLmNhcHR1cmVkTWFwLmdldChpZCkpID09IG51bGwgPyB2b2lkIDAgOiBfaW50ZXJuYWwkY2FwdHVyZWRNYXAyLmhhcyhoaXQuZXZlbnRPYmplY3QpKSAhPSBudWxsID8gX2ludGVybmFsJGNhcHR1cmVkTWFwIDogZmFsc2U7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHNldFBvaW50ZXJDYXB0dXJlID0gaWQgPT4ge1xuICAgICAgICAgIGNvbnN0IGNhcHR1cmVEYXRhID0ge1xuICAgICAgICAgICAgaW50ZXJzZWN0aW9uOiBoaXQsXG4gICAgICAgICAgICB0YXJnZXQ6IGV2ZW50LnRhcmdldFxuICAgICAgICAgIH07XG4gICAgICAgICAgaWYgKGludGVybmFsLmNhcHR1cmVkTWFwLmhhcyhpZCkpIHtcbiAgICAgICAgICAgIC8vIGlmIHRoZSBwb2ludGVySWQgd2FzIHByZXZpb3VzbHkgY2FwdHVyZWQsIHdlIGFkZCB0aGUgaGl0IHRvIHRoZVxuICAgICAgICAgICAgLy8gZXZlbnQgY2FwdHVyZWRNYXAuXG4gICAgICAgICAgICBpbnRlcm5hbC5jYXB0dXJlZE1hcC5nZXQoaWQpLnNldChoaXQuZXZlbnRPYmplY3QsIGNhcHR1cmVEYXRhKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gaWYgdGhlIHBvaW50ZXJJZCB3YXMgbm90IHByZXZpb3VzbHkgY2FwdHVyZWQsIHdlIGNyZWF0ZSBhIG1hcFxuICAgICAgICAgICAgLy8gY29udGFpbmluZyB0aGUgaGl0T2JqZWN0LCBhbmQgdGhlIGhpdC4gaGl0T2JqZWN0IGlzIHVzZWQgZm9yXG4gICAgICAgICAgICAvLyBmYXN0ZXIgYWNjZXNzLlxuICAgICAgICAgICAgaW50ZXJuYWwuY2FwdHVyZWRNYXAuc2V0KGlkLCBuZXcgTWFwKFtbaGl0LmV2ZW50T2JqZWN0LCBjYXB0dXJlRGF0YV1dKSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGV2ZW50LnRhcmdldC5zZXRQb2ludGVyQ2FwdHVyZShpZCk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHJlbGVhc2VQb2ludGVyQ2FwdHVyZSA9IGlkID0+IHtcbiAgICAgICAgICBjb25zdCBjYXB0dXJlcyA9IGludGVybmFsLmNhcHR1cmVkTWFwLmdldChpZCk7XG4gICAgICAgICAgaWYgKGNhcHR1cmVzKSB7XG4gICAgICAgICAgICByZWxlYXNlSW50ZXJuYWxQb2ludGVyQ2FwdHVyZShpbnRlcm5hbC5jYXB0dXJlZE1hcCwgaGl0LmV2ZW50T2JqZWN0LCBjYXB0dXJlcywgaWQpO1xuICAgICAgICAgIH1cbiAgICAgICAgfTtcblxuICAgICAgICAvLyBBZGQgbmF0aXZlIGV2ZW50IHByb3BzXG4gICAgICAgIGxldCBleHRyYWN0RXZlbnRQcm9wcyA9IHt9O1xuICAgICAgICAvLyBUaGlzIGl0ZXJhdGVzIG92ZXIgdGhlIGV2ZW50J3MgcHJvcGVydGllcyBpbmNsdWRpbmcgdGhlIGluaGVyaXRlZCBvbmVzLiBOYXRpdmUgUG9pbnRlckV2ZW50cyBoYXZlIG1vc3Qgb2YgdGhlaXIgcHJvcHMgYXMgZ2V0dGVycyB3aGljaCBhcmUgaW5oZXJpdGVkLCBidXQgcG9seWZpbGxlZCBQb2ludGVyRXZlbnRzIGhhdmUgdGhlbSBhbGwgYXMgdGhlaXIgb3duIHByb3BlcnRpZXMgKGkuZS4gbm90IGluaGVyaXRlZCkuIFdlIGNhbid0IHVzZSBPYmplY3Qua2V5cygpIG9yIE9iamVjdC5lbnRyaWVzKCkgYXMgdGhleSBvbmx5IHJldHVybiBcIm93blwiIHByb3BlcnRpZXM7IG5vciBPYmplY3QuZ2V0UHJvdG90eXBlT2YoZXZlbnQpIGFzIHRoYXQgKmRvZXNuJ3QqIHJldHVybiBcIm93blwiIHByb3BlcnRpZXMsIG9ubHkgaW5oZXJpdGVkIG9uZXMuXG4gICAgICAgIGZvciAobGV0IHByb3AgaW4gZXZlbnQpIHtcbiAgICAgICAgICBsZXQgcHJvcGVydHkgPSBldmVudFtwcm9wXTtcbiAgICAgICAgICAvLyBPbmx5IGNvcHkgb3ZlciBhdG9taWNzLCBsZWF2ZSBmdW5jdGlvbnMgYWxvbmUgYXMgdGhlc2Ugc2hvdWxkIGJlXG4gICAgICAgICAgLy8gY2FsbGVkIGFzIGV2ZW50Lm5hdGl2ZUV2ZW50LmZuKClcbiAgICAgICAgICBpZiAodHlwZW9mIHByb3BlcnR5ICE9PSAnZnVuY3Rpb24nKSBleHRyYWN0RXZlbnRQcm9wc1twcm9wXSA9IHByb3BlcnR5O1xuICAgICAgICB9XG4gICAgICAgIGxldCByYXljYXN0RXZlbnQgPSB7XG4gICAgICAgICAgLi4uaGl0LFxuICAgICAgICAgIC4uLmV4dHJhY3RFdmVudFByb3BzLFxuICAgICAgICAgIHBvaW50ZXIsXG4gICAgICAgICAgaW50ZXJzZWN0aW9ucyxcbiAgICAgICAgICBzdG9wcGVkOiBsb2NhbFN0YXRlLnN0b3BwZWQsXG4gICAgICAgICAgZGVsdGEsXG4gICAgICAgICAgdW5wcm9qZWN0ZWRQb2ludCxcbiAgICAgICAgICByYXk6IHJheWNhc3Rlci5yYXksXG4gICAgICAgICAgY2FtZXJhOiBjYW1lcmEsXG4gICAgICAgICAgLy8gSGlqYWNrIHN0b3BQcm9wYWdhdGlvbiwgd2hpY2gganVzdCBzZXRzIGEgZmxhZ1xuICAgICAgICAgIHN0b3BQcm9wYWdhdGlvbigpIHtcbiAgICAgICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9wbW5kcnMvcmVhY3QtdGhyZWUtZmliZXIvaXNzdWVzLzU5NlxuICAgICAgICAgICAgLy8gRXZlbnRzIGFyZSBub3QgYWxsb3dlZCB0byBzdG9wIHByb3BhZ2F0aW9uIGlmIHRoZSBwb2ludGVyIGhhcyBiZWVuIGNhcHR1cmVkXG4gICAgICAgICAgICBjb25zdCBjYXB0dXJlc0ZvclBvaW50ZXIgPSAncG9pbnRlcklkJyBpbiBldmVudCAmJiBpbnRlcm5hbC5jYXB0dXJlZE1hcC5nZXQoZXZlbnQucG9pbnRlcklkKTtcblxuICAgICAgICAgICAgLy8gV2Ugb25seSBhdXRob3JpemUgc3RvcFByb3BhZ2F0aW9uLi4uXG4gICAgICAgICAgICBpZiAoXG4gICAgICAgICAgICAvLyAuLi5pZiB0aGlzIHBvaW50ZXIgaGFzbid0IGJlZW4gY2FwdHVyZWRcbiAgICAgICAgICAgICFjYXB0dXJlc0ZvclBvaW50ZXIgfHxcbiAgICAgICAgICAgIC8vIC4uLiBvciBpZiB0aGUgaGl0IG9iamVjdCBpcyBjYXB0dXJpbmcgdGhlIHBvaW50ZXJcbiAgICAgICAgICAgIGNhcHR1cmVzRm9yUG9pbnRlci5oYXMoaGl0LmV2ZW50T2JqZWN0KSkge1xuICAgICAgICAgICAgICByYXljYXN0RXZlbnQuc3RvcHBlZCA9IGxvY2FsU3RhdGUuc3RvcHBlZCA9IHRydWU7XG4gICAgICAgICAgICAgIC8vIFByb3BhZ2F0aW9uIGlzIHN0b3BwZWQsIHJlbW92ZSBhbGwgb3RoZXIgaG92ZXIgcmVjb3Jkc1xuICAgICAgICAgICAgICAvLyBBbiBldmVudCBoYW5kbGVyIGlzIG9ubHkgYWxsb3dlZCB0byBmbHVzaCBvdGhlciBoYW5kbGVycyBpZiBpdCBpcyBob3ZlcmVkIGl0c2VsZlxuICAgICAgICAgICAgICBpZiAoaW50ZXJuYWwuaG92ZXJlZC5zaXplICYmIEFycmF5LmZyb20oaW50ZXJuYWwuaG92ZXJlZC52YWx1ZXMoKSkuZmluZChpID0+IGkuZXZlbnRPYmplY3QgPT09IGhpdC5ldmVudE9iamVjdCkpIHtcbiAgICAgICAgICAgICAgICAvLyBPYmplY3RzIGNhbm5vdCBmbHVzaCBvdXQgaGlnaGVyIHVwIG9iamVjdHMgdGhhdCBoYXZlIGFscmVhZHkgY2F1Z2h0IHRoZSBldmVudFxuICAgICAgICAgICAgICAgIGNvbnN0IGhpZ2hlciA9IGludGVyc2VjdGlvbnMuc2xpY2UoMCwgaW50ZXJzZWN0aW9ucy5pbmRleE9mKGhpdCkpO1xuICAgICAgICAgICAgICAgIGNhbmNlbFBvaW50ZXIoWy4uLmhpZ2hlciwgaGl0XSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LFxuICAgICAgICAgIC8vIHRoZXJlIHNob3VsZCBiZSBhIGRpc3RpbmN0aW9uIGJldHdlZW4gdGFyZ2V0IGFuZCBjdXJyZW50VGFyZ2V0XG4gICAgICAgICAgdGFyZ2V0OiB7XG4gICAgICAgICAgICBoYXNQb2ludGVyQ2FwdHVyZSxcbiAgICAgICAgICAgIHNldFBvaW50ZXJDYXB0dXJlLFxuICAgICAgICAgICAgcmVsZWFzZVBvaW50ZXJDYXB0dXJlXG4gICAgICAgICAgfSxcbiAgICAgICAgICBjdXJyZW50VGFyZ2V0OiB7XG4gICAgICAgICAgICBoYXNQb2ludGVyQ2FwdHVyZSxcbiAgICAgICAgICAgIHNldFBvaW50ZXJDYXB0dXJlLFxuICAgICAgICAgICAgcmVsZWFzZVBvaW50ZXJDYXB0dXJlXG4gICAgICAgICAgfSxcbiAgICAgICAgICBuYXRpdmVFdmVudDogZXZlbnRcbiAgICAgICAgfTtcblxuICAgICAgICAvLyBDYWxsIHN1YnNjcmliZXJzXG4gICAgICAgIGNhbGxiYWNrKHJheWNhc3RFdmVudCk7XG4gICAgICAgIC8vIEV2ZW50IGJ1YmJsaW5nIG1heSBiZSBpbnRlcnJ1cHRlZCBieSBzdG9wUHJvcGFnYXRpb25cbiAgICAgICAgaWYgKGxvY2FsU3RhdGUuc3RvcHBlZCA9PT0gdHJ1ZSkgYnJlYWs7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBpbnRlcnNlY3Rpb25zO1xuICB9XG4gIGZ1bmN0aW9uIGNhbmNlbFBvaW50ZXIoaW50ZXJzZWN0aW9ucykge1xuICAgIGNvbnN0IHtcbiAgICAgIGludGVybmFsXG4gICAgfSA9IHN0b3JlLmdldFN0YXRlKCk7XG4gICAgZm9yIChjb25zdCBob3ZlcmVkT2JqIG9mIGludGVybmFsLmhvdmVyZWQudmFsdWVzKCkpIHtcbiAgICAgIC8vIFdoZW4gbm8gb2JqZWN0cyB3ZXJlIGhpdCBvciB0aGUgdGhlIGhvdmVyZWQgb2JqZWN0IHdhc24ndCBmb3VuZCB1bmRlcm5lYXRoIHRoZSBjdXJzb3JcbiAgICAgIC8vIHdlIGNhbGwgb25Qb2ludGVyT3V0IGFuZCBkZWxldGUgdGhlIG9iamVjdCBmcm9tIHRoZSBob3ZlcmVkLWVsZW1lbnRzIG1hcFxuICAgICAgaWYgKCFpbnRlcnNlY3Rpb25zLmxlbmd0aCB8fCAhaW50ZXJzZWN0aW9ucy5maW5kKGhpdCA9PiBoaXQub2JqZWN0ID09PSBob3ZlcmVkT2JqLm9iamVjdCAmJiBoaXQuaW5kZXggPT09IGhvdmVyZWRPYmouaW5kZXggJiYgaGl0Lmluc3RhbmNlSWQgPT09IGhvdmVyZWRPYmouaW5zdGFuY2VJZCkpIHtcbiAgICAgICAgY29uc3QgZXZlbnRPYmplY3QgPSBob3ZlcmVkT2JqLmV2ZW50T2JqZWN0O1xuICAgICAgICBjb25zdCBpbnN0YW5jZSA9IGV2ZW50T2JqZWN0Ll9fcjNmO1xuICAgICAgICBjb25zdCBoYW5kbGVycyA9IGluc3RhbmNlID09IG51bGwgPyB2b2lkIDAgOiBpbnN0YW5jZS5oYW5kbGVycztcbiAgICAgICAgaW50ZXJuYWwuaG92ZXJlZC5kZWxldGUobWFrZUlkKGhvdmVyZWRPYmopKTtcbiAgICAgICAgaWYgKGluc3RhbmNlICE9IG51bGwgJiYgaW5zdGFuY2UuZXZlbnRDb3VudCkge1xuICAgICAgICAgIC8vIENsZWFyIG91dCBpbnRlcnNlY3RzLCB0aGV5IGFyZSBvdXRkYXRlZCBieSBub3dcbiAgICAgICAgICBjb25zdCBkYXRhID0ge1xuICAgICAgICAgICAgLi4uaG92ZXJlZE9iaixcbiAgICAgICAgICAgIGludGVyc2VjdGlvbnNcbiAgICAgICAgICB9O1xuICAgICAgICAgIGhhbmRsZXJzLm9uUG9pbnRlck91dCA9PSBudWxsID8gdm9pZCAwIDogaGFuZGxlcnMub25Qb2ludGVyT3V0KGRhdGEpO1xuICAgICAgICAgIGhhbmRsZXJzLm9uUG9pbnRlckxlYXZlID09IG51bGwgPyB2b2lkIDAgOiBoYW5kbGVycy5vblBvaW50ZXJMZWF2ZShkYXRhKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBwb2ludGVyTWlzc2VkKGV2ZW50LCBvYmplY3RzKSB7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBvYmplY3RzLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBpbnN0YW5jZSA9IG9iamVjdHNbaV0uX19yM2Y7XG4gICAgICBpbnN0YW5jZSA9PSBudWxsID8gdm9pZCAwIDogaW5zdGFuY2UuaGFuZGxlcnMub25Qb2ludGVyTWlzc2VkID09IG51bGwgPyB2b2lkIDAgOiBpbnN0YW5jZS5oYW5kbGVycy5vblBvaW50ZXJNaXNzZWQoZXZlbnQpO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBoYW5kbGVQb2ludGVyKG5hbWUpIHtcbiAgICAvLyBEZWFsIHdpdGggY2FuY2VsYXRpb25cbiAgICBzd2l0Y2ggKG5hbWUpIHtcbiAgICAgIGNhc2UgJ29uUG9pbnRlckxlYXZlJzpcbiAgICAgIGNhc2UgJ29uUG9pbnRlckNhbmNlbCc6XG4gICAgICAgIHJldHVybiAoKSA9PiBjYW5jZWxQb2ludGVyKFtdKTtcbiAgICAgIGNhc2UgJ29uTG9zdFBvaW50ZXJDYXB0dXJlJzpcbiAgICAgICAgcmV0dXJuIGV2ZW50ID0+IHtcbiAgICAgICAgICBjb25zdCB7XG4gICAgICAgICAgICBpbnRlcm5hbFxuICAgICAgICAgIH0gPSBzdG9yZS5nZXRTdGF0ZSgpO1xuICAgICAgICAgIGlmICgncG9pbnRlcklkJyBpbiBldmVudCAmJiBpbnRlcm5hbC5jYXB0dXJlZE1hcC5oYXMoZXZlbnQucG9pbnRlcklkKSkge1xuICAgICAgICAgICAgLy8gSWYgdGhlIG9iamVjdCBldmVudCBpbnRlcmZhY2UgaGFkIG9uTG9zdFBvaW50ZXJDYXB0dXJlLCB3ZSdkIGNhbGwgaXQgaGVyZSBvbiBldmVyeVxuICAgICAgICAgICAgLy8gb2JqZWN0IHRoYXQncyBnZXR0aW5nIHJlbW92ZWQuIFdlIGNhbGwgaXQgb24gdGhlIG5leHQgZnJhbWUgYmVjYXVzZSBvbkxvc3RQb2ludGVyQ2FwdHVyZVxuICAgICAgICAgICAgLy8gZmlyZXMgYmVmb3JlIG9uUG9pbnRlclVwLiBPdGhlcndpc2UgcG9pbnRlclVwIHdvdWxkIG5ldmVyIGJlIGNhbGxlZCBpZiB0aGUgZXZlbnQgZGlkbid0XG4gICAgICAgICAgICAvLyBoYXBwZW4gaW4gdGhlIG9iamVjdCBpdCBvcmlnaW5hdGVkIGZyb20sIGxlYXZpbmcgY29tcG9uZW50cyBpbiBhIGluLWJldHdlZW4gc3RhdGUuXG4gICAgICAgICAgICByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoKCkgPT4ge1xuICAgICAgICAgICAgICAvLyBPbmx5IHJlbGVhc2UgaWYgcG9pbnRlci11cCBkaWRuJ3QgZG8gaXQgYWxyZWFkeVxuICAgICAgICAgICAgICBpZiAoaW50ZXJuYWwuY2FwdHVyZWRNYXAuaGFzKGV2ZW50LnBvaW50ZXJJZCkpIHtcbiAgICAgICAgICAgICAgICBpbnRlcm5hbC5jYXB0dXJlZE1hcC5kZWxldGUoZXZlbnQucG9pbnRlcklkKTtcbiAgICAgICAgICAgICAgICBjYW5jZWxQb2ludGVyKFtdKTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuICAgIH1cblxuICAgIC8vIEFueSBvdGhlciBwb2ludGVyIGdvZXMgaGVyZSAuLi5cbiAgICByZXR1cm4gZnVuY3Rpb24gaGFuZGxlRXZlbnQoZXZlbnQpIHtcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgb25Qb2ludGVyTWlzc2VkLFxuICAgICAgICBpbnRlcm5hbFxuICAgICAgfSA9IHN0b3JlLmdldFN0YXRlKCk7XG5cbiAgICAgIC8vIHByZXBhcmVSYXkoZXZlbnQpXG4gICAgICBpbnRlcm5hbC5sYXN0RXZlbnQuY3VycmVudCA9IGV2ZW50O1xuXG4gICAgICAvLyBHZXQgZnJlc2ggaW50ZXJzZWN0c1xuICAgICAgY29uc3QgaXNQb2ludGVyTW92ZSA9IG5hbWUgPT09ICdvblBvaW50ZXJNb3ZlJztcbiAgICAgIGNvbnN0IGlzQ2xpY2tFdmVudCA9IG5hbWUgPT09ICdvbkNsaWNrJyB8fCBuYW1lID09PSAnb25Db250ZXh0TWVudScgfHwgbmFtZSA9PT0gJ29uRG91YmxlQ2xpY2snO1xuICAgICAgY29uc3QgZmlsdGVyID0gaXNQb2ludGVyTW92ZSA/IGZpbHRlclBvaW50ZXJFdmVudHMgOiB1bmRlZmluZWQ7XG4gICAgICBjb25zdCBoaXRzID0gaW50ZXJzZWN0KGV2ZW50LCBmaWx0ZXIpO1xuICAgICAgY29uc3QgZGVsdGEgPSBpc0NsaWNrRXZlbnQgPyBjYWxjdWxhdGVEaXN0YW5jZShldmVudCkgOiAwO1xuXG4gICAgICAvLyBTYXZlIGluaXRpYWwgY29vcmRpbmF0ZXMgb24gcG9pbnRlci1kb3duXG4gICAgICBpZiAobmFtZSA9PT0gJ29uUG9pbnRlckRvd24nKSB7XG4gICAgICAgIGludGVybmFsLmluaXRpYWxDbGljayA9IFtldmVudC5vZmZzZXRYLCBldmVudC5vZmZzZXRZXTtcbiAgICAgICAgaW50ZXJuYWwuaW5pdGlhbEhpdHMgPSBoaXRzLm1hcChoaXQgPT4gaGl0LmV2ZW50T2JqZWN0KTtcbiAgICAgIH1cblxuICAgICAgLy8gSWYgYSBjbGljayB5aWVsZHMgbm8gcmVzdWx0cywgcGFzcyBpdCBiYWNrIHRvIHRoZSB1c2VyIGFzIGEgbWlzc1xuICAgICAgLy8gTWlzc2VkIGV2ZW50cyBoYXZlIHRvIGNvbWUgZmlyc3QgaW4gb3JkZXIgdG8gZXN0YWJsaXNoIHVzZXItbGFuZCBzaWRlLWVmZmVjdCBjbGVhbiB1cFxuICAgICAgaWYgKGlzQ2xpY2tFdmVudCAmJiAhaGl0cy5sZW5ndGgpIHtcbiAgICAgICAgaWYgKGRlbHRhIDw9IDIpIHtcbiAgICAgICAgICBwb2ludGVyTWlzc2VkKGV2ZW50LCBpbnRlcm5hbC5pbnRlcmFjdGlvbik7XG4gICAgICAgICAgaWYgKG9uUG9pbnRlck1pc3NlZCkgb25Qb2ludGVyTWlzc2VkKGV2ZW50KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgLy8gVGFrZSBjYXJlIG9mIHVuaG92ZXJcbiAgICAgIGlmIChpc1BvaW50ZXJNb3ZlKSBjYW5jZWxQb2ludGVyKGhpdHMpO1xuICAgICAgZnVuY3Rpb24gb25JbnRlcnNlY3QoZGF0YSkge1xuICAgICAgICBjb25zdCBldmVudE9iamVjdCA9IGRhdGEuZXZlbnRPYmplY3Q7XG4gICAgICAgIGNvbnN0IGluc3RhbmNlID0gZXZlbnRPYmplY3QuX19yM2Y7XG4gICAgICAgIGNvbnN0IGhhbmRsZXJzID0gaW5zdGFuY2UgPT0gbnVsbCA/IHZvaWQgMCA6IGluc3RhbmNlLmhhbmRsZXJzO1xuXG4gICAgICAgIC8vIENoZWNrIHByZXNlbmNlIG9mIGhhbmRsZXJzXG4gICAgICAgIGlmICghKGluc3RhbmNlICE9IG51bGwgJiYgaW5zdGFuY2UuZXZlbnRDb3VudCkpIHJldHVybjtcblxuICAgICAgICAvKlxyXG4gICAgICAgIE1BWUJFIFRPRE8sIERFTEVURSBJRiBOT1Q6IFxyXG4gICAgICAgICAgQ2hlY2sgaWYgdGhlIG9iamVjdCBpcyBjYXB0dXJlZCwgY2FwdHVyZWQgZXZlbnRzIHNob3VsZCBub3QgaGF2ZSBpbnRlcnNlY3RzIHJ1bm5pbmcgaW4gcGFyYWxsZWxcclxuICAgICAgICAgIEJ1dCB3b3VsZG4ndCBpdCBiZSBiZXR0ZXIgdG8ganVzdCByZXBsYWNlIGNhcHR1cmVkTWFwIHdpdGggYSBzaW5nbGUgZW50cnk/XHJcbiAgICAgICAgICBBbHNvLCBhcmUgd2UgT0sgd2l0aCBzdHJhaWdodCB1cCBtYWtpbmcgcGlja2luZyB1cCBtdWx0aXBsZSBvYmplY3RzIGltcG9zc2libGU/XHJcbiAgICAgICAgICBcclxuICAgICAgICBjb25zdCBwb2ludGVySWQgPSAoZGF0YSBhcyBUaHJlZUV2ZW50PFBvaW50ZXJFdmVudD4pLnBvaW50ZXJJZCAgICAgICAgXHJcbiAgICAgICAgaWYgKHBvaW50ZXJJZCAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgICBjb25zdCBjYXB0dXJlZE1lc2hTZXQgPSBpbnRlcm5hbC5jYXB0dXJlZE1hcC5nZXQocG9pbnRlcklkKVxyXG4gICAgICAgICAgaWYgKGNhcHR1cmVkTWVzaFNldCkge1xyXG4gICAgICAgICAgICBjb25zdCBjYXB0dXJlZCA9IGNhcHR1cmVkTWVzaFNldC5nZXQoZXZlbnRPYmplY3QpXHJcbiAgICAgICAgICAgIGlmIChjYXB0dXJlZCAmJiBjYXB0dXJlZC5sb2NhbFN0YXRlLnN0b3BwZWQpIHJldHVyblxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0qL1xuXG4gICAgICAgIGlmIChpc1BvaW50ZXJNb3ZlKSB7XG4gICAgICAgICAgLy8gTW92ZSBldmVudCAuLi5cbiAgICAgICAgICBpZiAoaGFuZGxlcnMub25Qb2ludGVyT3ZlciB8fCBoYW5kbGVycy5vblBvaW50ZXJFbnRlciB8fCBoYW5kbGVycy5vblBvaW50ZXJPdXQgfHwgaGFuZGxlcnMub25Qb2ludGVyTGVhdmUpIHtcbiAgICAgICAgICAgIC8vIFdoZW4gZW50ZXIgb3Igb3V0IGlzIHByZXNlbnQgdGFrZSBjYXJlIG9mIGhvdmVyLXN0YXRlXG4gICAgICAgICAgICBjb25zdCBpZCA9IG1ha2VJZChkYXRhKTtcbiAgICAgICAgICAgIGNvbnN0IGhvdmVyZWRJdGVtID0gaW50ZXJuYWwuaG92ZXJlZC5nZXQoaWQpO1xuICAgICAgICAgICAgaWYgKCFob3ZlcmVkSXRlbSkge1xuICAgICAgICAgICAgICAvLyBJZiB0aGUgb2JqZWN0IHdhc24ndCBwcmV2aW91c2x5IGhvdmVyZWQsIGJvb2sgaXQgYW5kIGNhbGwgaXRzIGhhbmRsZXJcbiAgICAgICAgICAgICAgaW50ZXJuYWwuaG92ZXJlZC5zZXQoaWQsIGRhdGEpO1xuICAgICAgICAgICAgICBoYW5kbGVycy5vblBvaW50ZXJPdmVyID09IG51bGwgPyB2b2lkIDAgOiBoYW5kbGVycy5vblBvaW50ZXJPdmVyKGRhdGEpO1xuICAgICAgICAgICAgICBoYW5kbGVycy5vblBvaW50ZXJFbnRlciA9PSBudWxsID8gdm9pZCAwIDogaGFuZGxlcnMub25Qb2ludGVyRW50ZXIoZGF0YSk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKGhvdmVyZWRJdGVtLnN0b3BwZWQpIHtcbiAgICAgICAgICAgICAgLy8gSWYgdGhlIG9iamVjdCB3YXMgcHJldmlvdXNseSBob3ZlcmVkIGFuZCBzdG9wcGVkLCB3ZSBzaG91bGRuJ3QgYWxsb3cgb3RoZXIgaXRlbXMgdG8gcHJvY2VlZFxuICAgICAgICAgICAgICBkYXRhLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgICAvLyBDYWxsIG1vdXNlIG1vdmVcbiAgICAgICAgICBoYW5kbGVycy5vblBvaW50ZXJNb3ZlID09IG51bGwgPyB2b2lkIDAgOiBoYW5kbGVycy5vblBvaW50ZXJNb3ZlKGRhdGEpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIEFsbCBvdGhlciBldmVudHMgLi4uXG4gICAgICAgICAgY29uc3QgaGFuZGxlciA9IGhhbmRsZXJzW25hbWVdO1xuICAgICAgICAgIGlmIChoYW5kbGVyKSB7XG4gICAgICAgICAgICAvLyBGb3J3YXJkIGFsbCBldmVudHMgYmFjayB0byB0aGVpciByZXNwZWN0aXZlIGhhbmRsZXJzIHdpdGggdGhlIGV4Y2VwdGlvbiBvZiBjbGljayBldmVudHMsXG4gICAgICAgICAgICAvLyB3aGljaCBtdXN0IHVzZSB0aGUgaW5pdGlhbCB0YXJnZXRcbiAgICAgICAgICAgIGlmICghaXNDbGlja0V2ZW50IHx8IGludGVybmFsLmluaXRpYWxIaXRzLmluY2x1ZGVzKGV2ZW50T2JqZWN0KSkge1xuICAgICAgICAgICAgICAvLyBNaXNzZWQgZXZlbnRzIGhhdmUgdG8gY29tZSBmaXJzdFxuICAgICAgICAgICAgICBwb2ludGVyTWlzc2VkKGV2ZW50LCBpbnRlcm5hbC5pbnRlcmFjdGlvbi5maWx0ZXIob2JqZWN0ID0+ICFpbnRlcm5hbC5pbml0aWFsSGl0cy5pbmNsdWRlcyhvYmplY3QpKSk7XG4gICAgICAgICAgICAgIC8vIE5vdyBjYWxsIHRoZSBoYW5kbGVyXG4gICAgICAgICAgICAgIGhhbmRsZXIoZGF0YSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIFRyaWdnZXIgb25Qb2ludGVyTWlzc2VkIG9uIGFsbCBlbGVtZW50cyB0aGF0IGhhdmUgcG9pbnRlciBvdmVyL291dCBoYW5kbGVycywgYnV0IG5vdCBjbGljayBhbmQgd2VyZW4ndCBoaXRcbiAgICAgICAgICAgIGlmIChpc0NsaWNrRXZlbnQgJiYgaW50ZXJuYWwuaW5pdGlhbEhpdHMuaW5jbHVkZXMoZXZlbnRPYmplY3QpKSB7XG4gICAgICAgICAgICAgIHBvaW50ZXJNaXNzZWQoZXZlbnQsIGludGVybmFsLmludGVyYWN0aW9uLmZpbHRlcihvYmplY3QgPT4gIWludGVybmFsLmluaXRpYWxIaXRzLmluY2x1ZGVzKG9iamVjdCkpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGhhbmRsZUludGVyc2VjdHMoaGl0cywgZXZlbnQsIGRlbHRhLCBvbkludGVyc2VjdCk7XG4gICAgfTtcbiAgfVxuICByZXR1cm4ge1xuICAgIGhhbmRsZVBvaW50ZXJcbiAgfTtcbn1cblxuLy8gS2V5cyB0aGF0IHNob3VsZG4ndCBiZSBjb3BpZWQgYmV0d2VlbiBSM0Ygc3RvcmVzXG5jb25zdCBwcml2YXRlS2V5cyA9IFsnc2V0JywgJ2dldCcsICdzZXRTaXplJywgJ3NldEZyYW1lbG9vcCcsICdzZXREcHInLCAnZXZlbnRzJywgJ2ludmFsaWRhdGUnLCAnYWR2YW5jZScsICdzaXplJywgJ3ZpZXdwb3J0J107XG5jb25zdCBpc1JlbmRlcmVyID0gZGVmID0+ICEhKGRlZiAhPSBudWxsICYmIGRlZi5yZW5kZXIpO1xuY29uc3QgY29udGV4dCA9IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVDb250ZXh0KG51bGwpO1xuY29uc3QgY3JlYXRlU3RvcmUgPSAoaW52YWxpZGF0ZSwgYWR2YW5jZSkgPT4ge1xuICBjb25zdCByb290U3RhdGUgPSBjcmVhdGUoKHNldCwgZ2V0KSA9PiB7XG4gICAgY29uc3QgcG9zaXRpb24gPSBuZXcgVEhSRUUuVmVjdG9yMygpO1xuICAgIGNvbnN0IGRlZmF1bHRUYXJnZXQgPSBuZXcgVEhSRUUuVmVjdG9yMygpO1xuICAgIGNvbnN0IHRlbXBUYXJnZXQgPSBuZXcgVEhSRUUuVmVjdG9yMygpO1xuICAgIGZ1bmN0aW9uIGdldEN1cnJlbnRWaWV3cG9ydChjYW1lcmEgPSBnZXQoKS5jYW1lcmEsIHRhcmdldCA9IGRlZmF1bHRUYXJnZXQsIHNpemUgPSBnZXQoKS5zaXplKSB7XG4gICAgICBjb25zdCB7XG4gICAgICAgIHdpZHRoLFxuICAgICAgICBoZWlnaHQsXG4gICAgICAgIHRvcCxcbiAgICAgICAgbGVmdFxuICAgICAgfSA9IHNpemU7XG4gICAgICBjb25zdCBhc3BlY3QgPSB3aWR0aCAvIGhlaWdodDtcbiAgICAgIGlmICh0YXJnZXQgaW5zdGFuY2VvZiBUSFJFRS5WZWN0b3IzKSB0ZW1wVGFyZ2V0LmNvcHkodGFyZ2V0KTtlbHNlIHRlbXBUYXJnZXQuc2V0KC4uLnRhcmdldCk7XG4gICAgICBjb25zdCBkaXN0YW5jZSA9IGNhbWVyYS5nZXRXb3JsZFBvc2l0aW9uKHBvc2l0aW9uKS5kaXN0YW5jZVRvKHRlbXBUYXJnZXQpO1xuICAgICAgaWYgKGlzT3J0aG9ncmFwaGljQ2FtZXJhKGNhbWVyYSkpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICB3aWR0aDogd2lkdGggLyBjYW1lcmEuem9vbSxcbiAgICAgICAgICBoZWlnaHQ6IGhlaWdodCAvIGNhbWVyYS56b29tLFxuICAgICAgICAgIHRvcCxcbiAgICAgICAgICBsZWZ0LFxuICAgICAgICAgIGZhY3RvcjogMSxcbiAgICAgICAgICBkaXN0YW5jZSxcbiAgICAgICAgICBhc3BlY3RcbiAgICAgICAgfTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGZvdiA9IGNhbWVyYS5mb3YgKiBNYXRoLlBJIC8gMTgwOyAvLyBjb252ZXJ0IHZlcnRpY2FsIGZvdiB0byByYWRpYW5zXG4gICAgICAgIGNvbnN0IGggPSAyICogTWF0aC50YW4oZm92IC8gMikgKiBkaXN0YW5jZTsgLy8gdmlzaWJsZSBoZWlnaHRcbiAgICAgICAgY29uc3QgdyA9IGggKiAod2lkdGggLyBoZWlnaHQpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHdpZHRoOiB3LFxuICAgICAgICAgIGhlaWdodDogaCxcbiAgICAgICAgICB0b3AsXG4gICAgICAgICAgbGVmdCxcbiAgICAgICAgICBmYWN0b3I6IHdpZHRoIC8gdyxcbiAgICAgICAgICBkaXN0YW5jZSxcbiAgICAgICAgICBhc3BlY3RcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9XG4gICAgbGV0IHBlcmZvcm1hbmNlVGltZW91dCA9IHVuZGVmaW5lZDtcbiAgICBjb25zdCBzZXRQZXJmb3JtYW5jZUN1cnJlbnQgPSBjdXJyZW50ID0+IHNldChzdGF0ZSA9PiAoe1xuICAgICAgcGVyZm9ybWFuY2U6IHtcbiAgICAgICAgLi4uc3RhdGUucGVyZm9ybWFuY2UsXG4gICAgICAgIGN1cnJlbnRcbiAgICAgIH1cbiAgICB9KSk7XG4gICAgY29uc3QgcG9pbnRlciA9IG5ldyBUSFJFRS5WZWN0b3IyKCk7XG4gICAgY29uc3Qgcm9vdFN0YXRlID0ge1xuICAgICAgc2V0LFxuICAgICAgZ2V0LFxuICAgICAgLy8gTW9jayBvYmplY3RzIHRoYXQgaGF2ZSB0byBiZSBjb25maWd1cmVkXG4gICAgICBnbDogbnVsbCxcbiAgICAgIGNhbWVyYTogbnVsbCxcbiAgICAgIHJheWNhc3RlcjogbnVsbCxcbiAgICAgIGV2ZW50czoge1xuICAgICAgICBwcmlvcml0eTogMSxcbiAgICAgICAgZW5hYmxlZDogdHJ1ZSxcbiAgICAgICAgY29ubmVjdGVkOiBmYWxzZVxuICAgICAgfSxcbiAgICAgIHhyOiBudWxsLFxuICAgICAgc2NlbmU6IG51bGwsXG4gICAgICBpbnZhbGlkYXRlOiAoZnJhbWVzID0gMSkgPT4gaW52YWxpZGF0ZShnZXQoKSwgZnJhbWVzKSxcbiAgICAgIGFkdmFuY2U6ICh0aW1lc3RhbXAsIHJ1bkdsb2JhbEVmZmVjdHMpID0+IGFkdmFuY2UodGltZXN0YW1wLCBydW5HbG9iYWxFZmZlY3RzLCBnZXQoKSksXG4gICAgICBsZWdhY3k6IGZhbHNlLFxuICAgICAgbGluZWFyOiBmYWxzZSxcbiAgICAgIGZsYXQ6IGZhbHNlLFxuICAgICAgY29udHJvbHM6IG51bGwsXG4gICAgICBjbG9jazogbmV3IFRIUkVFLkNsb2NrKCksXG4gICAgICBwb2ludGVyLFxuICAgICAgbW91c2U6IHBvaW50ZXIsXG4gICAgICBmcmFtZWxvb3A6ICdhbHdheXMnLFxuICAgICAgb25Qb2ludGVyTWlzc2VkOiB1bmRlZmluZWQsXG4gICAgICBwZXJmb3JtYW5jZToge1xuICAgICAgICBjdXJyZW50OiAxLFxuICAgICAgICBtaW46IDAuNSxcbiAgICAgICAgbWF4OiAxLFxuICAgICAgICBkZWJvdW5jZTogMjAwLFxuICAgICAgICByZWdyZXNzOiAoKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc3RhdGUgPSBnZXQoKTtcbiAgICAgICAgICAvLyBDbGVhciB0aW1lb3V0XG4gICAgICAgICAgaWYgKHBlcmZvcm1hbmNlVGltZW91dCkgY2xlYXJUaW1lb3V0KHBlcmZvcm1hbmNlVGltZW91dCk7XG4gICAgICAgICAgLy8gU2V0IGxvd2VyIGJvdW5kIHBlcmZvcm1hbmNlXG4gICAgICAgICAgaWYgKHN0YXRlLnBlcmZvcm1hbmNlLmN1cnJlbnQgIT09IHN0YXRlLnBlcmZvcm1hbmNlLm1pbikgc2V0UGVyZm9ybWFuY2VDdXJyZW50KHN0YXRlLnBlcmZvcm1hbmNlLm1pbik7XG4gICAgICAgICAgLy8gR28gYmFjayB0byB1cHBlciBib3VuZCBwZXJmb3JtYW5jZSBhZnRlciBhIHdoaWxlIHVubGVzcyBzb21ldGhpbmcgcmVncmVzc2VzIG1lYW53aGlsZVxuICAgICAgICAgIHBlcmZvcm1hbmNlVGltZW91dCA9IHNldFRpbWVvdXQoKCkgPT4gc2V0UGVyZm9ybWFuY2VDdXJyZW50KGdldCgpLnBlcmZvcm1hbmNlLm1heCksIHN0YXRlLnBlcmZvcm1hbmNlLmRlYm91bmNlKTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgd2lkdGg6IDAsXG4gICAgICAgIGhlaWdodDogMCxcbiAgICAgICAgdG9wOiAwLFxuICAgICAgICBsZWZ0OiAwLFxuICAgICAgICB1cGRhdGVTdHlsZTogZmFsc2VcbiAgICAgIH0sXG4gICAgICB2aWV3cG9ydDoge1xuICAgICAgICBpbml0aWFsRHByOiAwLFxuICAgICAgICBkcHI6IDAsXG4gICAgICAgIHdpZHRoOiAwLFxuICAgICAgICBoZWlnaHQ6IDAsXG4gICAgICAgIHRvcDogMCxcbiAgICAgICAgbGVmdDogMCxcbiAgICAgICAgYXNwZWN0OiAwLFxuICAgICAgICBkaXN0YW5jZTogMCxcbiAgICAgICAgZmFjdG9yOiAwLFxuICAgICAgICBnZXRDdXJyZW50Vmlld3BvcnRcbiAgICAgIH0sXG4gICAgICBzZXRFdmVudHM6IGV2ZW50cyA9PiBzZXQoc3RhdGUgPT4gKHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIGV2ZW50czoge1xuICAgICAgICAgIC4uLnN0YXRlLmV2ZW50cyxcbiAgICAgICAgICAuLi5ldmVudHNcbiAgICAgICAgfVxuICAgICAgfSkpLFxuICAgICAgc2V0U2l6ZTogKHdpZHRoLCBoZWlnaHQsIHVwZGF0ZVN0eWxlLCB0b3AsIGxlZnQpID0+IHtcbiAgICAgICAgY29uc3QgY2FtZXJhID0gZ2V0KCkuY2FtZXJhO1xuICAgICAgICBjb25zdCBzaXplID0ge1xuICAgICAgICAgIHdpZHRoLFxuICAgICAgICAgIGhlaWdodCxcbiAgICAgICAgICB0b3A6IHRvcCB8fCAwLFxuICAgICAgICAgIGxlZnQ6IGxlZnQgfHwgMCxcbiAgICAgICAgICB1cGRhdGVTdHlsZVxuICAgICAgICB9O1xuICAgICAgICBzZXQoc3RhdGUgPT4gKHtcbiAgICAgICAgICBzaXplLFxuICAgICAgICAgIHZpZXdwb3J0OiB7XG4gICAgICAgICAgICAuLi5zdGF0ZS52aWV3cG9ydCxcbiAgICAgICAgICAgIC4uLmdldEN1cnJlbnRWaWV3cG9ydChjYW1lcmEsIGRlZmF1bHRUYXJnZXQsIHNpemUpXG4gICAgICAgICAgfVxuICAgICAgICB9KSk7XG4gICAgICB9LFxuICAgICAgc2V0RHByOiBkcHIgPT4gc2V0KHN0YXRlID0+IHtcbiAgICAgICAgY29uc3QgcmVzb2x2ZWQgPSBjYWxjdWxhdGVEcHIoZHByKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICB2aWV3cG9ydDoge1xuICAgICAgICAgICAgLi4uc3RhdGUudmlld3BvcnQsXG4gICAgICAgICAgICBkcHI6IHJlc29sdmVkLFxuICAgICAgICAgICAgaW5pdGlhbERwcjogc3RhdGUudmlld3BvcnQuaW5pdGlhbERwciB8fCByZXNvbHZlZFxuICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgIH0pLFxuICAgICAgc2V0RnJhbWVsb29wOiAoZnJhbWVsb29wID0gJ2Fsd2F5cycpID0+IHtcbiAgICAgICAgY29uc3QgY2xvY2sgPSBnZXQoKS5jbG9jaztcblxuICAgICAgICAvLyBpZiBmcmFtZWxvb3AgPT09IFwibmV2ZXJcIiBjbG9jay5lbGFwc2VkVGltZSBpcyB1cGRhdGVkIHVzaW5nIGFkdmFuY2UodGltZXN0YW1wKVxuICAgICAgICBjbG9jay5zdG9wKCk7XG4gICAgICAgIGNsb2NrLmVsYXBzZWRUaW1lID0gMDtcbiAgICAgICAgaWYgKGZyYW1lbG9vcCAhPT0gJ25ldmVyJykge1xuICAgICAgICAgIGNsb2NrLnN0YXJ0KCk7XG4gICAgICAgICAgY2xvY2suZWxhcHNlZFRpbWUgPSAwO1xuICAgICAgICB9XG4gICAgICAgIHNldCgoKSA9PiAoe1xuICAgICAgICAgIGZyYW1lbG9vcFxuICAgICAgICB9KSk7XG4gICAgICB9LFxuICAgICAgcHJldmlvdXNSb290OiB1bmRlZmluZWQsXG4gICAgICBpbnRlcm5hbDoge1xuICAgICAgICBhY3RpdmU6IGZhbHNlLFxuICAgICAgICBwcmlvcml0eTogMCxcbiAgICAgICAgZnJhbWVzOiAwLFxuICAgICAgICBsYXN0RXZlbnQ6IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVSZWYoKSxcbiAgICAgICAgaW50ZXJhY3Rpb246IFtdLFxuICAgICAgICBob3ZlcmVkOiBuZXcgTWFwKCksXG4gICAgICAgIHN1YnNjcmliZXJzOiBbXSxcbiAgICAgICAgaW5pdGlhbENsaWNrOiBbMCwgMF0sXG4gICAgICAgIGluaXRpYWxIaXRzOiBbXSxcbiAgICAgICAgY2FwdHVyZWRNYXA6IG5ldyBNYXAoKSxcbiAgICAgICAgc3Vic2NyaWJlOiAocmVmLCBwcmlvcml0eSwgc3RvcmUpID0+IHtcbiAgICAgICAgICBjb25zdCBpbnRlcm5hbCA9IGdldCgpLmludGVybmFsO1xuICAgICAgICAgIC8vIElmIHRoaXMgc3Vic2NyaXB0aW9uIHdhcyBnaXZlbiBhIHByaW9yaXR5LCBpdCB0YWtlcyByZW5kZXJpbmcgaW50byBpdHMgb3duIGhhbmRzXG4gICAgICAgICAgLy8gRm9yIHRoYXQgcmVhc29uIHdlIHN3aXRjaCBvZmYgYXV0b21hdGljIHJlbmRlcmluZyBhbmQgaW5jcmVhc2UgdGhlIG1hbnVhbCBmbGFnXG4gICAgICAgICAgLy8gQXMgbG9uZyBhcyB0aGlzIGZsYWcgaXMgcG9zaXRpdmUgdGhlcmUgY2FuIGJlIG5vIGludGVybmFsIHJlbmRlcmluZyBhdCBhbGxcbiAgICAgICAgICAvLyBiZWNhdXNlIHRoZXJlIGNvdWxkIGJlIG11bHRpcGxlIHJlbmRlciBzdWJzY3JpcHRpb25zXG4gICAgICAgICAgaW50ZXJuYWwucHJpb3JpdHkgPSBpbnRlcm5hbC5wcmlvcml0eSArIChwcmlvcml0eSA+IDAgPyAxIDogMCk7XG4gICAgICAgICAgaW50ZXJuYWwuc3Vic2NyaWJlcnMucHVzaCh7XG4gICAgICAgICAgICByZWYsXG4gICAgICAgICAgICBwcmlvcml0eSxcbiAgICAgICAgICAgIHN0b3JlXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgLy8gUmVnaXN0ZXIgc3Vic2NyaWJlciBhbmQgc29ydCBsYXllcnMgZnJvbSBsb3dlc3QgdG8gaGlnaGVzdCwgbWVhbmluZyxcbiAgICAgICAgICAvLyBoaWdoZXN0IHByaW9yaXR5IHJlbmRlcnMgbGFzdCAob24gdG9wIG9mIHRoZSBvdGhlciBmcmFtZXMpXG4gICAgICAgICAgaW50ZXJuYWwuc3Vic2NyaWJlcnMgPSBpbnRlcm5hbC5zdWJzY3JpYmVycy5zb3J0KChhLCBiKSA9PiBhLnByaW9yaXR5IC0gYi5wcmlvcml0eSk7XG4gICAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGludGVybmFsID0gZ2V0KCkuaW50ZXJuYWw7XG4gICAgICAgICAgICBpZiAoaW50ZXJuYWwgIT0gbnVsbCAmJiBpbnRlcm5hbC5zdWJzY3JpYmVycykge1xuICAgICAgICAgICAgICAvLyBEZWNyZWFzZSBtYW51YWwgZmxhZyBpZiB0aGlzIHN1YnNjcmlwdGlvbiBoYWQgYSBwcmlvcml0eVxuICAgICAgICAgICAgICBpbnRlcm5hbC5wcmlvcml0eSA9IGludGVybmFsLnByaW9yaXR5IC0gKHByaW9yaXR5ID4gMCA/IDEgOiAwKTtcbiAgICAgICAgICAgICAgLy8gUmVtb3ZlIHN1YnNjcmliZXIgZnJvbSBsaXN0XG4gICAgICAgICAgICAgIGludGVybmFsLnN1YnNjcmliZXJzID0gaW50ZXJuYWwuc3Vic2NyaWJlcnMuZmlsdGVyKHMgPT4gcy5yZWYgIT09IHJlZik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG4gICAgcmV0dXJuIHJvb3RTdGF0ZTtcbiAgfSk7XG4gIGNvbnN0IHN0YXRlID0gcm9vdFN0YXRlLmdldFN0YXRlKCk7XG4gIGxldCBvbGRTaXplID0gc3RhdGUuc2l6ZTtcbiAgbGV0IG9sZERwciA9IHN0YXRlLnZpZXdwb3J0LmRwcjtcbiAgbGV0IG9sZENhbWVyYSA9IHN0YXRlLmNhbWVyYTtcbiAgcm9vdFN0YXRlLnN1YnNjcmliZSgoKSA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgY2FtZXJhLFxuICAgICAgc2l6ZSxcbiAgICAgIHZpZXdwb3J0LFxuICAgICAgZ2wsXG4gICAgICBzZXRcbiAgICB9ID0gcm9vdFN0YXRlLmdldFN0YXRlKCk7XG5cbiAgICAvLyBSZXNpemUgY2FtZXJhIGFuZCByZW5kZXJlciBvbiBjaGFuZ2VzIHRvIHNpemUgYW5kIHBpeGVscmF0aW9cbiAgICBpZiAoc2l6ZSAhPT0gb2xkU2l6ZSB8fCB2aWV3cG9ydC5kcHIgIT09IG9sZERwcikge1xuICAgICAgdmFyIF9zaXplJHVwZGF0ZVN0eWxlO1xuICAgICAgb2xkU2l6ZSA9IHNpemU7XG4gICAgICBvbGREcHIgPSB2aWV3cG9ydC5kcHI7XG4gICAgICAvLyBVcGRhdGUgY2FtZXJhICYgcmVuZGVyZXJcbiAgICAgIHVwZGF0ZUNhbWVyYShjYW1lcmEsIHNpemUpO1xuICAgICAgZ2wuc2V0UGl4ZWxSYXRpbyh2aWV3cG9ydC5kcHIpO1xuICAgICAgY29uc3QgdXBkYXRlU3R5bGUgPSAoX3NpemUkdXBkYXRlU3R5bGUgPSBzaXplLnVwZGF0ZVN0eWxlKSAhPSBudWxsID8gX3NpemUkdXBkYXRlU3R5bGUgOiB0eXBlb2YgSFRNTENhbnZhc0VsZW1lbnQgIT09ICd1bmRlZmluZWQnICYmIGdsLmRvbUVsZW1lbnQgaW5zdGFuY2VvZiBIVE1MQ2FudmFzRWxlbWVudDtcbiAgICAgIGdsLnNldFNpemUoc2l6ZS53aWR0aCwgc2l6ZS5oZWlnaHQsIHVwZGF0ZVN0eWxlKTtcbiAgICB9XG5cbiAgICAvLyBVcGRhdGUgdmlld3BvcnQgb25jZSB0aGUgY2FtZXJhIGNoYW5nZXNcbiAgICBpZiAoY2FtZXJhICE9PSBvbGRDYW1lcmEpIHtcbiAgICAgIG9sZENhbWVyYSA9IGNhbWVyYTtcbiAgICAgIC8vIFVwZGF0ZSB2aWV3cG9ydFxuICAgICAgc2V0KHN0YXRlID0+ICh7XG4gICAgICAgIHZpZXdwb3J0OiB7XG4gICAgICAgICAgLi4uc3RhdGUudmlld3BvcnQsXG4gICAgICAgICAgLi4uc3RhdGUudmlld3BvcnQuZ2V0Q3VycmVudFZpZXdwb3J0KGNhbWVyYSlcbiAgICAgICAgfVxuICAgICAgfSkpO1xuICAgIH1cbiAgfSk7XG5cbiAgLy8gSW52YWxpZGF0ZSBvbiBhbnkgY2hhbmdlXG4gIHJvb3RTdGF0ZS5zdWJzY3JpYmUoc3RhdGUgPT4gaW52YWxpZGF0ZShzdGF0ZSkpO1xuXG4gIC8vIFJldHVybiByb290IHN0YXRlXG4gIHJldHVybiByb290U3RhdGU7XG59O1xuXG5mdW5jdGlvbiBjcmVhdGVTdWJzKGNhbGxiYWNrLCBzdWJzKSB7XG4gIGNvbnN0IHN1YiA9IHtcbiAgICBjYWxsYmFja1xuICB9O1xuICBzdWJzLmFkZChzdWIpO1xuICByZXR1cm4gKCkgPT4gdm9pZCBzdWJzLmRlbGV0ZShzdWIpO1xufVxubGV0IGk7XG5sZXQgZ2xvYmFsRWZmZWN0cyA9IG5ldyBTZXQoKTtcbmxldCBnbG9iYWxBZnRlckVmZmVjdHMgPSBuZXcgU2V0KCk7XG5sZXQgZ2xvYmFsVGFpbEVmZmVjdHMgPSBuZXcgU2V0KCk7XG5cbi8qKlxyXG4gKiBBZGRzIGEgZ2xvYmFsIHJlbmRlciBjYWxsYmFjayB3aGljaCBpcyBjYWxsZWQgZWFjaCBmcmFtZS5cclxuICogQHNlZSBodHRwczovL2RvY3MucG1uZC5ycy9yZWFjdC10aHJlZS1maWJlci9hcGkvYWRkaXRpb25hbC1leHBvcnRzI2FkZEVmZmVjdFxyXG4gKi9cbmNvbnN0IGFkZEVmZmVjdCA9IGNhbGxiYWNrID0+IGNyZWF0ZVN1YnMoY2FsbGJhY2ssIGdsb2JhbEVmZmVjdHMpO1xuXG4vKipcclxuICogQWRkcyBhIGdsb2JhbCBhZnRlci1yZW5kZXIgY2FsbGJhY2sgd2hpY2ggaXMgY2FsbGVkIGVhY2ggZnJhbWUuXHJcbiAqIEBzZWUgaHR0cHM6Ly9kb2NzLnBtbmQucnMvcmVhY3QtdGhyZWUtZmliZXIvYXBpL2FkZGl0aW9uYWwtZXhwb3J0cyNhZGRBZnRlckVmZmVjdFxyXG4gKi9cbmNvbnN0IGFkZEFmdGVyRWZmZWN0ID0gY2FsbGJhY2sgPT4gY3JlYXRlU3VicyhjYWxsYmFjaywgZ2xvYmFsQWZ0ZXJFZmZlY3RzKTtcblxuLyoqXHJcbiAqIEFkZHMgYSBnbG9iYWwgY2FsbGJhY2sgd2hpY2ggaXMgY2FsbGVkIHdoZW4gcmVuZGVyaW5nIHN0b3BzLlxyXG4gKiBAc2VlIGh0dHBzOi8vZG9jcy5wbW5kLnJzL3JlYWN0LXRocmVlLWZpYmVyL2FwaS9hZGRpdGlvbmFsLWV4cG9ydHMjYWRkVGFpbFxyXG4gKi9cbmNvbnN0IGFkZFRhaWwgPSBjYWxsYmFjayA9PiBjcmVhdGVTdWJzKGNhbGxiYWNrLCBnbG9iYWxUYWlsRWZmZWN0cyk7XG5mdW5jdGlvbiBydW4oZWZmZWN0cywgdGltZXN0YW1wKSB7XG4gIGlmICghZWZmZWN0cy5zaXplKSByZXR1cm47XG4gIGZvciAoY29uc3Qge1xuICAgIGNhbGxiYWNrXG4gIH0gb2YgZWZmZWN0cy52YWx1ZXMoKSkge1xuICAgIGNhbGxiYWNrKHRpbWVzdGFtcCk7XG4gIH1cbn1cbmZ1bmN0aW9uIGZsdXNoR2xvYmFsRWZmZWN0cyh0eXBlLCB0aW1lc3RhbXApIHtcbiAgc3dpdGNoICh0eXBlKSB7XG4gICAgY2FzZSAnYmVmb3JlJzpcbiAgICAgIHJldHVybiBydW4oZ2xvYmFsRWZmZWN0cywgdGltZXN0YW1wKTtcbiAgICBjYXNlICdhZnRlcic6XG4gICAgICByZXR1cm4gcnVuKGdsb2JhbEFmdGVyRWZmZWN0cywgdGltZXN0YW1wKTtcbiAgICBjYXNlICd0YWlsJzpcbiAgICAgIHJldHVybiBydW4oZ2xvYmFsVGFpbEVmZmVjdHMsIHRpbWVzdGFtcCk7XG4gIH1cbn1cbmxldCBzdWJzY3JpYmVycztcbmxldCBzdWJzY3JpcHRpb247XG5mdW5jdGlvbiByZW5kZXIkMSh0aW1lc3RhbXAsIHN0YXRlLCBmcmFtZSkge1xuICAvLyBSdW4gbG9jYWwgZWZmZWN0c1xuICBsZXQgZGVsdGEgPSBzdGF0ZS5jbG9jay5nZXREZWx0YSgpO1xuICAvLyBJbiBmcmFtZWxvb3A9J25ldmVyJyBtb2RlLCBjbG9jayB0aW1lcyBhcmUgdXBkYXRlZCB1c2luZyB0aGUgcHJvdmlkZWQgdGltZXN0YW1wXG4gIGlmIChzdGF0ZS5mcmFtZWxvb3AgPT09ICduZXZlcicgJiYgdHlwZW9mIHRpbWVzdGFtcCA9PT0gJ251bWJlcicpIHtcbiAgICBkZWx0YSA9IHRpbWVzdGFtcCAtIHN0YXRlLmNsb2NrLmVsYXBzZWRUaW1lO1xuICAgIHN0YXRlLmNsb2NrLm9sZFRpbWUgPSBzdGF0ZS5jbG9jay5lbGFwc2VkVGltZTtcbiAgICBzdGF0ZS5jbG9jay5lbGFwc2VkVGltZSA9IHRpbWVzdGFtcDtcbiAgfVxuICAvLyBDYWxsIHN1YnNjcmliZXJzICh1c2VGcmFtZSlcbiAgc3Vic2NyaWJlcnMgPSBzdGF0ZS5pbnRlcm5hbC5zdWJzY3JpYmVycztcbiAgZm9yIChpID0gMDsgaSA8IHN1YnNjcmliZXJzLmxlbmd0aDsgaSsrKSB7XG4gICAgc3Vic2NyaXB0aW9uID0gc3Vic2NyaWJlcnNbaV07XG4gICAgc3Vic2NyaXB0aW9uLnJlZi5jdXJyZW50KHN1YnNjcmlwdGlvbi5zdG9yZS5nZXRTdGF0ZSgpLCBkZWx0YSwgZnJhbWUpO1xuICB9XG4gIC8vIFJlbmRlciBjb250ZW50XG4gIGlmICghc3RhdGUuaW50ZXJuYWwucHJpb3JpdHkgJiYgc3RhdGUuZ2wucmVuZGVyKSBzdGF0ZS5nbC5yZW5kZXIoc3RhdGUuc2NlbmUsIHN0YXRlLmNhbWVyYSk7XG4gIC8vIERlY3JlYXNlIGZyYW1lIGNvdW50XG4gIHN0YXRlLmludGVybmFsLmZyYW1lcyA9IE1hdGgubWF4KDAsIHN0YXRlLmludGVybmFsLmZyYW1lcyAtIDEpO1xuICByZXR1cm4gc3RhdGUuZnJhbWVsb29wID09PSAnYWx3YXlzJyA/IDEgOiBzdGF0ZS5pbnRlcm5hbC5mcmFtZXM7XG59XG5mdW5jdGlvbiBjcmVhdGVMb29wKHJvb3RzKSB7XG4gIGxldCBydW5uaW5nID0gZmFsc2U7XG4gIGxldCByZXBlYXQ7XG4gIGxldCBmcmFtZTtcbiAgbGV0IHN0YXRlO1xuICBmdW5jdGlvbiBsb29wKHRpbWVzdGFtcCkge1xuICAgIGZyYW1lID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKGxvb3ApO1xuICAgIHJ1bm5pbmcgPSB0cnVlO1xuICAgIHJlcGVhdCA9IDA7XG5cbiAgICAvLyBSdW4gZWZmZWN0c1xuICAgIGZsdXNoR2xvYmFsRWZmZWN0cygnYmVmb3JlJywgdGltZXN0YW1wKTtcblxuICAgIC8vIFJlbmRlciBhbGwgcm9vdHNcbiAgICBmb3IgKGNvbnN0IHJvb3Qgb2Ygcm9vdHMudmFsdWVzKCkpIHtcbiAgICAgIHZhciBfc3RhdGUkZ2wkeHI7XG4gICAgICBzdGF0ZSA9IHJvb3Quc3RvcmUuZ2V0U3RhdGUoKTtcbiAgICAgIC8vIElmIHRoZSBmcmFtZWxvb3AgaXMgaW52YWxpZGF0ZWQsIGRvIG5vdCBydW4gYW5vdGhlciBmcmFtZVxuICAgICAgaWYgKHN0YXRlLmludGVybmFsLmFjdGl2ZSAmJiAoc3RhdGUuZnJhbWVsb29wID09PSAnYWx3YXlzJyB8fCBzdGF0ZS5pbnRlcm5hbC5mcmFtZXMgPiAwKSAmJiAhKChfc3RhdGUkZ2wkeHIgPSBzdGF0ZS5nbC54cikgIT0gbnVsbCAmJiBfc3RhdGUkZ2wkeHIuaXNQcmVzZW50aW5nKSkge1xuICAgICAgICByZXBlYXQgKz0gcmVuZGVyJDEodGltZXN0YW1wLCBzdGF0ZSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gUnVuIGFmdGVyLWVmZmVjdHNcbiAgICBmbHVzaEdsb2JhbEVmZmVjdHMoJ2FmdGVyJywgdGltZXN0YW1wKTtcblxuICAgIC8vIFN0b3AgdGhlIGxvb3AgaWYgbm90aGluZyBpbnZhbGlkYXRlcyBpdFxuICAgIGlmIChyZXBlYXQgPT09IDApIHtcbiAgICAgIC8vIFRhaWwgY2FsbCBlZmZlY3RzLCB0aGV5IGFyZSBjYWxsZWQgd2hlbiByZW5kZXJpbmcgc3RvcHNcbiAgICAgIGZsdXNoR2xvYmFsRWZmZWN0cygndGFpbCcsIHRpbWVzdGFtcCk7XG5cbiAgICAgIC8vIEZsYWcgZW5kIG9mIG9wZXJhdGlvblxuICAgICAgcnVubmluZyA9IGZhbHNlO1xuICAgICAgcmV0dXJuIGNhbmNlbEFuaW1hdGlvbkZyYW1lKGZyYW1lKTtcbiAgICB9XG4gIH1cbiAgZnVuY3Rpb24gaW52YWxpZGF0ZShzdGF0ZSwgZnJhbWVzID0gMSkge1xuICAgIHZhciBfc3RhdGUkZ2wkeHIyO1xuICAgIGlmICghc3RhdGUpIHJldHVybiByb290cy5mb3JFYWNoKHJvb3QgPT4gaW52YWxpZGF0ZShyb290LnN0b3JlLmdldFN0YXRlKCkpLCBmcmFtZXMpO1xuICAgIGlmICgoX3N0YXRlJGdsJHhyMiA9IHN0YXRlLmdsLnhyKSAhPSBudWxsICYmIF9zdGF0ZSRnbCR4cjIuaXNQcmVzZW50aW5nIHx8ICFzdGF0ZS5pbnRlcm5hbC5hY3RpdmUgfHwgc3RhdGUuZnJhbWVsb29wID09PSAnbmV2ZXInKSByZXR1cm47XG4gICAgLy8gSW5jcmVhc2UgZnJhbWVzLCBkbyBub3QgZ28gaGlnaGVyIHRoYW4gNjBcbiAgICBzdGF0ZS5pbnRlcm5hbC5mcmFtZXMgPSBNYXRoLm1pbig2MCwgc3RhdGUuaW50ZXJuYWwuZnJhbWVzICsgZnJhbWVzKTtcbiAgICAvLyBJZiB0aGUgcmVuZGVyLWxvb3AgaXNuJ3QgYWN0aXZlLCBzdGFydCBpdFxuICAgIGlmICghcnVubmluZykge1xuICAgICAgcnVubmluZyA9IHRydWU7XG4gICAgICByZXF1ZXN0QW5pbWF0aW9uRnJhbWUobG9vcCk7XG4gICAgfVxuICB9XG4gIGZ1bmN0aW9uIGFkdmFuY2UodGltZXN0YW1wLCBydW5HbG9iYWxFZmZlY3RzID0gdHJ1ZSwgc3RhdGUsIGZyYW1lKSB7XG4gICAgaWYgKHJ1bkdsb2JhbEVmZmVjdHMpIGZsdXNoR2xvYmFsRWZmZWN0cygnYmVmb3JlJywgdGltZXN0YW1wKTtcbiAgICBpZiAoIXN0YXRlKSBmb3IgKGNvbnN0IHJvb3Qgb2Ygcm9vdHMudmFsdWVzKCkpIHJlbmRlciQxKHRpbWVzdGFtcCwgcm9vdC5zdG9yZS5nZXRTdGF0ZSgpKTtlbHNlIHJlbmRlciQxKHRpbWVzdGFtcCwgc3RhdGUsIGZyYW1lKTtcbiAgICBpZiAocnVuR2xvYmFsRWZmZWN0cykgZmx1c2hHbG9iYWxFZmZlY3RzKCdhZnRlcicsIHRpbWVzdGFtcCk7XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBsb29wLFxuICAgIC8qKlxyXG4gICAgICogSW52YWxpZGF0ZXMgdGhlIHZpZXcsIHJlcXVlc3RpbmcgYSBmcmFtZSB0byBiZSByZW5kZXJlZC4gV2lsbCBnbG9iYWxseSBpbnZhbGlkYXRlIHVubGVzcyBwYXNzZWQgYSByb290J3Mgc3RhdGUuXHJcbiAgICAgKiBAc2VlIGh0dHBzOi8vZG9jcy5wbW5kLnJzL3JlYWN0LXRocmVlLWZpYmVyL2FwaS9hZGRpdGlvbmFsLWV4cG9ydHMjaW52YWxpZGF0ZVxyXG4gICAgICovXG4gICAgaW52YWxpZGF0ZSxcbiAgICAvKipcclxuICAgICAqIEFkdmFuY2VzIHRoZSBmcmFtZWxvb3AgYW5kIHJ1bnMgcmVuZGVyIGVmZmVjdHMsIHVzZWZ1bCBmb3Igd2hlbiBtYW51YWxseSByZW5kZXJpbmcgdmlhIGBmcmFtZWxvb3A9XCJuZXZlclwiYC5cclxuICAgICAqIEBzZWUgaHR0cHM6Ly9kb2NzLnBtbmQucnMvcmVhY3QtdGhyZWUtZmliZXIvYXBpL2FkZGl0aW9uYWwtZXhwb3J0cyNhZHZhbmNlXHJcbiAgICAgKi9cbiAgICBhZHZhbmNlXG4gIH07XG59XG5cbi8qKlxyXG4gKiBFeHBvc2VzIGFuIG9iamVjdCdzIHtAbGluayBMb2NhbFN0YXRlfS5cclxuICogQHNlZSBodHRwczovL2RvY3MucG1uZC5ycy9yZWFjdC10aHJlZS1maWJlci9hcGkvYWRkaXRpb25hbC1leHBvcnRzI3VzZUluc3RhbmNlSGFuZGxlXHJcbiAqXHJcbiAqICoqTm90ZSoqOiB0aGlzIGlzIGFuIGVzY2FwZSBoYXRjaCB0byByZWFjdC1pbnRlcm5hbCBmaWVsZHMuIEV4cGVjdCB0aGlzIHRvIGNoYW5nZSBzaWduaWZpY2FudGx5IGJldHdlZW4gdmVyc2lvbnMuXHJcbiAqL1xuZnVuY3Rpb24gdXNlSW5zdGFuY2VIYW5kbGUocmVmKSB7XG4gIGNvbnN0IGluc3RhbmNlID0gUmVhY3QudXNlUmVmKG51bGwpO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHZvaWQgKGluc3RhbmNlLmN1cnJlbnQgPSByZWYuY3VycmVudC5fX3IzZiksIFtyZWZdKTtcbiAgcmV0dXJuIGluc3RhbmNlO1xufVxuZnVuY3Rpb24gdXNlU3RvcmUoKSB7XG4gIGNvbnN0IHN0b3JlID0gUmVhY3QudXNlQ29udGV4dChjb250ZXh0KTtcbiAgaWYgKCFzdG9yZSkgdGhyb3cgbmV3IEVycm9yKCdSM0Y6IEhvb2tzIGNhbiBvbmx5IGJlIHVzZWQgd2l0aGluIHRoZSBDYW52YXMgY29tcG9uZW50IScpO1xuICByZXR1cm4gc3RvcmU7XG59XG5cbi8qKlxyXG4gKiBBY2Nlc3NlcyBSM0YncyBpbnRlcm5hbCBzdGF0ZSwgY29udGFpbmluZyByZW5kZXJlciwgY2FudmFzLCBzY2VuZSwgZXRjLlxyXG4gKiBAc2VlIGh0dHBzOi8vZG9jcy5wbW5kLnJzL3JlYWN0LXRocmVlLWZpYmVyL2FwaS9ob29rcyN1c2V0aHJlZVxyXG4gKi9cbmZ1bmN0aW9uIHVzZVRocmVlKHNlbGVjdG9yID0gc3RhdGUgPT4gc3RhdGUsIGVxdWFsaXR5Rm4pIHtcbiAgcmV0dXJuIHVzZVN0b3JlKCkoc2VsZWN0b3IsIGVxdWFsaXR5Rm4pO1xufVxuXG4vKipcclxuICogRXhlY3V0ZXMgYSBjYWxsYmFjayBiZWZvcmUgcmVuZGVyIGluIGEgc2hhcmVkIGZyYW1lIGxvb3AuXHJcbiAqIENhbiBvcmRlciBlZmZlY3RzIHdpdGggcmVuZGVyIHByaW9yaXR5IG9yIG1hbnVhbGx5IHJlbmRlciB3aXRoIGEgcG9zaXRpdmUgcHJpb3JpdHkuXHJcbiAqIEBzZWUgaHR0cHM6Ly9kb2NzLnBtbmQucnMvcmVhY3QtdGhyZWUtZmliZXIvYXBpL2hvb2tzI3VzZWZyYW1lXHJcbiAqL1xuZnVuY3Rpb24gdXNlRnJhbWUoY2FsbGJhY2ssIHJlbmRlclByaW9yaXR5ID0gMCkge1xuICBjb25zdCBzdG9yZSA9IHVzZVN0b3JlKCk7XG4gIGNvbnN0IHN1YnNjcmliZSA9IHN0b3JlLmdldFN0YXRlKCkuaW50ZXJuYWwuc3Vic2NyaWJlO1xuICAvLyBNZW1vaXplIHJlZlxuICBjb25zdCByZWYgPSB1c2VNdXRhYmxlQ2FsbGJhY2soY2FsbGJhY2spO1xuICAvLyBTdWJzY3JpYmUgb24gbW91bnQsIHVuc3Vic2NyaWJlIG9uIHVubW91bnRcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiBzdWJzY3JpYmUocmVmLCByZW5kZXJQcmlvcml0eSwgc3RvcmUpLCBbcmVuZGVyUHJpb3JpdHksIHN1YnNjcmliZSwgc3RvcmVdKTtcbiAgcmV0dXJuIG51bGw7XG59XG5cbi8qKlxyXG4gKiBSZXR1cm5zIGEgbm9kZSBncmFwaCBvZiBhbiBvYmplY3Qgd2l0aCBuYW1lZCBub2RlcyAmIG1hdGVyaWFscy5cclxuICogQHNlZSBodHRwczovL2RvY3MucG1uZC5ycy9yZWFjdC10aHJlZS1maWJlci9hcGkvaG9va3MjdXNlZ3JhcGhcclxuICovXG5mdW5jdGlvbiB1c2VHcmFwaChvYmplY3QpIHtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gYnVpbGRHcmFwaChvYmplY3QpLCBbb2JqZWN0XSk7XG59XG5jb25zdCBtZW1vaXplZExvYWRlcnMgPSBuZXcgV2Vha01hcCgpO1xuZnVuY3Rpb24gbG9hZGluZ0ZuKGV4dGVuc2lvbnMsIG9uUHJvZ3Jlc3MpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIChQcm90bywgLi4uaW5wdXQpIHtcbiAgICAvLyBDb25zdHJ1Y3QgbmV3IGxvYWRlciBhbmQgcnVuIGV4dGVuc2lvbnNcbiAgICBsZXQgbG9hZGVyID0gbWVtb2l6ZWRMb2FkZXJzLmdldChQcm90byk7XG4gICAgaWYgKCFsb2FkZXIpIHtcbiAgICAgIGxvYWRlciA9IG5ldyBQcm90bygpO1xuICAgICAgbWVtb2l6ZWRMb2FkZXJzLnNldChQcm90bywgbG9hZGVyKTtcbiAgICB9XG4gICAgaWYgKGV4dGVuc2lvbnMpIGV4dGVuc2lvbnMobG9hZGVyKTtcbiAgICAvLyBHbyB0aHJvdWdoIHRoZSB1cmxzIGFuZCBsb2FkIHRoZW1cbiAgICByZXR1cm4gUHJvbWlzZS5hbGwoaW5wdXQubWFwKGlucHV0ID0+IG5ldyBQcm9taXNlKChyZXMsIHJlamVjdCkgPT4gbG9hZGVyLmxvYWQoaW5wdXQsIGRhdGEgPT4ge1xuICAgICAgaWYgKGRhdGEuc2NlbmUpIE9iamVjdC5hc3NpZ24oZGF0YSwgYnVpbGRHcmFwaChkYXRhLnNjZW5lKSk7XG4gICAgICByZXMoZGF0YSk7XG4gICAgfSwgb25Qcm9ncmVzcywgZXJyb3IgPT4gcmVqZWN0KG5ldyBFcnJvcihgQ291bGQgbm90IGxvYWQgJHtpbnB1dH06ICR7ZXJyb3IgPT0gbnVsbCA/IHZvaWQgMCA6IGVycm9yLm1lc3NhZ2V9YCkpKSkpKS5maW5hbGx5KCgpID0+IGxvYWRlci5kaXNwb3NlID09IG51bGwgPyB2b2lkIDAgOiBsb2FkZXIuZGlzcG9zZSgpKTtcbiAgfTtcbn1cblxuLyoqXHJcbiAqIFN5bmNocm9ub3VzbHkgbG9hZHMgYW5kIGNhY2hlcyBhc3NldHMgd2l0aCBhIHRocmVlIGxvYWRlci5cclxuICpcclxuICogTm90ZTogdGhpcyBob29rJ3MgY2FsbGVyIG11c3QgYmUgd3JhcHBlZCB3aXRoIGBSZWFjdC5TdXNwZW5zZWBcclxuICogQHNlZSBodHRwczovL2RvY3MucG1uZC5ycy9yZWFjdC10aHJlZS1maWJlci9hcGkvaG9va3MjdXNlbG9hZGVyXHJcbiAqL1xuZnVuY3Rpb24gdXNlTG9hZGVyKFByb3RvLCBpbnB1dCwgZXh0ZW5zaW9ucywgb25Qcm9ncmVzcykge1xuICAvLyBVc2Ugc3VzcGVuc2UgdG8gbG9hZCBhc3luYyBhc3NldHNcbiAgY29uc3Qga2V5cyA9IEFycmF5LmlzQXJyYXkoaW5wdXQpID8gaW5wdXQgOiBbaW5wdXRdO1xuICBjb25zdCByZXN1bHRzID0gc3VzcGVuZChsb2FkaW5nRm4oZXh0ZW5zaW9ucywgb25Qcm9ncmVzcyksIFtQcm90bywgLi4ua2V5c10sIHtcbiAgICBlcXVhbDogaXMuZXF1XG4gIH0pO1xuICAvLyBSZXR1cm4gdGhlIG9iamVjdC9zXG4gIHJldHVybiBBcnJheS5pc0FycmF5KGlucHV0KSA/IHJlc3VsdHMgOiByZXN1bHRzWzBdO1xufVxuXG4vKipcclxuICogUHJlbG9hZHMgYW4gYXNzZXQgaW50byBjYWNoZSBhcyBhIHNpZGUtZWZmZWN0LlxyXG4gKi9cbnVzZUxvYWRlci5wcmVsb2FkID0gZnVuY3Rpb24gKFByb3RvLCBpbnB1dCwgZXh0ZW5zaW9ucykge1xuICBjb25zdCBrZXlzID0gQXJyYXkuaXNBcnJheShpbnB1dCkgPyBpbnB1dCA6IFtpbnB1dF07XG4gIHJldHVybiBwcmVsb2FkKGxvYWRpbmdGbihleHRlbnNpb25zKSwgW1Byb3RvLCAuLi5rZXlzXSk7XG59O1xuXG4vKipcclxuICogUmVtb3ZlcyBhIGxvYWRlZCBhc3NldCBmcm9tIGNhY2hlLlxyXG4gKi9cbnVzZUxvYWRlci5jbGVhciA9IGZ1bmN0aW9uIChQcm90bywgaW5wdXQpIHtcbiAgY29uc3Qga2V5cyA9IEFycmF5LmlzQXJyYXkoaW5wdXQpID8gaW5wdXQgOiBbaW5wdXRdO1xuICByZXR1cm4gY2xlYXIoW1Byb3RvLCAuLi5rZXlzXSk7XG59O1xuXG5jb25zdCByb290cyA9IG5ldyBNYXAoKTtcbmNvbnN0IHtcbiAgaW52YWxpZGF0ZSxcbiAgYWR2YW5jZVxufSA9IGNyZWF0ZUxvb3Aocm9vdHMpO1xuY29uc3Qge1xuICByZWNvbmNpbGVyLFxuICBhcHBseVByb3BzXG59ID0gY3JlYXRlUmVuZGVyZXIocm9vdHMsIGdldEV2ZW50UHJpb3JpdHkpO1xuY29uc3Qgc2hhbGxvd0xvb3NlID0ge1xuICBvYmplY3RzOiAnc2hhbGxvdycsXG4gIHN0cmljdDogZmFsc2Vcbn07XG5jb25zdCBjcmVhdGVSZW5kZXJlckluc3RhbmNlID0gKGdsLCBjYW52YXMpID0+IHtcbiAgY29uc3QgY3VzdG9tUmVuZGVyZXIgPSB0eXBlb2YgZ2wgPT09ICdmdW5jdGlvbicgPyBnbChjYW52YXMpIDogZ2w7XG4gIGlmIChpc1JlbmRlcmVyKGN1c3RvbVJlbmRlcmVyKSkgcmV0dXJuIGN1c3RvbVJlbmRlcmVyO2Vsc2UgcmV0dXJuIG5ldyBUSFJFRS5XZWJHTFJlbmRlcmVyKHtcbiAgICBwb3dlclByZWZlcmVuY2U6ICdoaWdoLXBlcmZvcm1hbmNlJyxcbiAgICBjYW52YXM6IGNhbnZhcyxcbiAgICBhbnRpYWxpYXM6IHRydWUsXG4gICAgYWxwaGE6IHRydWUsXG4gICAgLi4uZ2xcbiAgfSk7XG59O1xuZnVuY3Rpb24gY29tcHV0ZUluaXRpYWxTaXplKGNhbnZhcywgZGVmYXVsdFNpemUpIHtcbiAgaWYgKGRlZmF1bHRTaXplKSByZXR1cm4gZGVmYXVsdFNpemU7XG4gIGlmICh0eXBlb2YgSFRNTENhbnZhc0VsZW1lbnQgIT09ICd1bmRlZmluZWQnICYmIGNhbnZhcyBpbnN0YW5jZW9mIEhUTUxDYW52YXNFbGVtZW50ICYmIGNhbnZhcy5wYXJlbnRFbGVtZW50KSB7XG4gICAgY29uc3Qge1xuICAgICAgd2lkdGgsXG4gICAgICBoZWlnaHQsXG4gICAgICB0b3AsXG4gICAgICBsZWZ0XG4gICAgfSA9IGNhbnZhcy5wYXJlbnRFbGVtZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgIHJldHVybiB7XG4gICAgICB3aWR0aCxcbiAgICAgIGhlaWdodCxcbiAgICAgIHRvcCxcbiAgICAgIGxlZnRcbiAgICB9O1xuICB9IGVsc2UgaWYgKHR5cGVvZiBPZmZzY3JlZW5DYW52YXMgIT09ICd1bmRlZmluZWQnICYmIGNhbnZhcyBpbnN0YW5jZW9mIE9mZnNjcmVlbkNhbnZhcykge1xuICAgIHJldHVybiB7XG4gICAgICB3aWR0aDogY2FudmFzLndpZHRoLFxuICAgICAgaGVpZ2h0OiBjYW52YXMuaGVpZ2h0LFxuICAgICAgdG9wOiAwLFxuICAgICAgbGVmdDogMFxuICAgIH07XG4gIH1cbiAgcmV0dXJuIHtcbiAgICB3aWR0aDogMCxcbiAgICBoZWlnaHQ6IDAsXG4gICAgdG9wOiAwLFxuICAgIGxlZnQ6IDBcbiAgfTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZVJvb3QoY2FudmFzKSB7XG4gIC8vIENoZWNrIGFnYWluc3QgbWlzdGFrZW4gdXNlIG9mIGNyZWF0ZVJvb3RcbiAgY29uc3QgcHJldlJvb3QgPSByb290cy5nZXQoY2FudmFzKTtcbiAgY29uc3QgcHJldkZpYmVyID0gcHJldlJvb3QgPT0gbnVsbCA/IHZvaWQgMCA6IHByZXZSb290LmZpYmVyO1xuICBjb25zdCBwcmV2U3RvcmUgPSBwcmV2Um9vdCA9PSBudWxsID8gdm9pZCAwIDogcHJldlJvb3Quc3RvcmU7XG4gIGlmIChwcmV2Um9vdCkgY29uc29sZS53YXJuKCdSM0YuY3JlYXRlUm9vdCBzaG91bGQgb25seSBiZSBjYWxsZWQgb25jZSEnKTtcblxuICAvLyBSZXBvcnQgd2hlbiBhbiBlcnJvciB3YXMgZGV0ZWN0ZWQgaW4gYSBwcmV2aW91cyByZW5kZXJcbiAgLy8gaHR0cHM6Ly9naXRodWIuY29tL3BtbmRycy9yZWFjdC10aHJlZS1maWJlci9wdWxsLzIyNjFcbiAgY29uc3QgbG9nUmVjb3ZlcmFibGVFcnJvciA9IHR5cGVvZiByZXBvcnRFcnJvciA9PT0gJ2Z1bmN0aW9uJyA/XG4gIC8vIEluIG1vZGVybiBicm93c2VycywgcmVwb3J0RXJyb3Igd2lsbCBkaXNwYXRjaCBhbiBlcnJvciBldmVudCxcbiAgLy8gZW11bGF0aW5nIGFuIHVuY2F1Z2h0IEphdmFTY3JpcHQgZXJyb3IuXG4gIHJlcG9ydEVycm9yIDpcbiAgLy8gSW4gb2xkZXIgYnJvd3NlcnMgYW5kIHRlc3QgZW52aXJvbm1lbnRzLCBmYWxsYmFjayB0byBjb25zb2xlLmVycm9yLlxuICBjb25zb2xlLmVycm9yO1xuXG4gIC8vIENyZWF0ZSBzdG9yZVxuICBjb25zdCBzdG9yZSA9IHByZXZTdG9yZSB8fCBjcmVhdGVTdG9yZShpbnZhbGlkYXRlLCBhZHZhbmNlKTtcbiAgLy8gQ3JlYXRlIHJlbmRlcmVyXG4gIGNvbnN0IGZpYmVyID0gcHJldkZpYmVyIHx8IHJlY29uY2lsZXIuY3JlYXRlQ29udGFpbmVyKHN0b3JlLCBDb25jdXJyZW50Um9vdCwgbnVsbCwgZmFsc2UsIG51bGwsICcnLCBsb2dSZWNvdmVyYWJsZUVycm9yLCBudWxsKTtcbiAgLy8gTWFwIGl0XG4gIGlmICghcHJldlJvb3QpIHJvb3RzLnNldChjYW52YXMsIHtcbiAgICBmaWJlcixcbiAgICBzdG9yZVxuICB9KTtcblxuICAvLyBMb2NhbHNcbiAgbGV0IG9uQ3JlYXRlZDtcbiAgbGV0IGNvbmZpZ3VyZWQgPSBmYWxzZTtcbiAgbGV0IGxhc3RDYW1lcmE7XG4gIHJldHVybiB7XG4gICAgY29uZmlndXJlKHByb3BzID0ge30pIHtcbiAgICAgIGxldCB7XG4gICAgICAgIGdsOiBnbENvbmZpZyxcbiAgICAgICAgc2l6ZTogcHJvcHNTaXplLFxuICAgICAgICBzY2VuZTogc2NlbmVPcHRpb25zLFxuICAgICAgICBldmVudHMsXG4gICAgICAgIG9uQ3JlYXRlZDogb25DcmVhdGVkQ2FsbGJhY2ssXG4gICAgICAgIHNoYWRvd3MgPSBmYWxzZSxcbiAgICAgICAgbGluZWFyID0gZmFsc2UsXG4gICAgICAgIGZsYXQgPSBmYWxzZSxcbiAgICAgICAgbGVnYWN5ID0gZmFsc2UsXG4gICAgICAgIG9ydGhvZ3JhcGhpYyA9IGZhbHNlLFxuICAgICAgICBmcmFtZWxvb3AgPSAnYWx3YXlzJyxcbiAgICAgICAgZHByID0gWzEsIDJdLFxuICAgICAgICBwZXJmb3JtYW5jZSxcbiAgICAgICAgcmF5Y2FzdGVyOiByYXljYXN0T3B0aW9ucyxcbiAgICAgICAgY2FtZXJhOiBjYW1lcmFPcHRpb25zLFxuICAgICAgICBvblBvaW50ZXJNaXNzZWRcbiAgICAgIH0gPSBwcm9wcztcbiAgICAgIGxldCBzdGF0ZSA9IHN0b3JlLmdldFN0YXRlKCk7XG5cbiAgICAgIC8vIFNldCB1cCByZW5kZXJlciAob25lIHRpbWUgb25seSEpXG4gICAgICBsZXQgZ2wgPSBzdGF0ZS5nbDtcbiAgICAgIGlmICghc3RhdGUuZ2wpIHN0YXRlLnNldCh7XG4gICAgICAgIGdsOiBnbCA9IGNyZWF0ZVJlbmRlcmVySW5zdGFuY2UoZ2xDb25maWcsIGNhbnZhcylcbiAgICAgIH0pO1xuXG4gICAgICAvLyBTZXQgdXAgcmF5Y2FzdGVyIChvbmUgdGltZSBvbmx5ISlcbiAgICAgIGxldCByYXljYXN0ZXIgPSBzdGF0ZS5yYXljYXN0ZXI7XG4gICAgICBpZiAoIXJheWNhc3Rlcikgc3RhdGUuc2V0KHtcbiAgICAgICAgcmF5Y2FzdGVyOiByYXljYXN0ZXIgPSBuZXcgVEhSRUUuUmF5Y2FzdGVyKClcbiAgICAgIH0pO1xuXG4gICAgICAvLyBTZXQgcmF5Y2FzdGVyIG9wdGlvbnNcbiAgICAgIGNvbnN0IHtcbiAgICAgICAgcGFyYW1zLFxuICAgICAgICAuLi5vcHRpb25zXG4gICAgICB9ID0gcmF5Y2FzdE9wdGlvbnMgfHwge307XG4gICAgICBpZiAoIWlzLmVxdShvcHRpb25zLCByYXljYXN0ZXIsIHNoYWxsb3dMb29zZSkpIGFwcGx5UHJvcHMocmF5Y2FzdGVyLCB7XG4gICAgICAgIC4uLm9wdGlvbnNcbiAgICAgIH0pO1xuICAgICAgaWYgKCFpcy5lcXUocGFyYW1zLCByYXljYXN0ZXIucGFyYW1zLCBzaGFsbG93TG9vc2UpKSBhcHBseVByb3BzKHJheWNhc3Rlciwge1xuICAgICAgICBwYXJhbXM6IHtcbiAgICAgICAgICAuLi5yYXljYXN0ZXIucGFyYW1zLFxuICAgICAgICAgIC4uLnBhcmFtc1xuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgLy8gQ3JlYXRlIGRlZmF1bHQgY2FtZXJhLCBkb24ndCBvdmVyd3JpdGUgYW55IHVzZXItc2V0IHN0YXRlXG4gICAgICBpZiAoIXN0YXRlLmNhbWVyYSB8fCBzdGF0ZS5jYW1lcmEgPT09IGxhc3RDYW1lcmEgJiYgIWlzLmVxdShsYXN0Q2FtZXJhLCBjYW1lcmFPcHRpb25zLCBzaGFsbG93TG9vc2UpKSB7XG4gICAgICAgIGxhc3RDYW1lcmEgPSBjYW1lcmFPcHRpb25zO1xuICAgICAgICBjb25zdCBpc0NhbWVyYSA9IGNhbWVyYU9wdGlvbnMgaW5zdGFuY2VvZiBUSFJFRS5DYW1lcmE7XG4gICAgICAgIGNvbnN0IGNhbWVyYSA9IGlzQ2FtZXJhID8gY2FtZXJhT3B0aW9ucyA6IG9ydGhvZ3JhcGhpYyA/IG5ldyBUSFJFRS5PcnRob2dyYXBoaWNDYW1lcmEoMCwgMCwgMCwgMCwgMC4xLCAxMDAwKSA6IG5ldyBUSFJFRS5QZXJzcGVjdGl2ZUNhbWVyYSg3NSwgMCwgMC4xLCAxMDAwKTtcbiAgICAgICAgaWYgKCFpc0NhbWVyYSkge1xuICAgICAgICAgIGNhbWVyYS5wb3NpdGlvbi56ID0gNTtcbiAgICAgICAgICBpZiAoY2FtZXJhT3B0aW9ucykgYXBwbHlQcm9wcyhjYW1lcmEsIGNhbWVyYU9wdGlvbnMpO1xuICAgICAgICAgIC8vIEFsd2F5cyBsb29rIGF0IGNlbnRlciBieSBkZWZhdWx0XG4gICAgICAgICAgaWYgKCFzdGF0ZS5jYW1lcmEgJiYgIShjYW1lcmFPcHRpb25zICE9IG51bGwgJiYgY2FtZXJhT3B0aW9ucy5yb3RhdGlvbikpIGNhbWVyYS5sb29rQXQoMCwgMCwgMCk7XG4gICAgICAgIH1cbiAgICAgICAgc3RhdGUuc2V0KHtcbiAgICAgICAgICBjYW1lcmFcbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIC8vIFNldCB1cCBzY2VuZSAob25lIHRpbWUgb25seSEpXG4gICAgICBpZiAoIXN0YXRlLnNjZW5lKSB7XG4gICAgICAgIGxldCBzY2VuZTtcbiAgICAgICAgaWYgKHNjZW5lT3B0aW9ucyBpbnN0YW5jZW9mIFRIUkVFLlNjZW5lKSB7XG4gICAgICAgICAgc2NlbmUgPSBzY2VuZU9wdGlvbnM7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2NlbmUgPSBuZXcgVEhSRUUuU2NlbmUoKTtcbiAgICAgICAgICBpZiAoc2NlbmVPcHRpb25zKSBhcHBseVByb3BzKHNjZW5lLCBzY2VuZU9wdGlvbnMpO1xuICAgICAgICB9XG4gICAgICAgIHN0YXRlLnNldCh7XG4gICAgICAgICAgc2NlbmU6IHByZXBhcmUoc2NlbmUpXG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICAvLyBTZXQgdXAgWFIgKG9uZSB0aW1lIG9ubHkhKVxuICAgICAgaWYgKCFzdGF0ZS54cikge1xuICAgICAgICB2YXIgX2dsJHhyO1xuICAgICAgICAvLyBIYW5kbGUgZnJhbWUgYmVoYXZpb3IgaW4gV2ViWFJcbiAgICAgICAgY29uc3QgaGFuZGxlWFJGcmFtZSA9ICh0aW1lc3RhbXAsIGZyYW1lKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc3RhdGUgPSBzdG9yZS5nZXRTdGF0ZSgpO1xuICAgICAgICAgIGlmIChzdGF0ZS5mcmFtZWxvb3AgPT09ICduZXZlcicpIHJldHVybjtcbiAgICAgICAgICBhZHZhbmNlKHRpbWVzdGFtcCwgdHJ1ZSwgc3RhdGUsIGZyYW1lKTtcbiAgICAgICAgfTtcblxuICAgICAgICAvLyBUb2dnbGUgcmVuZGVyIHN3aXRjaGluZyBvbiBzZXNzaW9uXG4gICAgICAgIGNvbnN0IGhhbmRsZVNlc3Npb25DaGFuZ2UgPSAoKSA9PiB7XG4gICAgICAgICAgY29uc3Qgc3RhdGUgPSBzdG9yZS5nZXRTdGF0ZSgpO1xuICAgICAgICAgIHN0YXRlLmdsLnhyLmVuYWJsZWQgPSBzdGF0ZS5nbC54ci5pc1ByZXNlbnRpbmc7XG4gICAgICAgICAgc3RhdGUuZ2wueHIuc2V0QW5pbWF0aW9uTG9vcChzdGF0ZS5nbC54ci5pc1ByZXNlbnRpbmcgPyBoYW5kbGVYUkZyYW1lIDogbnVsbCk7XG4gICAgICAgICAgaWYgKCFzdGF0ZS5nbC54ci5pc1ByZXNlbnRpbmcpIGludmFsaWRhdGUoc3RhdGUpO1xuICAgICAgICB9O1xuXG4gICAgICAgIC8vIFdlYlhSIHNlc3Npb24gbWFuYWdlclxuICAgICAgICBjb25zdCB4ciA9IHtcbiAgICAgICAgICBjb25uZWN0KCkge1xuICAgICAgICAgICAgY29uc3QgZ2wgPSBzdG9yZS5nZXRTdGF0ZSgpLmdsO1xuICAgICAgICAgICAgZ2wueHIuYWRkRXZlbnRMaXN0ZW5lcignc2Vzc2lvbnN0YXJ0JywgaGFuZGxlU2Vzc2lvbkNoYW5nZSk7XG4gICAgICAgICAgICBnbC54ci5hZGRFdmVudExpc3RlbmVyKCdzZXNzaW9uZW5kJywgaGFuZGxlU2Vzc2lvbkNoYW5nZSk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBkaXNjb25uZWN0KCkge1xuICAgICAgICAgICAgY29uc3QgZ2wgPSBzdG9yZS5nZXRTdGF0ZSgpLmdsO1xuICAgICAgICAgICAgZ2wueHIucmVtb3ZlRXZlbnRMaXN0ZW5lcignc2Vzc2lvbnN0YXJ0JywgaGFuZGxlU2Vzc2lvbkNoYW5nZSk7XG4gICAgICAgICAgICBnbC54ci5yZW1vdmVFdmVudExpc3RlbmVyKCdzZXNzaW9uZW5kJywgaGFuZGxlU2Vzc2lvbkNoYW5nZSk7XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuXG4gICAgICAgIC8vIFN1YnNjcmliZSB0byBXZWJYUiBzZXNzaW9uIGV2ZW50c1xuICAgICAgICBpZiAodHlwZW9mICgoX2dsJHhyID0gZ2wueHIpID09IG51bGwgPyB2b2lkIDAgOiBfZ2wkeHIuYWRkRXZlbnRMaXN0ZW5lcikgPT09ICdmdW5jdGlvbicpIHhyLmNvbm5lY3QoKTtcbiAgICAgICAgc3RhdGUuc2V0KHtcbiAgICAgICAgICB4clxuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gU2V0IHNoYWRvd21hcFxuICAgICAgaWYgKGdsLnNoYWRvd01hcCkge1xuICAgICAgICBjb25zdCBvbGRFbmFibGVkID0gZ2wuc2hhZG93TWFwLmVuYWJsZWQ7XG4gICAgICAgIGNvbnN0IG9sZFR5cGUgPSBnbC5zaGFkb3dNYXAudHlwZTtcbiAgICAgICAgZ2wuc2hhZG93TWFwLmVuYWJsZWQgPSAhIXNoYWRvd3M7XG4gICAgICAgIGlmIChpcy5ib28oc2hhZG93cykpIHtcbiAgICAgICAgICBnbC5zaGFkb3dNYXAudHlwZSA9IFRIUkVFLlBDRlNvZnRTaGFkb3dNYXA7XG4gICAgICAgIH0gZWxzZSBpZiAoaXMuc3RyKHNoYWRvd3MpKSB7XG4gICAgICAgICAgdmFyIF90eXBlcyRzaGFkb3dzO1xuICAgICAgICAgIGNvbnN0IHR5cGVzID0ge1xuICAgICAgICAgICAgYmFzaWM6IFRIUkVFLkJhc2ljU2hhZG93TWFwLFxuICAgICAgICAgICAgcGVyY2VudGFnZTogVEhSRUUuUENGU2hhZG93TWFwLFxuICAgICAgICAgICAgc29mdDogVEhSRUUuUENGU29mdFNoYWRvd01hcCxcbiAgICAgICAgICAgIHZhcmlhbmNlOiBUSFJFRS5WU01TaGFkb3dNYXBcbiAgICAgICAgICB9O1xuICAgICAgICAgIGdsLnNoYWRvd01hcC50eXBlID0gKF90eXBlcyRzaGFkb3dzID0gdHlwZXNbc2hhZG93c10pICE9IG51bGwgPyBfdHlwZXMkc2hhZG93cyA6IFRIUkVFLlBDRlNvZnRTaGFkb3dNYXA7XG4gICAgICAgIH0gZWxzZSBpZiAoaXMub2JqKHNoYWRvd3MpKSB7XG4gICAgICAgICAgT2JqZWN0LmFzc2lnbihnbC5zaGFkb3dNYXAsIHNoYWRvd3MpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChvbGRFbmFibGVkICE9PSBnbC5zaGFkb3dNYXAuZW5hYmxlZCB8fCBvbGRUeXBlICE9PSBnbC5zaGFkb3dNYXAudHlwZSkgZ2wuc2hhZG93TWFwLm5lZWRzVXBkYXRlID0gdHJ1ZTtcbiAgICAgIH1cblxuICAgICAgLy8gU2FmZWx5IHNldCBjb2xvciBtYW5hZ2VtZW50IGlmIGF2YWlsYWJsZS5cbiAgICAgIC8vIEF2b2lkIGFjY2Vzc2luZyBUSFJFRS5Db2xvck1hbmFnZW1lbnQgdG8gcGxheSBuaWNlIHdpdGggb2xkZXIgdmVyc2lvbnNcbiAgICAgIGNvbnN0IENvbG9yTWFuYWdlbWVudCA9IGdldENvbG9yTWFuYWdlbWVudCgpO1xuICAgICAgaWYgKENvbG9yTWFuYWdlbWVudCkge1xuICAgICAgICBpZiAoJ2VuYWJsZWQnIGluIENvbG9yTWFuYWdlbWVudCkgQ29sb3JNYW5hZ2VtZW50LmVuYWJsZWQgPSAhbGVnYWN5O2Vsc2UgaWYgKCdsZWdhY3lNb2RlJyBpbiBDb2xvck1hbmFnZW1lbnQpIENvbG9yTWFuYWdlbWVudC5sZWdhY3lNb2RlID0gbGVnYWN5O1xuICAgICAgfVxuXG4gICAgICAvLyBTZXQgY29sb3Igc3BhY2UgYW5kIHRvbmVtYXBwaW5nIHByZWZlcmVuY2VzXG4gICAgICBjb25zdCBMaW5lYXJFbmNvZGluZyA9IDMwMDA7XG4gICAgICBjb25zdCBzUkdCRW5jb2RpbmcgPSAzMDAxO1xuICAgICAgYXBwbHlQcm9wcyhnbCwge1xuICAgICAgICBvdXRwdXRFbmNvZGluZzogbGluZWFyID8gTGluZWFyRW5jb2RpbmcgOiBzUkdCRW5jb2RpbmcsXG4gICAgICAgIHRvbmVNYXBwaW5nOiBmbGF0ID8gVEhSRUUuTm9Ub25lTWFwcGluZyA6IFRIUkVFLkFDRVNGaWxtaWNUb25lTWFwcGluZ1xuICAgICAgfSk7XG5cbiAgICAgIC8vIFVwZGF0ZSBjb2xvciBtYW5hZ2VtZW50IHN0YXRlXG4gICAgICBpZiAoc3RhdGUubGVnYWN5ICE9PSBsZWdhY3kpIHN0YXRlLnNldCgoKSA9PiAoe1xuICAgICAgICBsZWdhY3lcbiAgICAgIH0pKTtcbiAgICAgIGlmIChzdGF0ZS5saW5lYXIgIT09IGxpbmVhcikgc3RhdGUuc2V0KCgpID0+ICh7XG4gICAgICAgIGxpbmVhclxuICAgICAgfSkpO1xuICAgICAgaWYgKHN0YXRlLmZsYXQgIT09IGZsYXQpIHN0YXRlLnNldCgoKSA9PiAoe1xuICAgICAgICBmbGF0XG4gICAgICB9KSk7XG5cbiAgICAgIC8vIFNldCBnbCBwcm9wc1xuICAgICAgaWYgKGdsQ29uZmlnICYmICFpcy5mdW4oZ2xDb25maWcpICYmICFpc1JlbmRlcmVyKGdsQ29uZmlnKSAmJiAhaXMuZXF1KGdsQ29uZmlnLCBnbCwgc2hhbGxvd0xvb3NlKSkgYXBwbHlQcm9wcyhnbCwgZ2xDb25maWcpO1xuICAgICAgLy8gU3RvcmUgZXZlbnRzIGludGVybmFsbHlcbiAgICAgIGlmIChldmVudHMgJiYgIXN0YXRlLmV2ZW50cy5oYW5kbGVycykgc3RhdGUuc2V0KHtcbiAgICAgICAgZXZlbnRzOiBldmVudHMoc3RvcmUpXG4gICAgICB9KTtcbiAgICAgIC8vIENoZWNrIHNpemUsIGFsbG93IGl0IHRvIHRha2Ugb24gY29udGFpbmVyIGJvdW5kcyBpbml0aWFsbHlcbiAgICAgIGNvbnN0IHNpemUgPSBjb21wdXRlSW5pdGlhbFNpemUoY2FudmFzLCBwcm9wc1NpemUpO1xuICAgICAgaWYgKCFpcy5lcXUoc2l6ZSwgc3RhdGUuc2l6ZSwgc2hhbGxvd0xvb3NlKSkge1xuICAgICAgICBzdGF0ZS5zZXRTaXplKHNpemUud2lkdGgsIHNpemUuaGVpZ2h0LCBzaXplLnVwZGF0ZVN0eWxlLCBzaXplLnRvcCwgc2l6ZS5sZWZ0KTtcbiAgICAgIH1cbiAgICAgIC8vIENoZWNrIHBpeGVscmF0aW9cbiAgICAgIGlmIChkcHIgJiYgc3RhdGUudmlld3BvcnQuZHByICE9PSBjYWxjdWxhdGVEcHIoZHByKSkgc3RhdGUuc2V0RHByKGRwcik7XG4gICAgICAvLyBDaGVjayBmcmFtZWxvb3BcbiAgICAgIGlmIChzdGF0ZS5mcmFtZWxvb3AgIT09IGZyYW1lbG9vcCkgc3RhdGUuc2V0RnJhbWVsb29wKGZyYW1lbG9vcCk7XG4gICAgICAvLyBDaGVjayBwb2ludGVyIG1pc3NlZFxuICAgICAgaWYgKCFzdGF0ZS5vblBvaW50ZXJNaXNzZWQpIHN0YXRlLnNldCh7XG4gICAgICAgIG9uUG9pbnRlck1pc3NlZFxuICAgICAgfSk7XG4gICAgICAvLyBDaGVjayBwZXJmb3JtYW5jZVxuICAgICAgaWYgKHBlcmZvcm1hbmNlICYmICFpcy5lcXUocGVyZm9ybWFuY2UsIHN0YXRlLnBlcmZvcm1hbmNlLCBzaGFsbG93TG9vc2UpKSBzdGF0ZS5zZXQoc3RhdGUgPT4gKHtcbiAgICAgICAgcGVyZm9ybWFuY2U6IHtcbiAgICAgICAgICAuLi5zdGF0ZS5wZXJmb3JtYW5jZSxcbiAgICAgICAgICAuLi5wZXJmb3JtYW5jZVxuICAgICAgICB9XG4gICAgICB9KSk7XG5cbiAgICAgIC8vIFNldCBsb2NhbHNcbiAgICAgIG9uQ3JlYXRlZCA9IG9uQ3JlYXRlZENhbGxiYWNrO1xuICAgICAgY29uZmlndXJlZCA9IHRydWU7XG4gICAgICByZXR1cm4gdGhpcztcbiAgICB9LFxuICAgIHJlbmRlcihjaGlsZHJlbikge1xuICAgICAgLy8gVGhlIHJvb3QgaGFzIHRvIGJlIGNvbmZpZ3VyZWQgYmVmb3JlIGl0IGNhbiBiZSByZW5kZXJlZFxuICAgICAgaWYgKCFjb25maWd1cmVkKSB0aGlzLmNvbmZpZ3VyZSgpO1xuICAgICAgcmVjb25jaWxlci51cGRhdGVDb250YWluZXIoIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFByb3ZpZGVyLCB7XG4gICAgICAgIHN0b3JlOiBzdG9yZSxcbiAgICAgICAgY2hpbGRyZW46IGNoaWxkcmVuLFxuICAgICAgICBvbkNyZWF0ZWQ6IG9uQ3JlYXRlZCxcbiAgICAgICAgcm9vdEVsZW1lbnQ6IGNhbnZhc1xuICAgICAgfSksIGZpYmVyLCBudWxsLCAoKSA9PiB1bmRlZmluZWQpO1xuICAgICAgcmV0dXJuIHN0b3JlO1xuICAgIH0sXG4gICAgdW5tb3VudCgpIHtcbiAgICAgIHVubW91bnRDb21wb25lbnRBdE5vZGUoY2FudmFzKTtcbiAgICB9XG4gIH07XG59XG5mdW5jdGlvbiByZW5kZXIoY2hpbGRyZW4sIGNhbnZhcywgY29uZmlnKSB7XG4gIGNvbnNvbGUud2FybignUjNGLnJlbmRlciBpcyBubyBsb25nZXIgc3VwcG9ydGVkIGluIFJlYWN0IDE4LiBVc2UgY3JlYXRlUm9vdCBpbnN0ZWFkIScpO1xuICBjb25zdCByb290ID0gY3JlYXRlUm9vdChjYW52YXMpO1xuICByb290LmNvbmZpZ3VyZShjb25maWcpO1xuICByZXR1cm4gcm9vdC5yZW5kZXIoY2hpbGRyZW4pO1xufVxuZnVuY3Rpb24gUHJvdmlkZXIoe1xuICBzdG9yZSxcbiAgY2hpbGRyZW4sXG4gIG9uQ3JlYXRlZCxcbiAgcm9vdEVsZW1lbnRcbn0pIHtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc3RhdGUgPSBzdG9yZS5nZXRTdGF0ZSgpO1xuICAgIC8vIEZsYWcgdGhlIGNhbnZhcyBhY3RpdmUsIHJlbmRlcmluZyB3aWxsIG5vdyBiZWdpblxuICAgIHN0YXRlLnNldChzdGF0ZSA9PiAoe1xuICAgICAgaW50ZXJuYWw6IHtcbiAgICAgICAgLi4uc3RhdGUuaW50ZXJuYWwsXG4gICAgICAgIGFjdGl2ZTogdHJ1ZVxuICAgICAgfVxuICAgIH0pKTtcbiAgICAvLyBOb3RpZml5IHRoYXQgaW5pdCBpcyBjb21wbGV0ZWQsIHRoZSBzY2VuZSBncmFwaCBleGlzdHMsIGJ1dCBub3RoaW5nIGhhcyB5ZXQgcmVuZGVyZWRcbiAgICBpZiAob25DcmVhdGVkKSBvbkNyZWF0ZWQoc3RhdGUpO1xuICAgIC8vIENvbm5lY3QgZXZlbnRzIHRvIHRoZSB0YXJnZXRzIHBhcmVudCwgdGhpcyBpcyBkb25lIHRvIGVuc3VyZSBldmVudHMgYXJlIHJlZ2lzdGVyZWQgb25cbiAgICAvLyBhIHNoYXJlZCB0YXJnZXQsIGFuZCBub3Qgb24gdGhlIGNhbnZhcyBpdHNlbGZcbiAgICBpZiAoIXN0b3JlLmdldFN0YXRlKCkuZXZlbnRzLmNvbm5lY3RlZCkgc3RhdGUuZXZlbnRzLmNvbm5lY3QgPT0gbnVsbCA/IHZvaWQgMCA6IHN0YXRlLmV2ZW50cy5jb25uZWN0KHJvb3RFbGVtZW50KTtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gIH0sIFtdKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KGNvbnRleHQuUHJvdmlkZXIsIHtcbiAgICB2YWx1ZTogc3RvcmVcbiAgfSwgY2hpbGRyZW4pO1xufVxuZnVuY3Rpb24gdW5tb3VudENvbXBvbmVudEF0Tm9kZShjYW52YXMsIGNhbGxiYWNrKSB7XG4gIGNvbnN0IHJvb3QgPSByb290cy5nZXQoY2FudmFzKTtcbiAgY29uc3QgZmliZXIgPSByb290ID09IG51bGwgPyB2b2lkIDAgOiByb290LmZpYmVyO1xuICBpZiAoZmliZXIpIHtcbiAgICBjb25zdCBzdGF0ZSA9IHJvb3QgPT0gbnVsbCA/IHZvaWQgMCA6IHJvb3Quc3RvcmUuZ2V0U3RhdGUoKTtcbiAgICBpZiAoc3RhdGUpIHN0YXRlLmludGVybmFsLmFjdGl2ZSA9IGZhbHNlO1xuICAgIHJlY29uY2lsZXIudXBkYXRlQ29udGFpbmVyKG51bGwsIGZpYmVyLCBudWxsLCAoKSA9PiB7XG4gICAgICBpZiAoc3RhdGUpIHtcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHZhciBfc3RhdGUkZ2wsIF9zdGF0ZSRnbCRyZW5kZXJMaXN0cywgX3N0YXRlJGdsMiwgX3N0YXRlJGdsMztcbiAgICAgICAgICAgIHN0YXRlLmV2ZW50cy5kaXNjb25uZWN0ID09IG51bGwgPyB2b2lkIDAgOiBzdGF0ZS5ldmVudHMuZGlzY29ubmVjdCgpO1xuICAgICAgICAgICAgKF9zdGF0ZSRnbCA9IHN0YXRlLmdsKSA9PSBudWxsID8gdm9pZCAwIDogKF9zdGF0ZSRnbCRyZW5kZXJMaXN0cyA9IF9zdGF0ZSRnbC5yZW5kZXJMaXN0cykgPT0gbnVsbCA/IHZvaWQgMCA6IF9zdGF0ZSRnbCRyZW5kZXJMaXN0cy5kaXNwb3NlID09IG51bGwgPyB2b2lkIDAgOiBfc3RhdGUkZ2wkcmVuZGVyTGlzdHMuZGlzcG9zZSgpO1xuICAgICAgICAgICAgKF9zdGF0ZSRnbDIgPSBzdGF0ZS5nbCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9zdGF0ZSRnbDIuZm9yY2VDb250ZXh0TG9zcyA9PSBudWxsID8gdm9pZCAwIDogX3N0YXRlJGdsMi5mb3JjZUNvbnRleHRMb3NzKCk7XG4gICAgICAgICAgICBpZiAoKF9zdGF0ZSRnbDMgPSBzdGF0ZS5nbCkgIT0gbnVsbCAmJiBfc3RhdGUkZ2wzLnhyKSBzdGF0ZS54ci5kaXNjb25uZWN0KCk7XG4gICAgICAgICAgICBkaXNwb3NlKHN0YXRlKTtcbiAgICAgICAgICAgIHJvb3RzLmRlbGV0ZShjYW52YXMpO1xuICAgICAgICAgICAgaWYgKGNhbGxiYWNrKSBjYWxsYmFjayhjYW52YXMpO1xuICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgIC8qIC4uLiAqL1xuICAgICAgICAgIH1cbiAgICAgICAgfSwgNTAwKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxufVxuZnVuY3Rpb24gY3JlYXRlUG9ydGFsKGNoaWxkcmVuLCBjb250YWluZXIsIHN0YXRlKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChQb3J0YWwsIHtcbiAgICBrZXk6IGNvbnRhaW5lci51dWlkLFxuICAgIGNoaWxkcmVuOiBjaGlsZHJlbixcbiAgICBjb250YWluZXI6IGNvbnRhaW5lcixcbiAgICBzdGF0ZTogc3RhdGVcbiAgfSk7XG59XG5mdW5jdGlvbiBQb3J0YWwoe1xuICBzdGF0ZSA9IHt9LFxuICBjaGlsZHJlbixcbiAgY29udGFpbmVyXG59KSB7XG4gIC8qKiBUaGlzIGhhcyB0byBiZSBhIGNvbXBvbmVudCBiZWNhdXNlIGl0IHdvdWxkIG5vdCBiZSBhYmxlIHRvIGNhbGwgdXNlVGhyZWUvdXNlU3RvcmUgb3RoZXJ3aXNlIHNpbmNlXHJcbiAgICogIGlmIHRoaXMgaXMgb3VyIGVudmlyb25tZW50LCB0aGVuIHdlIGFyZSBub3QgaW4gcjNmJ3MgcmVuZGVyZXIgYnV0IGluIHJlYWN0LWRvbSwgaXQgd291bGQgdHJpZ2dlclxyXG4gICAqICB0aGUgXCJSM0YgaG9va3MgY2FuIG9ubHkgYmUgdXNlZCB3aXRoaW4gdGhlIENhbnZhcyBjb21wb25lbnQhXCIgd2FybmluZzpcclxuICAgKiAgPENhbnZhcz5cclxuICAgKiAgICB7Y3JlYXRlUG9ydGFsKC4uLil9ICovXG4gIGNvbnN0IHtcbiAgICBldmVudHMsXG4gICAgc2l6ZSxcbiAgICAuLi5yZXN0XG4gIH0gPSBzdGF0ZTtcbiAgY29uc3QgcHJldmlvdXNSb290ID0gdXNlU3RvcmUoKTtcbiAgY29uc3QgW3JheWNhc3Rlcl0gPSBSZWFjdC51c2VTdGF0ZSgoKSA9PiBuZXcgVEhSRUUuUmF5Y2FzdGVyKCkpO1xuICBjb25zdCBbcG9pbnRlcl0gPSBSZWFjdC51c2VTdGF0ZSgoKSA9PiBuZXcgVEhSRUUuVmVjdG9yMigpKTtcbiAgY29uc3QgaW5qZWN0ID0gUmVhY3QudXNlQ2FsbGJhY2soKHJvb3RTdGF0ZSwgaW5qZWN0U3RhdGUpID0+IHtcbiAgICBjb25zdCBpbnRlcnNlY3QgPSB7XG4gICAgICAuLi5yb290U3RhdGVcbiAgICB9OyAvLyBhbGwgcHJldiBzdGF0ZSBwcm9wc1xuXG4gICAgLy8gT25seSB0aGUgZmllbGRzIG9mIFwicm9vdFN0YXRlXCIgdGhhdCBkbyBub3QgZGlmZmVyIGZyb20gaW5qZWN0U3RhdGVcbiAgICAvLyBTb21lIHByb3BzIHNob3VsZCBiZSBvZmYtbGltaXRzXG4gICAgLy8gT3RoZXJ3aXNlIGZpbHRlciBvdXQgdGhlIHByb3BzIHRoYXQgYXJlIGRpZmZlcmVudCBhbmQgbGV0IHRoZSBpbmplY3QgbGF5ZXIgdGFrZSBwcmVjZWRlbmNlXG4gICAgT2JqZWN0LmtleXMocm9vdFN0YXRlKS5mb3JFYWNoKGtleSA9PiB7XG4gICAgICBpZiAoXG4gICAgICAvLyBTb21lIHByb3BzIHNob3VsZCBiZSBvZmYtbGltaXRzXG4gICAgICBwcml2YXRlS2V5cy5pbmNsdWRlcyhrZXkpIHx8XG4gICAgICAvLyBPdGhlcndpc2UgZmlsdGVyIG91dCB0aGUgcHJvcHMgdGhhdCBhcmUgZGlmZmVyZW50IGFuZCBsZXQgdGhlIGluamVjdCBsYXllciB0YWtlIHByZWNlZGVuY2VcbiAgICAgIC8vIFVubGVzcyB0aGUgaW5qZWN0IGxheWVyIHByb3BzIGlzIHVuZGVmaW5lZCwgdGhlbiB3ZSBrZWVwIHRoZSByb290IGxheWVyXG4gICAgICByb290U3RhdGVba2V5XSAhPT0gaW5qZWN0U3RhdGVba2V5XSAmJiBpbmplY3RTdGF0ZVtrZXldKSB7XG4gICAgICAgIGRlbGV0ZSBpbnRlcnNlY3Rba2V5XTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICBsZXQgdmlld3BvcnQgPSB1bmRlZmluZWQ7XG4gICAgaWYgKGluamVjdFN0YXRlICYmIHNpemUpIHtcbiAgICAgIGNvbnN0IGNhbWVyYSA9IGluamVjdFN0YXRlLmNhbWVyYTtcbiAgICAgIC8vIENhbGN1bGF0ZSB0aGUgb3ZlcnJpZGUgdmlld3BvcnQsIGlmIHByZXNlbnRcbiAgICAgIHZpZXdwb3J0ID0gcm9vdFN0YXRlLnZpZXdwb3J0LmdldEN1cnJlbnRWaWV3cG9ydChjYW1lcmEsIG5ldyBUSFJFRS5WZWN0b3IzKCksIHNpemUpO1xuICAgICAgLy8gVXBkYXRlIHRoZSBwb3J0YWwgY2FtZXJhLCBpZiBpdCBkaWZmZXJzIGZyb20gdGhlIHByZXZpb3VzIGxheWVyXG4gICAgICBpZiAoY2FtZXJhICE9PSByb290U3RhdGUuY2FtZXJhKSB1cGRhdGVDYW1lcmEoY2FtZXJhLCBzaXplKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIC8vIFRoZSBpbnRlcnNlY3QgY29uc2lzdHMgb2YgdGhlIHByZXZpb3VzIHJvb3Qgc3RhdGVcbiAgICAgIC4uLmludGVyc2VjdCxcbiAgICAgIC8vIFBvcnRhbHMgaGF2ZSB0aGVpciBvd24gc2NlbmUsIHdoaWNoIGZvcm1zIHRoZSByb290LCBhIHJheWNhc3RlciBhbmQgYSBwb2ludGVyXG4gICAgICBzY2VuZTogY29udGFpbmVyLFxuICAgICAgcmF5Y2FzdGVyLFxuICAgICAgcG9pbnRlcixcbiAgICAgIG1vdXNlOiBwb2ludGVyLFxuICAgICAgLy8gVGhlaXIgcHJldmlvdXMgcm9vdCBpcyB0aGUgbGF5ZXIgYmVmb3JlIGl0XG4gICAgICBwcmV2aW91c1Jvb3QsXG4gICAgICAvLyBFdmVudHMsIHNpemUgYW5kIHZpZXdwb3J0IGNhbiBiZSBvdmVycmlkZGVuIGJ5IHRoZSBpbmplY3QgbGF5ZXJcbiAgICAgIGV2ZW50czoge1xuICAgICAgICAuLi5yb290U3RhdGUuZXZlbnRzLFxuICAgICAgICAuLi4oaW5qZWN0U3RhdGUgPT0gbnVsbCA/IHZvaWQgMCA6IGluamVjdFN0YXRlLmV2ZW50cyksXG4gICAgICAgIC4uLmV2ZW50c1xuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgLi4ucm9vdFN0YXRlLnNpemUsXG4gICAgICAgIC4uLnNpemVcbiAgICAgIH0sXG4gICAgICB2aWV3cG9ydDoge1xuICAgICAgICAuLi5yb290U3RhdGUudmlld3BvcnQsXG4gICAgICAgIC4uLnZpZXdwb3J0XG4gICAgICB9LFxuICAgICAgLi4ucmVzdFxuICAgIH07XG4gIH0sIFtzdGF0ZV0pO1xuICBjb25zdCBbdXNlUG9ydGFsU3RvcmVdID0gUmVhY3QudXNlU3RhdGUoKCkgPT4ge1xuICAgIC8vIENyZWF0ZSBhIG1pcnJvcmVkIHN0b3JlLCBiYXNlZCBvbiB0aGUgcHJldmlvdXMgcm9vdCB3aXRoIGEgZmV3IG92ZXJyaWRlcyAuLi5cbiAgICBjb25zdCBwcmV2aW91c1N0YXRlID0gcHJldmlvdXNSb290LmdldFN0YXRlKCk7XG4gICAgY29uc3Qgc3RvcmUgPSBjcmVhdGUoKHNldCwgZ2V0KSA9PiAoe1xuICAgICAgLi4ucHJldmlvdXNTdGF0ZSxcbiAgICAgIHNjZW5lOiBjb250YWluZXIsXG4gICAgICByYXljYXN0ZXIsXG4gICAgICBwb2ludGVyLFxuICAgICAgbW91c2U6IHBvaW50ZXIsXG4gICAgICBwcmV2aW91c1Jvb3QsXG4gICAgICBldmVudHM6IHtcbiAgICAgICAgLi4ucHJldmlvdXNTdGF0ZS5ldmVudHMsXG4gICAgICAgIC4uLmV2ZW50c1xuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgLi4ucHJldmlvdXNTdGF0ZS5zaXplLFxuICAgICAgICAuLi5zaXplXG4gICAgICB9LFxuICAgICAgLi4ucmVzdCxcbiAgICAgIC8vIFNldCBhbmQgZ2V0IHJlZmVyIHRvIHRoaXMgcm9vdC1zdGF0ZVxuICAgICAgc2V0LFxuICAgICAgZ2V0LFxuICAgICAgLy8gTGF5ZXJzIGFyZSBhbGxvd2VkIHRvIG92ZXJyaWRlIGV2ZW50c1xuICAgICAgc2V0RXZlbnRzOiBldmVudHMgPT4gc2V0KHN0YXRlID0+ICh7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICBldmVudHM6IHtcbiAgICAgICAgICAuLi5zdGF0ZS5ldmVudHMsXG4gICAgICAgICAgLi4uZXZlbnRzXG4gICAgICAgIH1cbiAgICAgIH0pKVxuICAgIH0pKTtcbiAgICByZXR1cm4gc3RvcmU7XG4gIH0pO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFN1YnNjcmliZSB0byBwcmV2aW91cyByb290LXN0YXRlIGFuZCBjb3B5IGNoYW5nZXMgb3ZlciB0byB0aGUgbWlycm9yZWQgcG9ydGFsLXN0YXRlXG4gICAgY29uc3QgdW5zdWIgPSBwcmV2aW91c1Jvb3Quc3Vic2NyaWJlKHByZXYgPT4gdXNlUG9ydGFsU3RvcmUuc2V0U3RhdGUoc3RhdGUgPT4gaW5qZWN0KHByZXYsIHN0YXRlKSkpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB1bnN1YigpO1xuICAgICAgdXNlUG9ydGFsU3RvcmUuZGVzdHJveSgpO1xuICAgIH07XG4gIH0sIFtdKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICB1c2VQb3J0YWxTdG9yZS5zZXRTdGF0ZShpbmplY3RTdGF0ZSA9PiBpbmplY3QocHJldmlvdXNSb290LmdldFN0YXRlKCksIGluamVjdFN0YXRlKSk7XG4gIH0sIFtpbmplY3RdKTtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFJlYWN0LkZyYWdtZW50LCBudWxsLCByZWNvbmNpbGVyLmNyZWF0ZVBvcnRhbCggLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoY29udGV4dC5Qcm92aWRlciwge1xuICAgIHZhbHVlOiB1c2VQb3J0YWxTdG9yZVxuICB9LCBjaGlsZHJlbiksIHVzZVBvcnRhbFN0b3JlLCBudWxsKSk7XG59XG5yZWNvbmNpbGVyLmluamVjdEludG9EZXZUb29scyh7XG4gIGJ1bmRsZVR5cGU6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicgPyAwIDogMSxcbiAgcmVuZGVyZXJQYWNrYWdlTmFtZTogJ0ByZWFjdC10aHJlZS9maWJlcicsXG4gIHZlcnNpb246IFJlYWN0LnZlcnNpb25cbn0pO1xuY29uc3QgYWN0ID0gUmVhY3QudW5zdGFibGVfYWN0O1xuXG5leHBvcnQgeyB1c2VUaHJlZSBhcyBBLCBCbG9jayBhcyBCLCB1c2VGcmFtZSBhcyBDLCB1c2VHcmFwaCBhcyBELCBFcnJvckJvdW5kYXJ5IGFzIEUsIHVzZUxvYWRlciBhcyBGLCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0IGFzIGEsIGNyZWF0ZVJvb3QgYXMgYiwgY3JlYXRlRXZlbnRzIGFzIGMsIHVubW91bnRDb21wb25lbnRBdE5vZGUgYXMgZCwgZXh0ZW5kIGFzIGUsIGNvbnRleHQgYXMgZiwgY3JlYXRlUG9ydGFsIGFzIGcsIHJlY29uY2lsZXIgYXMgaCwgaXNSZWYgYXMgaSwgYXBwbHlQcm9wcyBhcyBqLCBkaXNwb3NlIGFzIGssIGludmFsaWRhdGUgYXMgbCwgYWR2YW5jZSBhcyBtLCBhZGRFZmZlY3QgYXMgbiwgYWRkQWZ0ZXJFZmZlY3QgYXMgbywgYWRkVGFpbCBhcyBwLCBmbHVzaEdsb2JhbEVmZmVjdHMgYXMgcSwgcmVuZGVyIGFzIHIsIGdldFJvb3RTdGF0ZSBhcyBzLCB0aHJlZVR5cGVzIGFzIHQsIHVzZU11dGFibGVDYWxsYmFjayBhcyB1LCBhY3QgYXMgdiwgYnVpbGRHcmFwaCBhcyB3LCByb290cyBhcyB4LCB1c2VJbnN0YW5jZUhhbmRsZSBhcyB5LCB1c2VTdG9yZSBhcyB6IH07XG4iXSwibmFtZXMiOlsiVEhSRUUiLCJSZWFjdCIsIkRlZmF1bHRFdmVudFByaW9yaXR5IiwiQ29udGludW91c0V2ZW50UHJpb3JpdHkiLCJEaXNjcmV0ZUV2ZW50UHJpb3JpdHkiLCJDb25jdXJyZW50Um9vdCIsImNyZWF0ZSIsIlJlY29uY2lsZXIiLCJ1bnN0YWJsZV9zY2hlZHVsZUNhbGxiYWNrIiwidW5zdGFibGVfSWRsZVByaW9yaXR5Iiwic3VzcGVuZCIsInByZWxvYWQiLCJjbGVhciIsInRocmVlVHlwZXMiLCJPYmplY3QiLCJmcmVlemUiLCJfX3Byb3RvX18iLCJjYXRhbG9ndWUiLCJleHRlbmQiLCJvYmplY3RzIiwiYXNzaWduIiwiY3JlYXRlUmVuZGVyZXIiLCJfcm9vdHMiLCJfZ2V0RXZlbnRQcmlvcml0eSIsImNyZWF0ZUluc3RhbmNlIiwidHlwZSIsImFyZ3MiLCJhdHRhY2giLCJwcm9wcyIsInJvb3QiLCJuYW1lIiwidG9VcHBlckNhc2UiLCJzbGljZSIsImluc3RhbmNlIiwib2JqZWN0IiwidW5kZWZpbmVkIiwiRXJyb3IiLCJwcmVwYXJlIiwicHJpbWl0aXZlIiwidGFyZ2V0IiwiQXJyYXkiLCJpc0FycmF5IiwibWVtb2l6ZWRQcm9wcyIsIl9fcjNmIiwiQnVmZmVyR2VvbWV0cnkiLCJNYXRlcmlhbCIsImFwcGx5UHJvcHMkMSIsImFwcGVuZENoaWxkIiwicGFyZW50SW5zdGFuY2UiLCJjaGlsZCIsImFkZGVkIiwiX2NoaWxkJF9fcjNmIiwiX3BhcmVudEluc3RhbmNlJF9fcjNmIiwiaXNPYmplY3QzRCIsImFkZCIsInB1c2giLCJwYXJlbnQiLCJ1cGRhdGVJbnN0YW5jZSIsImludmFsaWRhdGVJbnN0YW5jZSIsImluc2VydEJlZm9yZSIsImJlZm9yZUNoaWxkIiwiX2NoaWxkJF9fcjNmMiIsIl9wYXJlbnRJbnN0YW5jZSRfX3IzZjIiLCJkaXNwYXRjaEV2ZW50IiwicmVzdFNpYmxpbmdzIiwiY2hpbGRyZW4iLCJmaWx0ZXIiLCJzaWJsaW5nIiwiaW5kZXgiLCJpbmRleE9mIiwicmVtb3ZlUmVjdXJzaXZlIiwiYXJyYXkiLCJkaXNwb3NlIiwiZm9yRWFjaCIsInJlbW92ZUNoaWxkIiwiX3BhcmVudEluc3RhbmNlJF9fcjNmMyIsIl9jaGlsZCRfX3IzZjMiLCJfY2hpbGQkX19yM2Y1IiwieCIsImRldGFjaCIsIl9jaGlsZCRfX3IzZjQiLCJyZW1vdmUiLCJyZW1vdmVJbnRlcmFjdGl2aXR5IiwiaXNQcmltaXRpdmUiLCJzaG91bGREaXNwb3NlIiwiX2NoaWxkJF9fcjNmNiIsImUiLCJzd2l0Y2hJbnN0YW5jZSIsIm5ld1Byb3BzIiwiZmliZXIiLCJfaW5zdGFuY2UkX19yM2YiLCJuZXdJbnN0YW5jZSIsImF1dG9SZW1vdmVkQmVmb3JlQXBwZW5kIiwicmF5Y2FzdCIsImV2ZW50Q291bnQiLCJyb290U3RhdGUiLCJnZXRTdGF0ZSIsImludGVybmFsIiwiaW50ZXJhY3Rpb24iLCJhbHRlcm5hdGUiLCJzdGF0ZU5vZGUiLCJyZWYiLCJjdXJyZW50IiwiaGFuZGxlVGV4dEluc3RhbmNlIiwiY29uc29sZSIsIndhcm4iLCJyZWNvbmNpbGVyIiwiYXBwZW5kSW5pdGlhbENoaWxkIiwic3VwcG9ydHNNdXRhdGlvbiIsImlzUHJpbWFyeVJlbmRlcmVyIiwic3VwcG9ydHNQZXJzaXN0ZW5jZSIsInN1cHBvcnRzSHlkcmF0aW9uIiwibm9UaW1lb3V0IiwiYXBwZW5kQ2hpbGRUb0NvbnRhaW5lciIsImNvbnRhaW5lciIsInNjZW5lIiwicmVtb3ZlQ2hpbGRGcm9tQ29udGFpbmVyIiwiaW5zZXJ0SW5Db250YWluZXJCZWZvcmUiLCJnZXRSb290SG9zdENvbnRleHQiLCJnZXRDaGlsZEhvc3RDb250ZXh0IiwicGFyZW50SG9zdENvbnRleHQiLCJmaW5hbGl6ZUluaXRpYWxDaGlsZHJlbiIsIl9pbnN0YW5jZSRfX3IzZjIiLCJsb2NhbFN0YXRlIiwiQm9vbGVhbiIsImhhbmRsZXJzIiwicHJlcGFyZVVwZGF0ZSIsIl90eXBlIiwib2xkUHJvcHMiLCJfaW5zdGFuY2UkX19yM2YzIiwiYXJnc05ldyIsImNOIiwicmVzdE5ldyIsImFyZ3NPbGQiLCJjTyIsInJlc3RPbGQiLCJzb21lIiwidmFsdWUiLCJkaWZmIiwiZGlmZlByb3BzIiwiY2hhbmdlcyIsImxlbmd0aCIsImNvbW1pdFVwZGF0ZSIsInJlY29uc3RydWN0IiwiX29sZFByb3BzIiwiY29tbWl0TW91bnQiLCJfcHJvcHMiLCJfaW50IiwiX2luc3RhbmNlJF9fcjNmNCIsImdldFB1YmxpY0luc3RhbmNlIiwicHJlcGFyZUZvckNvbW1pdCIsInByZXBhcmVQb3J0YWxNb3VudCIsInJlc2V0QWZ0ZXJDb21taXQiLCJzaG91bGRTZXRUZXh0Q29udGVudCIsImNsZWFyQ29udGFpbmVyIiwiaGlkZUluc3RhbmNlIiwiX2luc3RhbmNlJF9fcjNmNSIsInZpc2libGUiLCJ1bmhpZGVJbnN0YW5jZSIsIl9pbnN0YW5jZSRfX3IzZjYiLCJjcmVhdGVUZXh0SW5zdGFuY2UiLCJoaWRlVGV4dEluc3RhbmNlIiwidW5oaWRlVGV4dEluc3RhbmNlIiwiZ2V0Q3VycmVudEV2ZW50UHJpb3JpdHkiLCJiZWZvcmVBY3RpdmVJbnN0YW5jZUJsdXIiLCJhZnRlckFjdGl2ZUluc3RhbmNlQmx1ciIsImRldGFjaERlbGV0ZWRJbnN0YW5jZSIsIm5vdyIsInBlcmZvcm1hbmNlIiwiaXMiLCJmdW4iLCJEYXRlIiwic2NoZWR1bGVUaW1lb3V0Iiwic2V0VGltZW91dCIsImNhbmNlbFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJhcHBseVByb3BzIiwiX3dpbmRvdyRkb2N1bWVudCIsIl93aW5kb3ckbmF2aWdhdG9yIiwiaGFzQ29sb3JTcGFjZSIsImdldENvbG9yTWFuYWdlbWVudCIsIl9Db2xvck1hbmFnZW1lbnQiLCJDb2xvck1hbmFnZW1lbnQiLCJpc09ydGhvZ3JhcGhpY0NhbWVyYSIsImRlZiIsImlzUmVmIiwib2JqIiwiaGFzT3duUHJvcGVydHkiLCJ1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0Iiwid2luZG93IiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwibmF2aWdhdG9yIiwicHJvZHVjdCIsInVzZUxheW91dEVmZmVjdCIsInVzZUVmZmVjdCIsInVzZU11dGFibGVDYWxsYmFjayIsImZuIiwidXNlUmVmIiwiQmxvY2siLCJzZXQiLCJQcm9taXNlIiwiRXJyb3JCb3VuZGFyeSIsIkNvbXBvbmVudCIsImNvbnN0cnVjdG9yIiwic3RhdGUiLCJlcnJvciIsImNvbXBvbmVudERpZENhdGNoIiwiZXJyIiwicmVuZGVyIiwiZ2V0RGVyaXZlZFN0YXRlRnJvbUVycm9yIiwiREVGQVVMVCIsIkRFRkFVTFRTIiwiTWFwIiwiaXNEaWZmU2V0IiwibWVtb2l6ZWQiLCJjYWxjdWxhdGVEcHIiLCJkcHIiLCJfd2luZG93JGRldmljZVBpeGVsUmEiLCJkZXZpY2VQaXhlbFJhdGlvIiwiTWF0aCIsIm1pbiIsIm1heCIsImdldFJvb3RTdGF0ZSIsIl9yM2YiLCJhIiwiYXJyIiwic3RyIiwibnVtIiwiYm9vIiwidW5kIiwiZXF1IiwiYiIsImFycmF5cyIsInN0cmljdCIsImlzT2JqIiwiaXNBcnIiLCJpIiwia2V5cyIsImJ1aWxkR3JhcGgiLCJkYXRhIiwibm9kZXMiLCJtYXRlcmlhbHMiLCJ0cmF2ZXJzZSIsIm1hdGVyaWFsIiwicCIsInByZXZpb3VzQXR0YWNoIiwicmVzb2x2ZSIsImtleSIsImluY2x1ZGVzIiwiZW50cmllcyIsInNwbGl0IiwibGFzdCIsInBvcCIsInJlZHVjZSIsImFjYyIsIklOREVYX1JFR0VYIiwidGVzdCIsInJlcGxhY2UiLCJwcmV2aW91cyIsImtOIiwick4iLCJjUCIsImtQIiwiclAiLCJwcmV2aW91c0tleXMiLCJ1bnNoaWZ0IiwicHJvcCIsInN0YXJ0c1dpdGgiLCJfX0RFVl9fIiwicHJvY2VzcyIsIl9yb290JGdldFN0YXRlIiwicHJldkhhbmRsZXJzIiwiaXNFdmVudCIsInNSR0JFbmNvZGluZyIsIlNSR0JDb2xvclNwYWNlIiwiTGluZWFyU1JHQkNvbG9yU3BhY2UiLCJjdXJyZW50SW5zdGFuY2UiLCJ0YXJnZXRQcm9wIiwicmV2ZXJzZUVudHJpZXMiLCJyZXZlcnNlIiwiY3RvciIsImdldCIsImNvcHkiLCJMYXllcnMiLCJmcm9tQXJyYXkiLCJpc0NvbG9yIiwiQ29sb3IiLCJzZXRTY2FsYXIiLCJtYXNrIiwibGluZWFyIiwiY29udmVydFNSR0JUb0xpbmVhciIsIlRleHR1cmUiLCJmb3JtYXQiLCJSR0JBRm9ybWF0IiwiVW5zaWduZWRCeXRlVHlwZSIsInRleHR1cmUiLCJnbCIsImNvbG9yU3BhY2UiLCJvdXRwdXRDb2xvclNwYWNlIiwiZW5jb2RpbmciLCJvdXRwdXRFbmNvZGluZyIsInNwbGljZSIsImlzQ2lyY3VsYXIiLCJfaW5zdGFuY2UkX19yM2Y1JHJvb3QiLCJmcmFtZXMiLCJpbnZhbGlkYXRlIiwib25VcGRhdGUiLCJ1cGRhdGVDYW1lcmEiLCJjYW1lcmEiLCJzaXplIiwibWFudWFsIiwibGVmdCIsIndpZHRoIiwicmlnaHQiLCJ0b3AiLCJoZWlnaHQiLCJib3R0b20iLCJhc3BlY3QiLCJ1cGRhdGVQcm9qZWN0aW9uTWF0cml4IiwidXBkYXRlTWF0cml4V29ybGQiLCJtYWtlSWQiLCJldmVudCIsImV2ZW50T2JqZWN0IiwidXVpZCIsImluc3RhbmNlSWQiLCJnZXRFdmVudFByaW9yaXR5IiwiX2dsb2JhbFNjb3BlJGV2ZW50IiwiZ2xvYmFsU2NvcGUiLCJzZWxmIiwicmVsZWFzZUludGVybmFsUG9pbnRlckNhcHR1cmUiLCJjYXB0dXJlZE1hcCIsImNhcHR1cmVzIiwicG9pbnRlcklkIiwiY2FwdHVyZURhdGEiLCJkZWxldGUiLCJyZWxlYXNlUG9pbnRlckNhcHR1cmUiLCJzdG9yZSIsIm8iLCJpbml0aWFsSGl0cyIsImhvdmVyZWQiLCJjcmVhdGVFdmVudHMiLCJjYWxjdWxhdGVEaXN0YW5jZSIsImR4Iiwib2Zmc2V0WCIsImluaXRpYWxDbGljayIsImR5Iiwib2Zmc2V0WSIsInJvdW5kIiwic3FydCIsImZpbHRlclBvaW50ZXJFdmVudHMiLCJpbnRlcnNlY3QiLCJkdXBsaWNhdGVzIiwiU2V0IiwiaW50ZXJzZWN0aW9ucyIsImV2ZW50c09iamVjdHMiLCJyYXljYXN0ZXIiLCJwcmV2aW91c1Jvb3QiLCJldmVudHMiLCJjb21wdXRlIiwiaGFuZGxlUmF5Y2FzdCIsImVuYWJsZWQiLCJfc3RhdGUkcHJldmlvdXNSb290IiwiaW50ZXJzZWN0T2JqZWN0IiwiaGl0cyIsImZsYXRNYXAiLCJzb3J0IiwiYVN0YXRlIiwiYlN0YXRlIiwiZGlzdGFuY2UiLCJwcmlvcml0eSIsIml0ZW0iLCJpZCIsImhhcyIsImhpdCIsIl9yM2YyIiwidmFsdWVzIiwiaW50ZXJzZWN0aW9uIiwiaGFuZGxlSW50ZXJzZWN0cyIsImRlbHRhIiwiY2FsbGJhY2siLCJzdG9wcGVkIiwicG9pbnRlciIsInVucHJvamVjdGVkUG9pbnQiLCJWZWN0b3IzIiwieSIsInVucHJvamVjdCIsImhhc1BvaW50ZXJDYXB0dXJlIiwiX2ludGVybmFsJGNhcHR1cmVkTWFwIiwiX2ludGVybmFsJGNhcHR1cmVkTWFwMiIsInNldFBvaW50ZXJDYXB0dXJlIiwiZXh0cmFjdEV2ZW50UHJvcHMiLCJwcm9wZXJ0eSIsInJheWNhc3RFdmVudCIsInJheSIsInN0b3BQcm9wYWdhdGlvbiIsImNhcHR1cmVzRm9yUG9pbnRlciIsImZyb20iLCJmaW5kIiwiaGlnaGVyIiwiY2FuY2VsUG9pbnRlciIsImN1cnJlbnRUYXJnZXQiLCJuYXRpdmVFdmVudCIsImhvdmVyZWRPYmoiLCJvblBvaW50ZXJPdXQiLCJvblBvaW50ZXJMZWF2ZSIsInBvaW50ZXJNaXNzZWQiLCJvblBvaW50ZXJNaXNzZWQiLCJoYW5kbGVQb2ludGVyIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwiaGFuZGxlRXZlbnQiLCJsYXN0RXZlbnQiLCJpc1BvaW50ZXJNb3ZlIiwiaXNDbGlja0V2ZW50IiwibWFwIiwib25JbnRlcnNlY3QiLCJvblBvaW50ZXJPdmVyIiwib25Qb2ludGVyRW50ZXIiLCJob3ZlcmVkSXRlbSIsIm9uUG9pbnRlck1vdmUiLCJoYW5kbGVyIiwicHJpdmF0ZUtleXMiLCJpc1JlbmRlcmVyIiwiY29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJjcmVhdGVTdG9yZSIsImFkdmFuY2UiLCJwb3NpdGlvbiIsImRlZmF1bHRUYXJnZXQiLCJ0ZW1wVGFyZ2V0IiwiZ2V0Q3VycmVudFZpZXdwb3J0IiwiZ2V0V29ybGRQb3NpdGlvbiIsImRpc3RhbmNlVG8iLCJ6b29tIiwiZmFjdG9yIiwiZm92IiwiUEkiLCJoIiwidGFuIiwidyIsInBlcmZvcm1hbmNlVGltZW91dCIsInNldFBlcmZvcm1hbmNlQ3VycmVudCIsIlZlY3RvcjIiLCJjb25uZWN0ZWQiLCJ4ciIsInRpbWVzdGFtcCIsInJ1bkdsb2JhbEVmZmVjdHMiLCJsZWdhY3kiLCJmbGF0IiwiY29udHJvbHMiLCJjbG9jayIsIkNsb2NrIiwibW91c2UiLCJmcmFtZWxvb3AiLCJkZWJvdW5jZSIsInJlZ3Jlc3MiLCJ1cGRhdGVTdHlsZSIsInZpZXdwb3J0IiwiaW5pdGlhbERwciIsInNldEV2ZW50cyIsInNldFNpemUiLCJzZXREcHIiLCJyZXNvbHZlZCIsInNldEZyYW1lbG9vcCIsInN0b3AiLCJlbGFwc2VkVGltZSIsInN0YXJ0IiwiYWN0aXZlIiwiY3JlYXRlUmVmIiwic3Vic2NyaWJlcnMiLCJzdWJzY3JpYmUiLCJzIiwib2xkU2l6ZSIsIm9sZERwciIsIm9sZENhbWVyYSIsIl9zaXplJHVwZGF0ZVN0eWxlIiwic2V0UGl4ZWxSYXRpbyIsIkhUTUxDYW52YXNFbGVtZW50IiwiZG9tRWxlbWVudCIsImNyZWF0ZVN1YnMiLCJzdWJzIiwic3ViIiwiZ2xvYmFsRWZmZWN0cyIsImdsb2JhbEFmdGVyRWZmZWN0cyIsImdsb2JhbFRhaWxFZmZlY3RzIiwiYWRkRWZmZWN0IiwiYWRkQWZ0ZXJFZmZlY3QiLCJhZGRUYWlsIiwicnVuIiwiZWZmZWN0cyIsImZsdXNoR2xvYmFsRWZmZWN0cyIsInN1YnNjcmlwdGlvbiIsInJlbmRlciQxIiwiZnJhbWUiLCJnZXREZWx0YSIsIm9sZFRpbWUiLCJjcmVhdGVMb29wIiwicm9vdHMiLCJydW5uaW5nIiwicmVwZWF0IiwibG9vcCIsIl9zdGF0ZSRnbCR4ciIsImlzUHJlc2VudGluZyIsImNhbmNlbEFuaW1hdGlvbkZyYW1lIiwiX3N0YXRlJGdsJHhyMiIsInVzZUluc3RhbmNlSGFuZGxlIiwidXNlU3RvcmUiLCJ1c2VDb250ZXh0IiwidXNlVGhyZWUiLCJzZWxlY3RvciIsImVxdWFsaXR5Rm4iLCJ1c2VGcmFtZSIsInJlbmRlclByaW9yaXR5IiwidXNlR3JhcGgiLCJ1c2VNZW1vIiwibWVtb2l6ZWRMb2FkZXJzIiwiV2Vha01hcCIsImxvYWRpbmdGbiIsImV4dGVuc2lvbnMiLCJvblByb2dyZXNzIiwiUHJvdG8iLCJpbnB1dCIsImxvYWRlciIsImFsbCIsInJlcyIsInJlamVjdCIsImxvYWQiLCJtZXNzYWdlIiwiZmluYWxseSIsInVzZUxvYWRlciIsInJlc3VsdHMiLCJlcXVhbCIsInNoYWxsb3dMb29zZSIsImNyZWF0ZVJlbmRlcmVySW5zdGFuY2UiLCJjYW52YXMiLCJjdXN0b21SZW5kZXJlciIsIldlYkdMUmVuZGVyZXIiLCJwb3dlclByZWZlcmVuY2UiLCJhbnRpYWxpYXMiLCJhbHBoYSIsImNvbXB1dGVJbml0aWFsU2l6ZSIsImRlZmF1bHRTaXplIiwicGFyZW50RWxlbWVudCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsIk9mZnNjcmVlbkNhbnZhcyIsImNyZWF0ZVJvb3QiLCJwcmV2Um9vdCIsInByZXZGaWJlciIsInByZXZTdG9yZSIsImxvZ1JlY292ZXJhYmxlRXJyb3IiLCJyZXBvcnRFcnJvciIsImNyZWF0ZUNvbnRhaW5lciIsIm9uQ3JlYXRlZCIsImNvbmZpZ3VyZWQiLCJsYXN0Q2FtZXJhIiwiY29uZmlndXJlIiwiZ2xDb25maWciLCJwcm9wc1NpemUiLCJzY2VuZU9wdGlvbnMiLCJvbkNyZWF0ZWRDYWxsYmFjayIsInNoYWRvd3MiLCJvcnRob2dyYXBoaWMiLCJyYXljYXN0T3B0aW9ucyIsImNhbWVyYU9wdGlvbnMiLCJSYXljYXN0ZXIiLCJwYXJhbXMiLCJvcHRpb25zIiwiaXNDYW1lcmEiLCJDYW1lcmEiLCJPcnRob2dyYXBoaWNDYW1lcmEiLCJQZXJzcGVjdGl2ZUNhbWVyYSIsInoiLCJyb3RhdGlvbiIsImxvb2tBdCIsIlNjZW5lIiwiX2dsJHhyIiwiaGFuZGxlWFJGcmFtZSIsImhhbmRsZVNlc3Npb25DaGFuZ2UiLCJzZXRBbmltYXRpb25Mb29wIiwiY29ubmVjdCIsImFkZEV2ZW50TGlzdGVuZXIiLCJkaXNjb25uZWN0IiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInNoYWRvd01hcCIsIm9sZEVuYWJsZWQiLCJvbGRUeXBlIiwiUENGU29mdFNoYWRvd01hcCIsIl90eXBlcyRzaGFkb3dzIiwidHlwZXMiLCJiYXNpYyIsIkJhc2ljU2hhZG93TWFwIiwicGVyY2VudGFnZSIsIlBDRlNoYWRvd01hcCIsInNvZnQiLCJ2YXJpYW5jZSIsIlZTTVNoYWRvd01hcCIsIm5lZWRzVXBkYXRlIiwibGVnYWN5TW9kZSIsIkxpbmVhckVuY29kaW5nIiwidG9uZU1hcHBpbmciLCJOb1RvbmVNYXBwaW5nIiwiQUNFU0ZpbG1pY1RvbmVNYXBwaW5nIiwidXBkYXRlQ29udGFpbmVyIiwiUHJvdmlkZXIiLCJyb290RWxlbWVudCIsInVubW91bnQiLCJ1bm1vdW50Q29tcG9uZW50QXROb2RlIiwiY29uZmlnIiwiX3N0YXRlJGdsIiwiX3N0YXRlJGdsJHJlbmRlckxpc3RzIiwiX3N0YXRlJGdsMiIsIl9zdGF0ZSRnbDMiLCJyZW5kZXJMaXN0cyIsImZvcmNlQ29udGV4dExvc3MiLCJjcmVhdGVQb3J0YWwiLCJQb3J0YWwiLCJyZXN0IiwidXNlU3RhdGUiLCJpbmplY3QiLCJ1c2VDYWxsYmFjayIsImluamVjdFN0YXRlIiwidXNlUG9ydGFsU3RvcmUiLCJwcmV2aW91c1N0YXRlIiwidW5zdWIiLCJwcmV2Iiwic2V0U3RhdGUiLCJkZXN0cm95IiwiRnJhZ21lbnQiLCJpbmplY3RJbnRvRGV2VG9vbHMiLCJidW5kbGVUeXBlIiwicmVuZGVyZXJQYWNrYWdlTmFtZSIsInZlcnNpb24iLCJhY3QiLCJ1bnN0YWJsZV9hY3QiLCJBIiwiQiIsIkMiLCJEIiwiRSIsIkYiLCJjIiwiZCIsImYiLCJnIiwiaiIsImsiLCJsIiwibSIsIm4iLCJxIiwiciIsInQiLCJ1IiwidiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/index-5918012a.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Canvas: () => (/* binding */ Canvas),\n/* harmony export */   ReactThreeFiber: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   _roots: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   act: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   addAfterEffect: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   addEffect: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   addTail: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   advance: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   applyProps: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   buildGraph: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   context: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   createEvents: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   createPortal: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   createRoot: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   dispose: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   events: () => (/* binding */ createPointerEvents),\n/* harmony export */   extend: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   flushGlobalEffects: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   getRootState: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   invalidate: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   reconciler: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   render: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   unmountComponentAtNode: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   useFrame: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.C),\n/* harmony export */   useGraph: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.D),\n/* harmony export */   useInstanceHandle: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   useLoader: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.F),\n/* harmony export */   useStore: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   useThree: () => (/* reexport safe */ _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.A)\n/* harmony export */ });\n/* harmony import */ var _index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index-5918012a.esm.js */ \"(ssr)/./node_modules/@react-three/fiber/dist/index-5918012a.esm.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react_use_measure__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-use-measure */ \"(ssr)/./node_modules/react-use-measure/dist/index.js\");\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst DOM_EVENTS = {\n    onClick: [\n        \"click\",\n        false\n    ],\n    onContextMenu: [\n        \"contextmenu\",\n        false\n    ],\n    onDoubleClick: [\n        \"dblclick\",\n        false\n    ],\n    onWheel: [\n        \"wheel\",\n        true\n    ],\n    onPointerDown: [\n        \"pointerdown\",\n        true\n    ],\n    onPointerUp: [\n        \"pointerup\",\n        true\n    ],\n    onPointerLeave: [\n        \"pointerleave\",\n        true\n    ],\n    onPointerMove: [\n        \"pointermove\",\n        true\n    ],\n    onPointerCancel: [\n        \"pointercancel\",\n        true\n    ],\n    onLostPointerCapture: [\n        \"lostpointercapture\",\n        true\n    ]\n};\n/** Default R3F event manager for web */ function createPointerEvents(store) {\n    const { handlePointer } = (0,_index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.c)(store);\n    return {\n        priority: 1,\n        enabled: true,\n        compute (event, state, previous) {\n            // https://github.com/pmndrs/react-three-fiber/pull/782\n            // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n            state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n            state.raycaster.setFromCamera(state.pointer, state.camera);\n        },\n        connected: undefined,\n        handlers: Object.keys(DOM_EVENTS).reduce((acc, key)=>({\n                ...acc,\n                [key]: handlePointer(key)\n            }), {}),\n        update: ()=>{\n            var _internal$lastEvent;\n            const { events, internal } = store.getState();\n            if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n        },\n        connect: (target)=>{\n            var _events$handlers;\n            const { set, events } = store.getState();\n            events.disconnect == null ? void 0 : events.disconnect();\n            set((state)=>({\n                    events: {\n                        ...state.events,\n                        connected: target\n                    }\n                }));\n            Object.entries((_events$handlers = events.handlers) != null ? _events$handlers : []).forEach(([name, event])=>{\n                const [eventName, passive] = DOM_EVENTS[name];\n                target.addEventListener(eventName, event, {\n                    passive\n                });\n            });\n        },\n        disconnect: ()=>{\n            const { set, events } = store.getState();\n            if (events.connected) {\n                var _events$handlers2;\n                Object.entries((_events$handlers2 = events.handlers) != null ? _events$handlers2 : []).forEach(([name, event])=>{\n                    if (events && events.connected instanceof HTMLElement) {\n                        const [eventName] = DOM_EVENTS[name];\n                        events.connected.removeEventListener(eventName, event);\n                    }\n                });\n                set((state)=>({\n                        events: {\n                            ...state.events,\n                            connected: undefined\n                        }\n                    }));\n            }\n        }\n    };\n}\nconst CanvasImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function Canvas({ children, fallback, resize, style, gl, events = createPointerEvents, eventSource, eventPrefix, shadows, linear, flat, legacy, orthographic, frameloop, dpr, performance, raycaster, camera, scene, onPointerMissed, onCreated, ...props }, forwardedRef) {\n    // Create a known catalogue of Threejs-native elements\n    // This will include the entire THREE namespace by default, users can extend\n    // their own elements by using the createRoot API instead\n    react__WEBPACK_IMPORTED_MODULE_2__.useMemo(()=>(0,_index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.e)(three__WEBPACK_IMPORTED_MODULE_6__), []);\n    const Bridge = (0,its_fine__WEBPACK_IMPORTED_MODULE_7__.useContextBridge)();\n    const [containerRef, containerRect] = (0,react_use_measure__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({\n        scroll: true,\n        debounce: {\n            scroll: 50,\n            resize: 0\n        },\n        ...resize\n    });\n    const canvasRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n    const divRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n    react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle(forwardedRef, ()=>canvasRef.current);\n    const handlePointerMissed = (0,_index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)(onPointerMissed);\n    const [block, setBlock] = react__WEBPACK_IMPORTED_MODULE_2__.useState(false);\n    const [error, setError] = react__WEBPACK_IMPORTED_MODULE_2__.useState(false);\n    // Suspend this component if block is a promise (2nd run)\n    if (block) throw block;\n    // Throw exception outwards if anything within canvas throws\n    if (error) throw error;\n    const root = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n    (0,_index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(()=>{\n        const canvas = canvasRef.current;\n        if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n            if (!root.current) root.current = (0,_index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.b)(canvas);\n            root.current.configure({\n                gl,\n                events,\n                shadows,\n                linear,\n                flat,\n                legacy,\n                orthographic,\n                frameloop,\n                dpr,\n                performance,\n                raycaster,\n                camera,\n                scene,\n                size: containerRect,\n                // Pass mutable reference to onPointerMissed so it's free to update\n                onPointerMissed: (...args)=>handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n                onCreated: (state)=>{\n                    // Connect to event source\n                    state.events.connect == null ? void 0 : state.events.connect(eventSource ? (0,_index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.i)(eventSource) ? eventSource.current : eventSource : divRef.current);\n                    // Set up compute function\n                    if (eventPrefix) {\n                        state.setEvents({\n                            compute: (event, state)=>{\n                                const x = event[eventPrefix + \"X\"];\n                                const y = event[eventPrefix + \"Y\"];\n                                state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                                state.raycaster.setFromCamera(state.pointer, state.camera);\n                            }\n                        });\n                    }\n                    // Call onCreated callback\n                    onCreated == null ? void 0 : onCreated(state);\n                }\n            });\n            root.current.render(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(Bridge, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(_index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.E, {\n                set: setError\n            }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n                fallback: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(_index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.B, {\n                    set: setBlock\n                })\n            }, children))));\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_2__.useEffect(()=>{\n        const canvas = canvasRef.current;\n        if (canvas) return ()=>(0,_index_5918012a_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)(canvas);\n    }, []);\n    // When the event source is not this div, we need to set pointer-events to none\n    // Or else the canvas will block events from reaching the event source\n    const pointerEvents = eventSource ? \"none\" : \"auto\";\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        ref: divRef,\n        style: {\n            position: \"relative\",\n            width: \"100%\",\n            height: \"100%\",\n            overflow: \"hidden\",\n            pointerEvents,\n            ...style\n        }\n    }, props), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"div\", {\n        ref: containerRef,\n        style: {\n            width: \"100%\",\n            height: \"100%\"\n        }\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"canvas\", {\n        ref: canvasRef,\n        style: {\n            display: \"block\"\n        }\n    }, fallback)));\n});\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */ const Canvas = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(function CanvasWrapper(props, ref) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(its_fine__WEBPACK_IMPORTED_MODULE_7__.FiberProvider, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(CanvasImpl, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, props, {\n        ref: ref\n    })));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */ \nif (true) {\n    (function() {\n        \"use strict\";\n        /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */ if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== \"undefined\" && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart === \"function\") {\n            __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n        }\n        var enableSchedulerDebugging = false;\n        var enableProfiling = false;\n        var frameYieldMs = 5;\n        function push(heap, node) {\n            var index = heap.length;\n            heap.push(node);\n            siftUp(heap, node, index);\n        }\n        function peek(heap) {\n            return heap.length === 0 ? null : heap[0];\n        }\n        function pop(heap) {\n            if (heap.length === 0) {\n                return null;\n            }\n            var first = heap[0];\n            var last = heap.pop();\n            if (last !== first) {\n                heap[0] = last;\n                siftDown(heap, last, 0);\n            }\n            return first;\n        }\n        function siftUp(heap, node, i) {\n            var index = i;\n            while(index > 0){\n                var parentIndex = index - 1 >>> 1;\n                var parent = heap[parentIndex];\n                if (compare(parent, node) > 0) {\n                    // The parent is larger. Swap positions.\n                    heap[parentIndex] = node;\n                    heap[index] = parent;\n                    index = parentIndex;\n                } else {\n                    // The parent is smaller. Exit.\n                    return;\n                }\n            }\n        }\n        function siftDown(heap, node, i) {\n            var index = i;\n            var length = heap.length;\n            var halfLength = length >>> 1;\n            while(index < halfLength){\n                var leftIndex = (index + 1) * 2 - 1;\n                var left = heap[leftIndex];\n                var rightIndex = leftIndex + 1;\n                var right = heap[rightIndex]; // If the left or right node is smaller, swap with the smaller of those.\n                if (compare(left, node) < 0) {\n                    if (rightIndex < length && compare(right, left) < 0) {\n                        heap[index] = right;\n                        heap[rightIndex] = node;\n                        index = rightIndex;\n                    } else {\n                        heap[index] = left;\n                        heap[leftIndex] = node;\n                        index = leftIndex;\n                    }\n                } else if (rightIndex < length && compare(right, node) < 0) {\n                    heap[index] = right;\n                    heap[rightIndex] = node;\n                    index = rightIndex;\n                } else {\n                    // Neither child is smaller. Exit.\n                    return;\n                }\n            }\n        }\n        function compare(a, b) {\n            // Compare sort index first, then task id.\n            var diff = a.sortIndex - b.sortIndex;\n            return diff !== 0 ? diff : a.id - b.id;\n        }\n        // TODO: Use symbols?\n        var ImmediatePriority = 1;\n        var UserBlockingPriority = 2;\n        var NormalPriority = 3;\n        var LowPriority = 4;\n        var IdlePriority = 5;\n        function markTaskErrored(task, ms) {}\n        /* eslint-disable no-var */ var hasPerformanceNow = typeof performance === \"object\" && typeof performance.now === \"function\";\n        if (hasPerformanceNow) {\n            var localPerformance = performance;\n            exports.unstable_now = function() {\n                return localPerformance.now();\n            };\n        } else {\n            var localDate = Date;\n            var initialTime = localDate.now();\n            exports.unstable_now = function() {\n                return localDate.now() - initialTime;\n            };\n        } // Max 31 bit integer. The max integer size in V8 for 32-bit systems.\n        // Math.pow(2, 30) - 1\n        // 0b111111111111111111111111111111\n        var maxSigned31BitInt = 1073741823; // Times out immediately\n        var IMMEDIATE_PRIORITY_TIMEOUT = -1; // Eventually times out\n        var USER_BLOCKING_PRIORITY_TIMEOUT = 250;\n        var NORMAL_PRIORITY_TIMEOUT = 5000;\n        var LOW_PRIORITY_TIMEOUT = 10000; // Never times out\n        var IDLE_PRIORITY_TIMEOUT = maxSigned31BitInt; // Tasks are stored on a min heap\n        var taskQueue = [];\n        var timerQueue = []; // Incrementing id counter. Used to maintain insertion order.\n        var taskIdCounter = 1; // Pausing the scheduler is useful for debugging.\n        var currentTask = null;\n        var currentPriorityLevel = NormalPriority; // This is set while performing work, to prevent re-entrance.\n        var isPerformingWork = false;\n        var isHostCallbackScheduled = false;\n        var isHostTimeoutScheduled = false; // Capture local references to native APIs, in case a polyfill overrides them.\n        var localSetTimeout = typeof setTimeout === \"function\" ? setTimeout : null;\n        var localClearTimeout = typeof clearTimeout === \"function\" ? clearTimeout : null;\n        var localSetImmediate = typeof setImmediate !== \"undefined\" ? setImmediate : null; // IE and Node.js + jsdom\n        var isInputPending = typeof navigator !== \"undefined\" && navigator.scheduling !== undefined && navigator.scheduling.isInputPending !== undefined ? navigator.scheduling.isInputPending.bind(navigator.scheduling) : null;\n        function advanceTimers(currentTime) {\n            // Check for tasks that are no longer delayed and add them to the queue.\n            var timer = peek(timerQueue);\n            while(timer !== null){\n                if (timer.callback === null) {\n                    // Timer was cancelled.\n                    pop(timerQueue);\n                } else if (timer.startTime <= currentTime) {\n                    // Timer fired. Transfer to the task queue.\n                    pop(timerQueue);\n                    timer.sortIndex = timer.expirationTime;\n                    push(taskQueue, timer);\n                } else {\n                    // Remaining timers are pending.\n                    return;\n                }\n                timer = peek(timerQueue);\n            }\n        }\n        function handleTimeout(currentTime) {\n            isHostTimeoutScheduled = false;\n            advanceTimers(currentTime);\n            if (!isHostCallbackScheduled) {\n                if (peek(taskQueue) !== null) {\n                    isHostCallbackScheduled = true;\n                    requestHostCallback(flushWork);\n                } else {\n                    var firstTimer = peek(timerQueue);\n                    if (firstTimer !== null) {\n                        requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n                    }\n                }\n            }\n        }\n        function flushWork(hasTimeRemaining, initialTime) {\n            isHostCallbackScheduled = false;\n            if (isHostTimeoutScheduled) {\n                // We scheduled a timeout but it's no longer needed. Cancel it.\n                isHostTimeoutScheduled = false;\n                cancelHostTimeout();\n            }\n            isPerformingWork = true;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n                if (enableProfiling) {\n                    try {\n                        return workLoop(hasTimeRemaining, initialTime);\n                    } catch (error) {\n                        if (currentTask !== null) {\n                            var currentTime = exports.unstable_now();\n                            markTaskErrored(currentTask, currentTime);\n                            currentTask.isQueued = false;\n                        }\n                        throw error;\n                    }\n                } else {\n                    // No catch in prod code path.\n                    return workLoop(hasTimeRemaining, initialTime);\n                }\n            } finally{\n                currentTask = null;\n                currentPriorityLevel = previousPriorityLevel;\n                isPerformingWork = false;\n            }\n        }\n        function workLoop(hasTimeRemaining, initialTime) {\n            var currentTime = initialTime;\n            advanceTimers(currentTime);\n            currentTask = peek(taskQueue);\n            while(currentTask !== null && !enableSchedulerDebugging){\n                if (currentTask.expirationTime > currentTime && (!hasTimeRemaining || shouldYieldToHost())) {\n                    break;\n                }\n                var callback = currentTask.callback;\n                if (typeof callback === \"function\") {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n                    var continuationCallback = callback(didUserCallbackTimeout);\n                    currentTime = exports.unstable_now();\n                    if (typeof continuationCallback === \"function\") {\n                        currentTask.callback = continuationCallback;\n                    } else {\n                        if (currentTask === peek(taskQueue)) {\n                            pop(taskQueue);\n                        }\n                    }\n                    advanceTimers(currentTime);\n                } else {\n                    pop(taskQueue);\n                }\n                currentTask = peek(taskQueue);\n            } // Return whether there's additional work\n            if (currentTask !== null) {\n                return true;\n            } else {\n                var firstTimer = peek(timerQueue);\n                if (firstTimer !== null) {\n                    requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n                }\n                return false;\n            }\n        }\n        function unstable_runWithPriority(priorityLevel, eventHandler) {\n            switch(priorityLevel){\n                case ImmediatePriority:\n                case UserBlockingPriority:\n                case NormalPriority:\n                case LowPriority:\n                case IdlePriority:\n                    break;\n                default:\n                    priorityLevel = NormalPriority;\n            }\n            var previousPriorityLevel = currentPriorityLevel;\n            currentPriorityLevel = priorityLevel;\n            try {\n                return eventHandler();\n            } finally{\n                currentPriorityLevel = previousPriorityLevel;\n            }\n        }\n        function unstable_next(eventHandler) {\n            var priorityLevel;\n            switch(currentPriorityLevel){\n                case ImmediatePriority:\n                case UserBlockingPriority:\n                case NormalPriority:\n                    // Shift down to normal priority\n                    priorityLevel = NormalPriority;\n                    break;\n                default:\n                    // Anything lower than normal priority should remain at the current level.\n                    priorityLevel = currentPriorityLevel;\n                    break;\n            }\n            var previousPriorityLevel = currentPriorityLevel;\n            currentPriorityLevel = priorityLevel;\n            try {\n                return eventHandler();\n            } finally{\n                currentPriorityLevel = previousPriorityLevel;\n            }\n        }\n        function unstable_wrapCallback(callback) {\n            var parentPriorityLevel = currentPriorityLevel;\n            return function() {\n                // This is a fork of runWithPriority, inlined for performance.\n                var previousPriorityLevel = currentPriorityLevel;\n                currentPriorityLevel = parentPriorityLevel;\n                try {\n                    return callback.apply(this, arguments);\n                } finally{\n                    currentPriorityLevel = previousPriorityLevel;\n                }\n            };\n        }\n        function unstable_scheduleCallback(priorityLevel, callback, options) {\n            var currentTime = exports.unstable_now();\n            var startTime;\n            if (typeof options === \"object\" && options !== null) {\n                var delay = options.delay;\n                if (typeof delay === \"number\" && delay > 0) {\n                    startTime = currentTime + delay;\n                } else {\n                    startTime = currentTime;\n                }\n            } else {\n                startTime = currentTime;\n            }\n            var timeout;\n            switch(priorityLevel){\n                case ImmediatePriority:\n                    timeout = IMMEDIATE_PRIORITY_TIMEOUT;\n                    break;\n                case UserBlockingPriority:\n                    timeout = USER_BLOCKING_PRIORITY_TIMEOUT;\n                    break;\n                case IdlePriority:\n                    timeout = IDLE_PRIORITY_TIMEOUT;\n                    break;\n                case LowPriority:\n                    timeout = LOW_PRIORITY_TIMEOUT;\n                    break;\n                case NormalPriority:\n                default:\n                    timeout = NORMAL_PRIORITY_TIMEOUT;\n                    break;\n            }\n            var expirationTime = startTime + timeout;\n            var newTask = {\n                id: taskIdCounter++,\n                callback: callback,\n                priorityLevel: priorityLevel,\n                startTime: startTime,\n                expirationTime: expirationTime,\n                sortIndex: -1\n            };\n            if (startTime > currentTime) {\n                // This is a delayed task.\n                newTask.sortIndex = startTime;\n                push(timerQueue, newTask);\n                if (peek(taskQueue) === null && newTask === peek(timerQueue)) {\n                    // All tasks are delayed, and this is the task with the earliest delay.\n                    if (isHostTimeoutScheduled) {\n                        // Cancel an existing timeout.\n                        cancelHostTimeout();\n                    } else {\n                        isHostTimeoutScheduled = true;\n                    } // Schedule a timeout.\n                    requestHostTimeout(handleTimeout, startTime - currentTime);\n                }\n            } else {\n                newTask.sortIndex = expirationTime;\n                push(taskQueue, newTask);\n                // wait until the next time we yield.\n                if (!isHostCallbackScheduled && !isPerformingWork) {\n                    isHostCallbackScheduled = true;\n                    requestHostCallback(flushWork);\n                }\n            }\n            return newTask;\n        }\n        function unstable_pauseExecution() {}\n        function unstable_continueExecution() {\n            if (!isHostCallbackScheduled && !isPerformingWork) {\n                isHostCallbackScheduled = true;\n                requestHostCallback(flushWork);\n            }\n        }\n        function unstable_getFirstCallbackNode() {\n            return peek(taskQueue);\n        }\n        function unstable_cancelCallback(task) {\n            // remove from the queue because you can't remove arbitrary nodes from an\n            // array based heap, only the first one.)\n            task.callback = null;\n        }\n        function unstable_getCurrentPriorityLevel() {\n            return currentPriorityLevel;\n        }\n        var isMessageLoopRunning = false;\n        var scheduledHostCallback = null;\n        var taskTimeoutID = -1; // Scheduler periodically yields in case there is other work on the main\n        // thread, like user events. By default, it yields multiple times per frame.\n        // It does not attempt to align with frame boundaries, since most tasks don't\n        // need to be frame aligned; for those that do, use requestAnimationFrame.\n        var frameInterval = frameYieldMs;\n        var startTime = -1;\n        function shouldYieldToHost() {\n            var timeElapsed = exports.unstable_now() - startTime;\n            if (timeElapsed < frameInterval) {\n                // The main thread has only been blocked for a really short amount of time;\n                // smaller than a single frame. Don't yield yet.\n                return false;\n            } // The main thread has been blocked for a non-negligible amount of time. We\n            return true;\n        }\n        function requestPaint() {}\n        function forceFrameRate(fps) {\n            if (fps < 0 || fps > 125) {\n                // Using console['error'] to evade Babel and ESLint\n                console[\"error\"](\"forceFrameRate takes a positive int between 0 and 125, \" + \"forcing frame rates higher than 125 fps is not supported\");\n                return;\n            }\n            if (fps > 0) {\n                frameInterval = Math.floor(1000 / fps);\n            } else {\n                // reset the framerate\n                frameInterval = frameYieldMs;\n            }\n        }\n        var performWorkUntilDeadline = function() {\n            if (scheduledHostCallback !== null) {\n                var currentTime = exports.unstable_now(); // Keep track of the start time so we can measure how long the main thread\n                // has been blocked.\n                startTime = currentTime;\n                var hasTimeRemaining = true; // If a scheduler task throws, exit the current browser task so the\n                // error can be observed.\n                //\n                // Intentionally not using a try-catch, since that makes some debugging\n                // techniques harder. Instead, if `scheduledHostCallback` errors, then\n                // `hasMoreWork` will remain true, and we'll continue the work loop.\n                var hasMoreWork = true;\n                try {\n                    hasMoreWork = scheduledHostCallback(hasTimeRemaining, currentTime);\n                } finally{\n                    if (hasMoreWork) {\n                        // If there's more work, schedule the next message event at the end\n                        // of the preceding one.\n                        schedulePerformWorkUntilDeadline();\n                    } else {\n                        isMessageLoopRunning = false;\n                        scheduledHostCallback = null;\n                    }\n                }\n            } else {\n                isMessageLoopRunning = false;\n            } // Yielding to the browser will give it a chance to paint, so we can\n        };\n        var schedulePerformWorkUntilDeadline;\n        if (typeof localSetImmediate === \"function\") {\n            // Node.js and old IE.\n            // There's a few reasons for why we prefer setImmediate.\n            //\n            // Unlike MessageChannel, it doesn't prevent a Node.js process from exiting.\n            // (Even though this is a DOM fork of the Scheduler, you could get here\n            // with a mix of Node.js 15+, which has a MessageChannel, and jsdom.)\n            // https://github.com/facebook/react/issues/20756\n            //\n            // But also, it runs earlier which is the semantic we want.\n            // If other browsers ever implement it, it's better to use it.\n            // Although both of these would be inferior to native scheduling.\n            schedulePerformWorkUntilDeadline = function() {\n                localSetImmediate(performWorkUntilDeadline);\n            };\n        } else if (typeof MessageChannel !== \"undefined\") {\n            // DOM and Worker environments.\n            // We prefer MessageChannel because of the 4ms setTimeout clamping.\n            var channel = new MessageChannel();\n            var port = channel.port2;\n            channel.port1.onmessage = performWorkUntilDeadline;\n            schedulePerformWorkUntilDeadline = function() {\n                port.postMessage(null);\n            };\n        } else {\n            // We should only fallback here in non-browser environments.\n            schedulePerformWorkUntilDeadline = function() {\n                localSetTimeout(performWorkUntilDeadline, 0);\n            };\n        }\n        function requestHostCallback(callback) {\n            scheduledHostCallback = callback;\n            if (!isMessageLoopRunning) {\n                isMessageLoopRunning = true;\n                schedulePerformWorkUntilDeadline();\n            }\n        }\n        function requestHostTimeout(callback, ms) {\n            taskTimeoutID = localSetTimeout(function() {\n                callback(exports.unstable_now());\n            }, ms);\n        }\n        function cancelHostTimeout() {\n            localClearTimeout(taskTimeoutID);\n            taskTimeoutID = -1;\n        }\n        var unstable_requestPaint = requestPaint;\n        var unstable_Profiling = null;\n        exports.unstable_IdlePriority = IdlePriority;\n        exports.unstable_ImmediatePriority = ImmediatePriority;\n        exports.unstable_LowPriority = LowPriority;\n        exports.unstable_NormalPriority = NormalPriority;\n        exports.unstable_Profiling = unstable_Profiling;\n        exports.unstable_UserBlockingPriority = UserBlockingPriority;\n        exports.unstable_cancelCallback = unstable_cancelCallback;\n        exports.unstable_continueExecution = unstable_continueExecution;\n        exports.unstable_forceFrameRate = forceFrameRate;\n        exports.unstable_getCurrentPriorityLevel = unstable_getCurrentPriorityLevel;\n        exports.unstable_getFirstCallbackNode = unstable_getFirstCallbackNode;\n        exports.unstable_next = unstable_next;\n        exports.unstable_pauseExecution = unstable_pauseExecution;\n        exports.unstable_requestPaint = unstable_requestPaint;\n        exports.unstable_runWithPriority = unstable_runWithPriority;\n        exports.unstable_scheduleCallback = unstable_scheduleCallback;\n        exports.unstable_shouldYield = shouldYieldToHost;\n        exports.unstable_wrapCallback = unstable_wrapCallback;\n        /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */ if (typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== \"undefined\" && typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop === \"function\") {\n            __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n        }\n    })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@react-three/fiber/node_modules/scheduler/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nif (false) {} else {\n    module.exports = __webpack_require__(/*! ./cjs/scheduler.development.js */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2ZpYmVyL25vZGVfbW9kdWxlcy9zY2hlZHVsZXIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxJQUFJQSxLQUF5QixFQUFjLEVBRTFDLE1BQU07SUFDTEMseUtBQXlCO0FBQzNCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmFqYS1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9AcmVhY3QtdGhyZWUvZmliZXIvbm9kZV9tb2R1bGVzL3NjaGVkdWxlci9pbmRleC5qcz82YmE5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9zY2hlZHVsZXIucHJvZHVjdGlvbi5taW4uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvc2NoZWR1bGVyLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOlsicHJvY2VzcyIsIm1vZHVsZSIsImV4cG9ydHMiLCJyZXF1aXJlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/node_modules/zustand/esm/index.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@react-three/fiber/node_modules/zustand/esm/index.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ create)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction createStore(createState) {\n    let state;\n    const listeners = /* @__PURE__ */ new Set();\n    const setState = (partial, replace)=>{\n        const nextState = typeof partial === \"function\" ? partial(state) : partial;\n        if (nextState !== state) {\n            const previousState = state;\n            state = replace ? nextState : Object.assign({}, state, nextState);\n            listeners.forEach((listener)=>listener(state, previousState));\n        }\n    };\n    const getState = ()=>state;\n    const subscribeWithSelector = (listener, selector = getState, equalityFn = Object.is)=>{\n        console.warn(\"[DEPRECATED] Please use `subscribeWithSelector` middleware\");\n        let currentSlice = selector(state);\n        function listenerToAdd() {\n            const nextSlice = selector(state);\n            if (!equalityFn(currentSlice, nextSlice)) {\n                const previousSlice = currentSlice;\n                listener(currentSlice = nextSlice, previousSlice);\n            }\n        }\n        listeners.add(listenerToAdd);\n        return ()=>listeners.delete(listenerToAdd);\n    };\n    const subscribe = (listener, selector, equalityFn)=>{\n        if (selector || equalityFn) {\n            return subscribeWithSelector(listener, selector, equalityFn);\n        }\n        listeners.add(listener);\n        return ()=>listeners.delete(listener);\n    };\n    const destroy = ()=>listeners.clear();\n    const api = {\n        setState,\n        getState,\n        subscribe,\n        destroy\n    };\n    state = createState(setState, getState, api);\n    return api;\n}\nconst isSSR =  true || 0;\nconst useIsomorphicLayoutEffect = isSSR ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\nfunction create(createState) {\n    const api = typeof createState === \"function\" ? createStore(createState) : createState;\n    const useStore = (selector = api.getState, equalityFn = Object.is)=>{\n        const [, forceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((c)=>c + 1, 0);\n        const state = api.getState();\n        const stateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(state);\n        const selectorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(selector);\n        const equalityFnRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(equalityFn);\n        const erroredRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n        const currentSliceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n        if (currentSliceRef.current === void 0) {\n            currentSliceRef.current = selector(state);\n        }\n        let newStateSlice;\n        let hasNewStateSlice = false;\n        if (stateRef.current !== state || selectorRef.current !== selector || equalityFnRef.current !== equalityFn || erroredRef.current) {\n            newStateSlice = selector(state);\n            hasNewStateSlice = !equalityFn(currentSliceRef.current, newStateSlice);\n        }\n        useIsomorphicLayoutEffect(()=>{\n            if (hasNewStateSlice) {\n                currentSliceRef.current = newStateSlice;\n            }\n            stateRef.current = state;\n            selectorRef.current = selector;\n            equalityFnRef.current = equalityFn;\n            erroredRef.current = false;\n        });\n        const stateBeforeSubscriptionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(state);\n        useIsomorphicLayoutEffect(()=>{\n            const listener = ()=>{\n                try {\n                    const nextState = api.getState();\n                    const nextStateSlice = selectorRef.current(nextState);\n                    if (!equalityFnRef.current(currentSliceRef.current, nextStateSlice)) {\n                        stateRef.current = nextState;\n                        currentSliceRef.current = nextStateSlice;\n                        forceUpdate();\n                    }\n                } catch (error) {\n                    erroredRef.current = true;\n                    forceUpdate();\n                }\n            };\n            const unsubscribe = api.subscribe(listener);\n            if (api.getState() !== stateBeforeSubscriptionRef.current) {\n                listener();\n            }\n            return unsubscribe;\n        }, []);\n        const sliceToReturn = hasNewStateSlice ? newStateSlice : currentSliceRef.current;\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(sliceToReturn);\n        return sliceToReturn;\n    };\n    Object.assign(useStore, api);\n    useStore[Symbol.iterator] = function() {\n        console.warn(\"[useStore, api] = create() is deprecated and will be removed in v4\");\n        const items = [\n            useStore,\n            api\n        ];\n        return {\n            next () {\n                const done = items.length <= 0;\n                return {\n                    value: items.shift(),\n                    done\n                };\n            }\n        };\n    };\n    return useStore;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2ZpYmVyL25vZGVfbW9kdWxlcy96dXN0YW5kL2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBc0Y7QUFFdEYsU0FBU0ssWUFBWUMsV0FBVztJQUM5QixJQUFJQztJQUNKLE1BQU1DLFlBQVksYUFBYSxHQUFHLElBQUlDO0lBQ3RDLE1BQU1DLFdBQVcsQ0FBQ0MsU0FBU0M7UUFDekIsTUFBTUMsWUFBWSxPQUFPRixZQUFZLGFBQWFBLFFBQVFKLFNBQVNJO1FBQ25FLElBQUlFLGNBQWNOLE9BQU87WUFDdkIsTUFBTU8sZ0JBQWdCUDtZQUN0QkEsUUFBUUssVUFBVUMsWUFBWUUsT0FBT0MsTUFBTSxDQUFDLENBQUMsR0FBR1QsT0FBT007WUFDdkRMLFVBQVVTLE9BQU8sQ0FBQyxDQUFDQyxXQUFhQSxTQUFTWCxPQUFPTztRQUNsRDtJQUNGO0lBQ0EsTUFBTUssV0FBVyxJQUFNWjtJQUN2QixNQUFNYSx3QkFBd0IsQ0FBQ0YsVUFBVUcsV0FBV0YsUUFBUSxFQUFFRyxhQUFhUCxPQUFPUSxFQUFFO1FBQ2xGQyxRQUFRQyxJQUFJLENBQUM7UUFDYixJQUFJQyxlQUFlTCxTQUFTZDtRQUM1QixTQUFTb0I7WUFDUCxNQUFNQyxZQUFZUCxTQUFTZDtZQUMzQixJQUFJLENBQUNlLFdBQVdJLGNBQWNFLFlBQVk7Z0JBQ3hDLE1BQU1DLGdCQUFnQkg7Z0JBQ3RCUixTQUFTUSxlQUFlRSxXQUFXQztZQUNyQztRQUNGO1FBQ0FyQixVQUFVc0IsR0FBRyxDQUFDSDtRQUNkLE9BQU8sSUFBTW5CLFVBQVV1QixNQUFNLENBQUNKO0lBQ2hDO0lBQ0EsTUFBTUssWUFBWSxDQUFDZCxVQUFVRyxVQUFVQztRQUNyQyxJQUFJRCxZQUFZQyxZQUFZO1lBQzFCLE9BQU9GLHNCQUFzQkYsVUFBVUcsVUFBVUM7UUFDbkQ7UUFDQWQsVUFBVXNCLEdBQUcsQ0FBQ1o7UUFDZCxPQUFPLElBQU1WLFVBQVV1QixNQUFNLENBQUNiO0lBQ2hDO0lBQ0EsTUFBTWUsVUFBVSxJQUFNekIsVUFBVTBCLEtBQUs7SUFDckMsTUFBTUMsTUFBTTtRQUFFekI7UUFBVVM7UUFBVWE7UUFBV0M7SUFBUTtJQUNyRDFCLFFBQVFELFlBQVlJLFVBQVVTLFVBQVVnQjtJQUN4QyxPQUFPQTtBQUNUO0FBRUEsTUFBTUMsUUFBUSxLQUFrRCxJQUFJLENBQThEO0FBQ2xJLE1BQU1LLDRCQUE0QkwsUUFBUWpDLDRDQUFTQSxHQUFHQyxrREFBZUE7QUFDckUsU0FBU3NDLE9BQU9wQyxXQUFXO0lBQ3pCLE1BQU02QixNQUFNLE9BQU83QixnQkFBZ0IsYUFBYUQsWUFBWUMsZUFBZUE7SUFDM0UsTUFBTXFDLFdBQVcsQ0FBQ3RCLFdBQVdjLElBQUloQixRQUFRLEVBQUVHLGFBQWFQLE9BQU9RLEVBQUU7UUFDL0QsTUFBTSxHQUFHcUIsWUFBWSxHQUFHNUMsaURBQVVBLENBQUMsQ0FBQzZDLElBQU1BLElBQUksR0FBRztRQUNqRCxNQUFNdEMsUUFBUTRCLElBQUloQixRQUFRO1FBQzFCLE1BQU0yQixXQUFXN0MsNkNBQU1BLENBQUNNO1FBQ3hCLE1BQU13QyxjQUFjOUMsNkNBQU1BLENBQUNvQjtRQUMzQixNQUFNMkIsZ0JBQWdCL0MsNkNBQU1BLENBQUNxQjtRQUM3QixNQUFNMkIsYUFBYWhELDZDQUFNQSxDQUFDO1FBQzFCLE1BQU1pRCxrQkFBa0JqRCw2Q0FBTUE7UUFDOUIsSUFBSWlELGdCQUFnQkMsT0FBTyxLQUFLLEtBQUssR0FBRztZQUN0Q0QsZ0JBQWdCQyxPQUFPLEdBQUc5QixTQUFTZDtRQUNyQztRQUNBLElBQUk2QztRQUNKLElBQUlDLG1CQUFtQjtRQUN2QixJQUFJUCxTQUFTSyxPQUFPLEtBQUs1QyxTQUFTd0MsWUFBWUksT0FBTyxLQUFLOUIsWUFBWTJCLGNBQWNHLE9BQU8sS0FBSzdCLGNBQWMyQixXQUFXRSxPQUFPLEVBQUU7WUFDaElDLGdCQUFnQi9CLFNBQVNkO1lBQ3pCOEMsbUJBQW1CLENBQUMvQixXQUFXNEIsZ0JBQWdCQyxPQUFPLEVBQUVDO1FBQzFEO1FBQ0FYLDBCQUEwQjtZQUN4QixJQUFJWSxrQkFBa0I7Z0JBQ3BCSCxnQkFBZ0JDLE9BQU8sR0FBR0M7WUFDNUI7WUFDQU4sU0FBU0ssT0FBTyxHQUFHNUM7WUFDbkJ3QyxZQUFZSSxPQUFPLEdBQUc5QjtZQUN0QjJCLGNBQWNHLE9BQU8sR0FBRzdCO1lBQ3hCMkIsV0FBV0UsT0FBTyxHQUFHO1FBQ3ZCO1FBQ0EsTUFBTUcsNkJBQTZCckQsNkNBQU1BLENBQUNNO1FBQzFDa0MsMEJBQTBCO1lBQ3hCLE1BQU12QixXQUFXO2dCQUNmLElBQUk7b0JBQ0YsTUFBTUwsWUFBWXNCLElBQUloQixRQUFRO29CQUM5QixNQUFNb0MsaUJBQWlCUixZQUFZSSxPQUFPLENBQUN0QztvQkFDM0MsSUFBSSxDQUFDbUMsY0FBY0csT0FBTyxDQUFDRCxnQkFBZ0JDLE9BQU8sRUFBRUksaUJBQWlCO3dCQUNuRVQsU0FBU0ssT0FBTyxHQUFHdEM7d0JBQ25CcUMsZ0JBQWdCQyxPQUFPLEdBQUdJO3dCQUMxQlg7b0JBQ0Y7Z0JBQ0YsRUFBRSxPQUFPWSxPQUFPO29CQUNkUCxXQUFXRSxPQUFPLEdBQUc7b0JBQ3JCUDtnQkFDRjtZQUNGO1lBQ0EsTUFBTWEsY0FBY3RCLElBQUlILFNBQVMsQ0FBQ2Q7WUFDbEMsSUFBSWlCLElBQUloQixRQUFRLE9BQU9tQywyQkFBMkJILE9BQU8sRUFBRTtnQkFDekRqQztZQUNGO1lBQ0EsT0FBT3VDO1FBQ1QsR0FBRyxFQUFFO1FBQ0wsTUFBTUMsZ0JBQWdCTCxtQkFBbUJELGdCQUFnQkYsZ0JBQWdCQyxPQUFPO1FBQ2hGakQsb0RBQWFBLENBQUN3RDtRQUNkLE9BQU9BO0lBQ1Q7SUFDQTNDLE9BQU9DLE1BQU0sQ0FBQzJCLFVBQVVSO0lBQ3hCUSxRQUFRLENBQUNnQixPQUFPQyxRQUFRLENBQUMsR0FBRztRQUMxQnBDLFFBQVFDLElBQUksQ0FBQztRQUNiLE1BQU1vQyxRQUFRO1lBQUNsQjtZQUFVUjtTQUFJO1FBQzdCLE9BQU87WUFDTDJCO2dCQUNFLE1BQU1DLE9BQU9GLE1BQU1HLE1BQU0sSUFBSTtnQkFDN0IsT0FBTztvQkFBRUMsT0FBT0osTUFBTUssS0FBSztvQkFBSUg7Z0JBQUs7WUFDdEM7UUFDRjtJQUNGO0lBQ0EsT0FBT3BCO0FBQ1Q7QUFFNkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9iYWphLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL0ByZWFjdC10aHJlZS9maWJlci9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vaW5kZXguanM/MzMwMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSZWR1Y2VyLCB1c2VSZWYsIHVzZURlYnVnVmFsdWUsIHVzZUVmZmVjdCwgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5mdW5jdGlvbiBjcmVhdGVTdG9yZShjcmVhdGVTdGF0ZSkge1xuICBsZXQgc3RhdGU7XG4gIGNvbnN0IGxpc3RlbmVycyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgU2V0KCk7XG4gIGNvbnN0IHNldFN0YXRlID0gKHBhcnRpYWwsIHJlcGxhY2UpID0+IHtcbiAgICBjb25zdCBuZXh0U3RhdGUgPSB0eXBlb2YgcGFydGlhbCA9PT0gXCJmdW5jdGlvblwiID8gcGFydGlhbChzdGF0ZSkgOiBwYXJ0aWFsO1xuICAgIGlmIChuZXh0U3RhdGUgIT09IHN0YXRlKSB7XG4gICAgICBjb25zdCBwcmV2aW91c1N0YXRlID0gc3RhdGU7XG4gICAgICBzdGF0ZSA9IHJlcGxhY2UgPyBuZXh0U3RhdGUgOiBPYmplY3QuYXNzaWduKHt9LCBzdGF0ZSwgbmV4dFN0YXRlKTtcbiAgICAgIGxpc3RlbmVycy5mb3JFYWNoKChsaXN0ZW5lcikgPT4gbGlzdGVuZXIoc3RhdGUsIHByZXZpb3VzU3RhdGUpKTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IGdldFN0YXRlID0gKCkgPT4gc3RhdGU7XG4gIGNvbnN0IHN1YnNjcmliZVdpdGhTZWxlY3RvciA9IChsaXN0ZW5lciwgc2VsZWN0b3IgPSBnZXRTdGF0ZSwgZXF1YWxpdHlGbiA9IE9iamVjdC5pcykgPT4ge1xuICAgIGNvbnNvbGUud2FybihcIltERVBSRUNBVEVEXSBQbGVhc2UgdXNlIGBzdWJzY3JpYmVXaXRoU2VsZWN0b3JgIG1pZGRsZXdhcmVcIik7XG4gICAgbGV0IGN1cnJlbnRTbGljZSA9IHNlbGVjdG9yKHN0YXRlKTtcbiAgICBmdW5jdGlvbiBsaXN0ZW5lclRvQWRkKCkge1xuICAgICAgY29uc3QgbmV4dFNsaWNlID0gc2VsZWN0b3Ioc3RhdGUpO1xuICAgICAgaWYgKCFlcXVhbGl0eUZuKGN1cnJlbnRTbGljZSwgbmV4dFNsaWNlKSkge1xuICAgICAgICBjb25zdCBwcmV2aW91c1NsaWNlID0gY3VycmVudFNsaWNlO1xuICAgICAgICBsaXN0ZW5lcihjdXJyZW50U2xpY2UgPSBuZXh0U2xpY2UsIHByZXZpb3VzU2xpY2UpO1xuICAgICAgfVxuICAgIH1cbiAgICBsaXN0ZW5lcnMuYWRkKGxpc3RlbmVyVG9BZGQpO1xuICAgIHJldHVybiAoKSA9PiBsaXN0ZW5lcnMuZGVsZXRlKGxpc3RlbmVyVG9BZGQpO1xuICB9O1xuICBjb25zdCBzdWJzY3JpYmUgPSAobGlzdGVuZXIsIHNlbGVjdG9yLCBlcXVhbGl0eUZuKSA9PiB7XG4gICAgaWYgKHNlbGVjdG9yIHx8IGVxdWFsaXR5Rm4pIHtcbiAgICAgIHJldHVybiBzdWJzY3JpYmVXaXRoU2VsZWN0b3IobGlzdGVuZXIsIHNlbGVjdG9yLCBlcXVhbGl0eUZuKTtcbiAgICB9XG4gICAgbGlzdGVuZXJzLmFkZChsaXN0ZW5lcik7XG4gICAgcmV0dXJuICgpID0+IGxpc3RlbmVycy5kZWxldGUobGlzdGVuZXIpO1xuICB9O1xuICBjb25zdCBkZXN0cm95ID0gKCkgPT4gbGlzdGVuZXJzLmNsZWFyKCk7XG4gIGNvbnN0IGFwaSA9IHsgc2V0U3RhdGUsIGdldFN0YXRlLCBzdWJzY3JpYmUsIGRlc3Ryb3kgfTtcbiAgc3RhdGUgPSBjcmVhdGVTdGF0ZShzZXRTdGF0ZSwgZ2V0U3RhdGUsIGFwaSk7XG4gIHJldHVybiBhcGk7XG59XG5cbmNvbnN0IGlzU1NSID0gdHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIiB8fCAhd2luZG93Lm5hdmlnYXRvciB8fCAvU2VydmVyU2lkZVJlbmRlcmluZ3xeRGVub1xcLy8udGVzdCh3aW5kb3cubmF2aWdhdG9yLnVzZXJBZ2VudCk7XG5jb25zdCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gaXNTU1IgPyB1c2VFZmZlY3QgOiB1c2VMYXlvdXRFZmZlY3Q7XG5mdW5jdGlvbiBjcmVhdGUoY3JlYXRlU3RhdGUpIHtcbiAgY29uc3QgYXBpID0gdHlwZW9mIGNyZWF0ZVN0YXRlID09PSBcImZ1bmN0aW9uXCIgPyBjcmVhdGVTdG9yZShjcmVhdGVTdGF0ZSkgOiBjcmVhdGVTdGF0ZTtcbiAgY29uc3QgdXNlU3RvcmUgPSAoc2VsZWN0b3IgPSBhcGkuZ2V0U3RhdGUsIGVxdWFsaXR5Rm4gPSBPYmplY3QuaXMpID0+IHtcbiAgICBjb25zdCBbLCBmb3JjZVVwZGF0ZV0gPSB1c2VSZWR1Y2VyKChjKSA9PiBjICsgMSwgMCk7XG4gICAgY29uc3Qgc3RhdGUgPSBhcGkuZ2V0U3RhdGUoKTtcbiAgICBjb25zdCBzdGF0ZVJlZiA9IHVzZVJlZihzdGF0ZSk7XG4gICAgY29uc3Qgc2VsZWN0b3JSZWYgPSB1c2VSZWYoc2VsZWN0b3IpO1xuICAgIGNvbnN0IGVxdWFsaXR5Rm5SZWYgPSB1c2VSZWYoZXF1YWxpdHlGbik7XG4gICAgY29uc3QgZXJyb3JlZFJlZiA9IHVzZVJlZihmYWxzZSk7XG4gICAgY29uc3QgY3VycmVudFNsaWNlUmVmID0gdXNlUmVmKCk7XG4gICAgaWYgKGN1cnJlbnRTbGljZVJlZi5jdXJyZW50ID09PSB2b2lkIDApIHtcbiAgICAgIGN1cnJlbnRTbGljZVJlZi5jdXJyZW50ID0gc2VsZWN0b3Ioc3RhdGUpO1xuICAgIH1cbiAgICBsZXQgbmV3U3RhdGVTbGljZTtcbiAgICBsZXQgaGFzTmV3U3RhdGVTbGljZSA9IGZhbHNlO1xuICAgIGlmIChzdGF0ZVJlZi5jdXJyZW50ICE9PSBzdGF0ZSB8fCBzZWxlY3RvclJlZi5jdXJyZW50ICE9PSBzZWxlY3RvciB8fCBlcXVhbGl0eUZuUmVmLmN1cnJlbnQgIT09IGVxdWFsaXR5Rm4gfHwgZXJyb3JlZFJlZi5jdXJyZW50KSB7XG4gICAgICBuZXdTdGF0ZVNsaWNlID0gc2VsZWN0b3Ioc3RhdGUpO1xuICAgICAgaGFzTmV3U3RhdGVTbGljZSA9ICFlcXVhbGl0eUZuKGN1cnJlbnRTbGljZVJlZi5jdXJyZW50LCBuZXdTdGF0ZVNsaWNlKTtcbiAgICB9XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICBpZiAoaGFzTmV3U3RhdGVTbGljZSkge1xuICAgICAgICBjdXJyZW50U2xpY2VSZWYuY3VycmVudCA9IG5ld1N0YXRlU2xpY2U7XG4gICAgICB9XG4gICAgICBzdGF0ZVJlZi5jdXJyZW50ID0gc3RhdGU7XG4gICAgICBzZWxlY3RvclJlZi5jdXJyZW50ID0gc2VsZWN0b3I7XG4gICAgICBlcXVhbGl0eUZuUmVmLmN1cnJlbnQgPSBlcXVhbGl0eUZuO1xuICAgICAgZXJyb3JlZFJlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgfSk7XG4gICAgY29uc3Qgc3RhdGVCZWZvcmVTdWJzY3JpcHRpb25SZWYgPSB1c2VSZWYoc3RhdGUpO1xuICAgIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgICAgY29uc3QgbGlzdGVuZXIgPSAoKSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgbmV4dFN0YXRlID0gYXBpLmdldFN0YXRlKCk7XG4gICAgICAgICAgY29uc3QgbmV4dFN0YXRlU2xpY2UgPSBzZWxlY3RvclJlZi5jdXJyZW50KG5leHRTdGF0ZSk7XG4gICAgICAgICAgaWYgKCFlcXVhbGl0eUZuUmVmLmN1cnJlbnQoY3VycmVudFNsaWNlUmVmLmN1cnJlbnQsIG5leHRTdGF0ZVNsaWNlKSkge1xuICAgICAgICAgICAgc3RhdGVSZWYuY3VycmVudCA9IG5leHRTdGF0ZTtcbiAgICAgICAgICAgIGN1cnJlbnRTbGljZVJlZi5jdXJyZW50ID0gbmV4dFN0YXRlU2xpY2U7XG4gICAgICAgICAgICBmb3JjZVVwZGF0ZSgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBlcnJvcmVkUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgICAgIGZvcmNlVXBkYXRlKCk7XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgICBjb25zdCB1bnN1YnNjcmliZSA9IGFwaS5zdWJzY3JpYmUobGlzdGVuZXIpO1xuICAgICAgaWYgKGFwaS5nZXRTdGF0ZSgpICE9PSBzdGF0ZUJlZm9yZVN1YnNjcmlwdGlvblJlZi5jdXJyZW50KSB7XG4gICAgICAgIGxpc3RlbmVyKCk7XG4gICAgICB9XG4gICAgICByZXR1cm4gdW5zdWJzY3JpYmU7XG4gICAgfSwgW10pO1xuICAgIGNvbnN0IHNsaWNlVG9SZXR1cm4gPSBoYXNOZXdTdGF0ZVNsaWNlID8gbmV3U3RhdGVTbGljZSA6IGN1cnJlbnRTbGljZVJlZi5jdXJyZW50O1xuICAgIHVzZURlYnVnVmFsdWUoc2xpY2VUb1JldHVybik7XG4gICAgcmV0dXJuIHNsaWNlVG9SZXR1cm47XG4gIH07XG4gIE9iamVjdC5hc3NpZ24odXNlU3RvcmUsIGFwaSk7XG4gIHVzZVN0b3JlW1N5bWJvbC5pdGVyYXRvcl0gPSBmdW5jdGlvbigpIHtcbiAgICBjb25zb2xlLndhcm4oXCJbdXNlU3RvcmUsIGFwaV0gPSBjcmVhdGUoKSBpcyBkZXByZWNhdGVkIGFuZCB3aWxsIGJlIHJlbW92ZWQgaW4gdjRcIik7XG4gICAgY29uc3QgaXRlbXMgPSBbdXNlU3RvcmUsIGFwaV07XG4gICAgcmV0dXJuIHtcbiAgICAgIG5leHQoKSB7XG4gICAgICAgIGNvbnN0IGRvbmUgPSBpdGVtcy5sZW5ndGggPD0gMDtcbiAgICAgICAgcmV0dXJuIHsgdmFsdWU6IGl0ZW1zLnNoaWZ0KCksIGRvbmUgfTtcbiAgICAgIH1cbiAgICB9O1xuICB9O1xuICByZXR1cm4gdXNlU3RvcmU7XG59XG5cbmV4cG9ydCB7IGNyZWF0ZSBhcyBkZWZhdWx0IH07XG4iXSwibmFtZXMiOlsidXNlUmVkdWNlciIsInVzZVJlZiIsInVzZURlYnVnVmFsdWUiLCJ1c2VFZmZlY3QiLCJ1c2VMYXlvdXRFZmZlY3QiLCJjcmVhdGVTdG9yZSIsImNyZWF0ZVN0YXRlIiwic3RhdGUiLCJsaXN0ZW5lcnMiLCJTZXQiLCJzZXRTdGF0ZSIsInBhcnRpYWwiLCJyZXBsYWNlIiwibmV4dFN0YXRlIiwicHJldmlvdXNTdGF0ZSIsIk9iamVjdCIsImFzc2lnbiIsImZvckVhY2giLCJsaXN0ZW5lciIsImdldFN0YXRlIiwic3Vic2NyaWJlV2l0aFNlbGVjdG9yIiwic2VsZWN0b3IiLCJlcXVhbGl0eUZuIiwiaXMiLCJjb25zb2xlIiwid2FybiIsImN1cnJlbnRTbGljZSIsImxpc3RlbmVyVG9BZGQiLCJuZXh0U2xpY2UiLCJwcmV2aW91c1NsaWNlIiwiYWRkIiwiZGVsZXRlIiwic3Vic2NyaWJlIiwiZGVzdHJveSIsImNsZWFyIiwiYXBpIiwiaXNTU1IiLCJ3aW5kb3ciLCJuYXZpZ2F0b3IiLCJ0ZXN0IiwidXNlckFnZW50IiwidXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCIsImNyZWF0ZSIsInVzZVN0b3JlIiwiZm9yY2VVcGRhdGUiLCJjIiwic3RhdGVSZWYiLCJzZWxlY3RvclJlZiIsImVxdWFsaXR5Rm5SZWYiLCJlcnJvcmVkUmVmIiwiY3VycmVudFNsaWNlUmVmIiwiY3VycmVudCIsIm5ld1N0YXRlU2xpY2UiLCJoYXNOZXdTdGF0ZVNsaWNlIiwic3RhdGVCZWZvcmVTdWJzY3JpcHRpb25SZWYiLCJuZXh0U3RhdGVTbGljZSIsImVycm9yIiwidW5zdWJzY3JpYmUiLCJzbGljZVRvUmV0dXJuIiwiU3ltYm9sIiwiaXRlcmF0b3IiLCJpdGVtcyIsIm5leHQiLCJkb25lIiwibGVuZ3RoIiwidmFsdWUiLCJzaGlmdCIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/node_modules/zustand/esm/index.js\n");

/***/ })

};
;